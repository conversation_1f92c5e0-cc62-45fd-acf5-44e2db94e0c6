import React, { useState, useEffect } from 'react';
import {
  Brain,
  TrendingUp,
  AlertTriangle,
  Package,
  Clock,
  RefreshCw,
  Settings,
  Play,
  Info
} from 'lucide-react';
import { predictiveAPI } from '../../services/api';
import DemandForecast<PERSON><PERSON> from './DemandForecastChart';
import ReorderSuggestions from './ReorderSuggestions';
import VelocityAnalytics from './VelocityAnalytics';
import toast from 'react-hot-toast';

const PredictiveDashboard = () => {
  const [summary, setSummary] = useState(null);
  const [comprehensiveAnalysis, setComprehensiveAnalysis] = useState(null);
  const [analysisMessage, setAnalysisMessage] = useState('');
  const [analysisSuggestions, setAnalysisSuggestions] = useState(null);
  const [seasonalData, setSeasonalData] = useState(null);
  const [supplierRecommendations, setSupplierRecommendations] = useState(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedSeason, setSelectedSeason] = useState('current');

  useEffect(() => {
    fetchDashboardSummary();
    fetchComprehensiveAnalysis();
    fetchSeasonalData();
    fetchSupplierRecommendations();
  }, []);

  const fetchDashboardSummary = async () => {
    try {
      setLoading(true);
      const response = await predictiveAPI.getDashboardSummary();
      setSummary(response.data);
    } catch (error) {
      console.error('Error fetching dashboard summary:', error);
      toast.error('Failed to load predictive analytics summary');
    } finally {
      setLoading(false);
    }
  };

  const fetchComprehensiveAnalysis = async () => {
    try {
      const response = await predictiveAPI.getComprehensiveDashboard({ season: selectedSeason });
      setComprehensiveAnalysis(response.data.analysis);
      setAnalysisMessage(response.data.message || '');
      setAnalysisSuggestions(response.data.suggestions || null);
    } catch (error) {
      console.error('Error fetching comprehensive analysis:', error);
      setAnalysisMessage('Failed to load comprehensive analysis. Please try again.');
    }
  };

  const fetchSeasonalData = async () => {
    try {
      const response = await predictiveAPI.getSeasonalAnalysis();
      setSeasonalData(response.data);
    } catch (error) {
      console.error('Error fetching seasonal data:', error);
    }
  };

  const fetchSupplierRecommendations = async () => {
    try {
      const response = await predictiveAPI.getSupplierRecommendations();
      setSupplierRecommendations(response.data);
    } catch (error) {
      console.error('Error fetching supplier recommendations:', error);
    }
  };

  const handleGenerateForecasts = async () => {
    try {
      setGenerating(true);
      toast.loading('Generating comprehensive demand forecasts...', { id: 'forecast-generation' });

      const response = await predictiveAPI.generateForecasts({
        forecastDays: 30,
        season: selectedSeason !== 'current' ? selectedSeason : null
      });

      toast.success(`Generated comprehensive analysis for ${response.data.forecasts.length} products`, {
        id: 'forecast-generation'
      });

      // Refresh all data after generation
      await Promise.all([
        fetchDashboardSummary(),
        fetchComprehensiveAnalysis(),
        fetchSupplierRecommendations()
      ]);
    } catch (error) {
      console.error('Error generating forecasts:', error);
      toast.error('Failed to generate forecasts', { id: 'forecast-generation' });
    } finally {
      setGenerating(false);
    }
  };

  const handleGenerateReorderSuggestions = async () => {
    try {
      setGenerating(true);
      toast.loading('Generating reorder suggestions...', { id: 'reorder-generation' });
      
      const response = await predictiveAPI.generateReorderSuggestions();
      
      toast.success(`Generated ${response.data.suggestions.length} reorder suggestions`, {
        id: 'reorder-generation'
      });
      
      // Refresh summary after generation
      await fetchDashboardSummary();
    } catch (error) {
      console.error('Error generating reorder suggestions:', error);
      toast.error('Failed to generate reorder suggestions', { id: 'reorder-generation' });
    } finally {
      setGenerating(false);
    }
  };

  const StatCard = ({ title, value, icon: Icon, color, subtitle, trend }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
      {trend && (
        <div className="mt-4 flex items-center">
          <span className="text-sm text-gray-500">{trend}</span>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Brain className="h-8 w-8 mr-3 text-blue-600" />
            Predictive Demand Engine
          </h1>
          <p className="text-gray-600 mt-1">
            AI-powered demand forecasting and inventory optimization
          </p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={handleGenerateForecasts}
            disabled={generating}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {generating ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Generate Forecasts
          </button>
          
          <button
            onClick={handleGenerateReorderSuggestions}
            disabled={generating}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Package className="h-4 w-4 mr-2" />
            Generate Suggestions
          </button>
          
          <button
            onClick={fetchDashboardSummary}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <StatCard
            title="Active Forecasts"
            value={summary.forecasts?.active || 0}
            icon={TrendingUp}
            color="bg-blue-100 text-blue-600"
            subtitle={`${summary.forecasts?.total || 0} total forecasts`}
          />
          <StatCard
            title="Pending Suggestions"
            value={summary.reorder_suggestions?.pending || 0}
            icon={Package}
            color="bg-green-100 text-green-600"
            subtitle="Reorder recommendations"
          />
          <StatCard
            title="High Priority Alerts"
            value={summary.reorder_suggestions?.high_priority || 0}
            icon={AlertTriangle}
            color="bg-orange-100 text-orange-600"
            subtitle="Urgent reorder needed"
          />
          <StatCard
            title="Critical Alerts"
            value={summary.reorder_suggestions?.critical || 0}
            icon={Clock}
            color="bg-red-100 text-red-600"
            subtitle="Immediate attention required"
          />
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: Brain },
            { id: 'forecasts', name: 'Demand Forecasts', icon: TrendingUp },
            { id: 'suggestions', name: 'Reorder Suggestions', icon: Package },
            { id: 'velocity', name: 'Velocity Analytics', icon: TrendingUp }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ReorderSuggestions limit={5} showActions={false} />
            <VelocityAnalytics days={30} />
          </div>
        )}

        {activeTab === 'forecasts' && (
          <div className="space-y-6">
            {/* Forecast Generation Section */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Comprehensive Demand Forecasting
              </h3>
              <p className="text-gray-600 mb-4">
                Generate AI-powered demand forecasts with comprehensive analysis including low stock alerts, seasonal trends, and supplier recommendations.
              </p>

              <div className="flex items-center space-x-4 mb-4">
                <label className="text-sm font-medium text-gray-700">Season Analysis:</label>
                <select
                  value={selectedSeason}
                  onChange={(e) => setSelectedSeason(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                >
                  <option value="current">Current Season</option>
                  <option value="peak">Peak Season</option>
                  <option value="low">Low Season</option>
                </select>
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={handleGenerateForecasts}
                  disabled={generating}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {generating ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Generate Comprehensive Analysis
                </button>
              </div>
            </div>

            {/* Comprehensive Analysis Results */}
            {comprehensiveAnalysis && (
              <div className="space-y-6">
                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="text-xs font-medium text-blue-600 uppercase tracking-wide">Products Analyzed</div>
                    <div className="text-2xl font-bold text-blue-900">{comprehensiveAnalysis.summary.total_products_analyzed}</div>
                  </div>
                  <div className="bg-red-50 rounded-lg p-4">
                    <div className="text-xs font-medium text-red-600 uppercase tracking-wide">Low Stock Alerts</div>
                    <div className="text-2xl font-bold text-red-900">{comprehensiveAnalysis.summary.low_stock_alerts}</div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="text-xs font-medium text-green-600 uppercase tracking-wide">High Demand Products</div>
                    <div className="text-2xl font-bold text-green-900">{comprehensiveAnalysis.summary.high_demand_products}</div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="text-xs font-medium text-purple-600 uppercase tracking-wide">Seasonal Recommendations</div>
                    <div className="text-2xl font-bold text-purple-900">{comprehensiveAnalysis.summary.seasonal_recommendations}</div>
                  </div>
                  <div className="bg-orange-50 rounded-lg p-4">
                    <div className="text-xs font-medium text-orange-600 uppercase tracking-wide">Suppliers Involved</div>
                    <div className="text-2xl font-bold text-orange-900">{comprehensiveAnalysis.summary.suppliers_involved}</div>
                  </div>
                </div>

                {/* Low Stock Products */}
                {comprehensiveAnalysis.low_stock_products.length > 0 && (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
                      Critical Low Stock Alerts
                    </h4>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Stock</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Left</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Daily Demand</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Urgency</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {comprehensiveAnalysis.low_stock_products.slice(0, 10).map((product, index) => (
                            <tr key={index}>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">{product.name}</div>
                                <div className="text-sm text-gray-500">{product.sku}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.current_stock}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.days_of_stock}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.avg_daily_demand}</td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  product.urgency === 'Critical' ? 'bg-red-100 text-red-800' :
                                  product.urgency === 'High' ? 'bg-orange-100 text-orange-800' :
                                  'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {product.urgency}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {product.supplier ? product.supplier.name : 'No supplier'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* High Demand Products */}
                {comprehensiveAnalysis.high_demand_products.length > 0 && (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                      High Demand Products
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {comprehensiveAnalysis.high_demand_products.slice(0, 6).map((product, index) => (
                        <div key={index} className="bg-green-50 rounded-lg p-4">
                          <div className="text-sm font-medium text-gray-900">{product.name}</div>
                          <div className="text-xs text-gray-500 mb-2">{product.category}</div>
                          <div className="text-lg font-bold text-green-900">{product.avg_daily_demand} units/day</div>
                          <div className="text-sm text-green-700">Total: {product.total_predicted_demand} units</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* No Analysis Available - Show Guidance */}
            {!comprehensiveAnalysis && !generating && (
              <div className="space-y-6">
                {/* Message from Backend */}
                {analysisMessage && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <Info className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
                      <p className="text-yellow-800">{analysisMessage}</p>
                    </div>
                  </div>
                )}

                {/* Getting Started Guide */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-start">
                    <Info className="h-6 w-6 text-blue-600 mt-1 mr-3" />
                    <div>
                      <h3 className="text-lg font-semibold text-blue-900 mb-2">
                        Get Started with Comprehensive Demand Analysis
                      </h3>
                      <p className="text-blue-800 mb-4">
                        Generate AI-powered demand forecasts to unlock powerful insights about your inventory.
                        Here's what you'll get when you click "Generate Comprehensive Analysis":
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {analysisSuggestions?.benefits ? (
                          analysisSuggestions.benefits.map((benefit, index) => (
                            <div key={index} className="bg-white rounded-lg p-4 border border-blue-200">
                              <div className="text-sm text-blue-800">• {benefit}</div>
                            </div>
                          ))
                        ) : (
                          <>
                            <div className="bg-white rounded-lg p-4 border border-blue-200">
                              <h4 className="font-semibold text-blue-900 mb-2">📊 Demand Forecasting</h4>
                              <ul className="text-sm text-blue-800 space-y-1">
                                <li>• 30-day demand predictions for all products</li>
                                <li>• Prophet ML model with seasonal patterns</li>
                                <li>• Historical trend analysis</li>
                              </ul>
                            </div>
                            <div className="bg-white rounded-lg p-4 border border-blue-200">
                              <h4 className="font-semibold text-blue-900 mb-2">🚨 Smart Alerts</h4>
                              <ul className="text-sm text-blue-800 space-y-1">
                                <li>• Low stock warnings with urgency levels</li>
                                <li>• High demand product identification</li>
                                <li>• Days of stock remaining calculations</li>
                              </ul>
                            </div>
                            <div className="bg-white rounded-lg p-4 border border-blue-200">
                              <h4 className="font-semibold text-blue-900 mb-2">🏪 Supplier Insights</h4>
                              <ul className="text-sm text-blue-800 space-y-1">
                                <li>• Supplier performance analysis</li>
                                <li>• Reorder recommendations by supplier</li>
                                <li>• Total value and quantity suggestions</li>
                              </ul>
                            </div>
                            <div className="bg-white rounded-lg p-4 border border-blue-200">
                              <h4 className="font-semibold text-blue-900 mb-2">📈 Seasonal Analysis</h4>
                              <ul className="text-sm text-blue-800 space-y-1">
                                <li>• Seasonal demand patterns</li>
                                <li>• Holiday and weather impact</li>
                                <li>• Market basket analysis integration</li>
                              </ul>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Sample Analysis Preview */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                    Sample Analysis Preview
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Here's an example of the comprehensive analysis tables you'll see:
                  </p>

                  {/* Sample Low Stock Table */}
                  <div className="mb-6">
                    <h4 className="text-md font-semibold text-gray-800 mb-3 flex items-center">
                      <AlertTriangle className="h-4 w-4 mr-2 text-red-600" />
                      Critical Low Stock Alerts (Sample)
                    </h4>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Stock</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Daily Demand</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Left</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Urgency</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">Organic Bananas</div>
                              <div className="text-sm text-gray-500">SKU-001</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">45 units</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12.3 units/day</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3.7 days</td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                Critical
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">Whole Milk</div>
                              <div className="text-sm text-gray-500">SKU-002</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">78 units</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8.9 units/day</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8.8 days</td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                Medium
                              </span>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* Sample High Demand Products */}
                  <div className="mb-6">
                    <h4 className="text-md font-semibold text-gray-800 mb-3 flex items-center">
                      <TrendingUp className="h-4 w-4 mr-2 text-green-600" />
                      High Demand Products (Sample)
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                        <div className="text-sm font-medium text-gray-900">Fresh Strawberries</div>
                        <div className="text-xs text-gray-500 mb-2">Produce</div>
                        <div className="text-lg font-bold text-green-900">24.5 units/day</div>
                        <div className="text-sm text-green-700">Predicted: 735 units/month</div>
                      </div>
                      <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                        <div className="text-sm font-medium text-gray-900">Greek Yogurt</div>
                        <div className="text-xs text-gray-500 mb-2">Dairy</div>
                        <div className="text-lg font-bold text-green-900">18.2 units/day</div>
                        <div className="text-sm text-green-700">Predicted: 546 units/month</div>
                      </div>
                      <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                        <div className="text-sm font-medium text-gray-900">Sourdough Bread</div>
                        <div className="text-xs text-gray-500 mb-2">Bakery</div>
                        <div className="text-lg font-bold text-green-900">15.7 units/day</div>
                        <div className="text-sm text-green-700">Predicted: 471 units/month</div>
                      </div>
                    </div>
                  </div>

                  {/* Action Steps */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-md font-semibold text-gray-800 mb-3">📋 What You Need to Do:</h4>
                    <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
                      {analysisSuggestions?.nextSteps ? (
                        analysisSuggestions.nextSteps.map((step, index) => (
                          <li key={index}><strong>{step.split(' ')[0]} {step.split(' ')[1]}</strong> {step.split(' ').slice(2).join(' ')}</li>
                        ))
                      ) : (
                        <>
                          <li><strong>Click "Generate Comprehensive Analysis"</strong> to start the AI forecasting process</li>
                          <li><strong>Review low stock alerts</strong> and prioritize critical items for immediate reordering</li>
                          <li><strong>Analyze high demand products</strong> to ensure adequate stock levels</li>
                          <li><strong>Check supplier recommendations</strong> for optimized reorder quantities</li>
                          <li><strong>Set up automated alerts</strong> for continuous monitoring</li>
                        </>
                      )}
                    </ol>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'suggestions' && (
          <ReorderSuggestions limit={20} showActions={true} />
        )}

        {activeTab === 'velocity' && (
          <VelocityAnalytics days={30} />
        )}
      </div>

      {/* Last Updated */}
      {summary && (
        <div className="text-center text-sm text-gray-500">
          Last updated: {new Date(summary.last_updated).toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default PredictiveDashboard;
