const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config({ path: './.env' });

async function testSystemComponents() {
  console.log('🔍 Testing System Components...\n');

  // Test 1: Database Connection
  console.log('1. Testing Database Connection...');
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Database connection successful');
    await mongoose.disconnect();
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
  }

  // Test 2: Backend Health Check
  console.log('\n2. Testing Backend Server...');
  try {
    const response = await axios.get('http://localhost:5000/api/health');
    console.log('✅ Backend server is running');
    console.log('   Status:', response.data.status);
    console.log('   Uptime:', Math.round(response.data.uptime), 'seconds');
  } catch (error) {
    console.log('❌ Backend server test failed:', error.message);
  }

  // Test 3: Frontend Availability
  console.log('\n3. Testing Frontend Server...');
  try {
    const response = await axios.get('http://localhost:3000', { timeout: 5000 });
    console.log('✅ Frontend server is running');
  } catch (error) {
    console.log('❌ Frontend server test failed:', error.message);
  }

  // Test 4: Gemini API Configuration
  console.log('\n4. Testing Gemini API Configuration...');
  try {
    const GeminiService = require('./services/geminiService');
    const geminiService = new GeminiService();
    
    if (geminiService.isAvailable()) {
      console.log('✅ Gemini API key is configured');
      
      // Test actual connection
      const testResult = await geminiService.testConnection();
      if (testResult.success) {
        console.log('✅ Gemini API connection successful');
        console.log('   Response:', testResult.response.substring(0, 50) + '...');
      } else {
        console.log('❌ Gemini API connection failed:', testResult.error);
      }
    } else {
      console.log('❌ Gemini API key not configured');
    }
  } catch (error) {
    console.log('❌ Gemini API test failed:', error.message);
  }

  // Test 5: Chatbot Health
  console.log('\n5. Testing Chatbot Service...');
  try {
    const response = await axios.get('http://localhost:5000/api/chatbot/health');
    console.log('✅ Chatbot service is running');
    console.log('   Status:', response.data.status);
    console.log('   Gemini Available:', response.data.gemini_available);
  } catch (error) {
    console.log('❌ Chatbot service test failed:', error.message);
  }

  // Test 6: Predictive Analytics
  console.log('\n6. Testing Predictive Analytics...');
  try {
    const response = await axios.get('http://localhost:5000/api/predictive/health');
    console.log('✅ Predictive analytics service is running');
  } catch (error) {
    console.log('❌ Predictive analytics test failed:', error.message);
  }

  console.log('\n========================================');
  console.log('🎉 System Component Testing Complete!');
  console.log('========================================');
  console.log('\n📝 Summary:');
  console.log('• Frontend: http://localhost:3000');
  console.log('• Backend: http://localhost:5000');
  console.log('• Login: <EMAIL> / admin123');
  console.log('\n🔧 If any tests failed, check the console logs above.');
}

// Run the tests
testSystemComponents().catch(console.error);
