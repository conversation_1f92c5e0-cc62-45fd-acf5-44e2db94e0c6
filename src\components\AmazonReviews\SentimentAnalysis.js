import React from 'react';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

const SentimentAnalysis = ({ data, detailed = false }) => {
  // Prepare sentiment data
  const sentimentData = data?.map(item => ({
    name: item._id.charAt(0).toUpperCase() + item._id.slice(1),
    value: item.count,
    percentage: 0 // Will be calculated below
  })) || [];

  // Calculate percentages
  const total = sentimentData.reduce((sum, item) => sum + item.value, 0);
  sentimentData.forEach(item => {
    item.percentage = total > 0 ? Math.round((item.value / total) * 100) : 0;
  });

  const COLORS = {
    'Positive': '#10B981',
    'Neutral': '#6B7280',
    'Negative': '#EF4444'
  };

  const getSentimentIcon = (sentiment) => {
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return <TrendingUp className="h-5 w-5 text-green-500" />;
      case 'negative':
        return <TrendingDown className="h-5 w-5 text-red-500" />;
      default:
        return <Minus className="h-5 w-5 text-gray-500" />;
    }
  };

  const getSentimentColor = (sentiment) => {
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return 'text-green-600 bg-green-50';
      case 'negative':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  if (!detailed) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Sentiment Distribution</h3>
        
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={sentimentData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {sentimentData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[entry.name]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
          
          <div className="ml-6 space-y-3">
            {sentimentData.map((item, index) => (
              <div key={index} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-3"
                  style={{ backgroundColor: COLORS[item.name] }}
                ></div>
                <span className="text-sm text-gray-600 mr-2">{item.name}:</span>
                <span className="text-sm font-medium text-gray-900">
                  {item.value.toLocaleString()} ({item.percentage}%)
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {sentimentData.map((item, index) => (
          <div key={index} className={`rounded-lg p-6 ${getSentimentColor(item.name)}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium opacity-75">{item.name} Reviews</p>
                <p className="text-2xl font-bold mt-1">{item.value.toLocaleString()}</p>
                <p className="text-sm opacity-75 mt-1">{item.percentage}% of total</p>
              </div>
              {getSentimentIcon(item.name)}
            </div>
          </div>
        ))}
      </div>

      {/* Detailed Chart */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Sentiment Analysis Details</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Pie Chart */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-4">Distribution Overview</h4>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={sentimentData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${name} ${percentage}%`}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {sentimentData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[entry.name]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Bar Chart */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-4">Review Counts</h4>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={sentimentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Insights */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Sentiment Insights</h3>
        
        <div className="space-y-4">
          {sentimentData.length > 0 && (
            <>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm text-gray-900">
                    <strong>Overall Sentiment:</strong> {
                      sentimentData.find(s => s.name === 'Positive')?.percentage > 50 
                        ? 'Predominantly Positive' 
                        : sentimentData.find(s => s.name === 'Negative')?.percentage > 50
                        ? 'Predominantly Negative'
                        : 'Mixed Sentiment'
                    }
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm text-gray-900">
                    <strong>Customer Satisfaction:</strong> {
                      (sentimentData.find(s => s.name === 'Positive')?.percentage || 0) > 60
                        ? 'High - Customers are generally satisfied'
                        : (sentimentData.find(s => s.name === 'Positive')?.percentage || 0) > 40
                        ? 'Moderate - Mixed customer feedback'
                        : 'Low - Significant customer concerns'
                    }
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm text-gray-900">
                    <strong>Recommendation:</strong> {
                      (sentimentData.find(s => s.name === 'Negative')?.percentage || 0) > 30
                        ? 'Focus on addressing negative feedback and improving product quality'
                        : (sentimentData.find(s => s.name === 'Positive')?.percentage || 0) > 70
                        ? 'Leverage positive reviews for marketing and continue current strategies'
                        : 'Monitor sentiment trends and gather more detailed feedback'
                    }
                  </p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SentimentAnalysis;