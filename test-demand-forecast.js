/**
 * Test script to diagnose demand forecast issues
 * Run this script to check all components needed for demand forecasting
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:5000/api';
let authToken = null;

// Test configuration
const testConfig = {
  adminCredentials: {
    email: '<EMAIL>',
    password: 'admin123'
  }
};

async function testLogin() {
  try {
    console.log('🔐 Testing login...');
    const response = await axios.post(`${API_BASE}/auth/login`, testConfig.adminCredentials);
    authToken = response.data.token;
    console.log('✅ Login successful');
    return true;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testDatabaseConnection() {
  try {
    console.log('🗄️ Testing database connection...');
    const response = await axios.get(`${API_BASE}/health`);
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

async function testProductsExist() {
  try {
    console.log('📦 Checking if products exist...');
    const response = await axios.get(`${API_BASE}/products`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    const products = response.data.products || response.data;
    console.log(`✅ Found ${products.length} products in database`);
    return products.length > 0 ? products : false;
  } catch (error) {
    console.error('❌ Failed to fetch products:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testInstacartData() {
  try {
    console.log('🛒 Checking Instacart data...');
    const response = await axios.get(`${API_BASE}/instacart/products?limit=5`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    const products = response.data.products || response.data;
    console.log(`✅ Found ${products.length} Instacart products`);
    return products.length > 0;
  } catch (error) {
    console.error('❌ Failed to fetch Instacart data:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testPythonEnvironment() {
  try {
    console.log('🐍 Testing Python environment...');
    const { spawn } = require('child_process');
    
    return new Promise((resolve) => {
      const pythonProcess = spawn('python', ['--version']);
      
      pythonProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Python is available');
          resolve(true);
        } else {
          console.error('❌ Python not found or not working');
          resolve(false);
        }
      });
      
      pythonProcess.on('error', () => {
        console.error('❌ Python not found');
        resolve(false);
      });
    });
  } catch (error) {
    console.error('❌ Python environment test failed:', error.message);
    return false;
  }
}

async function testProphetInstallation() {
  try {
    console.log('📊 Testing Prophet installation...');
    const { spawn } = require('child_process');
    
    return new Promise((resolve) => {
      const pythonProcess = spawn('python', ['-c', 'import prophet; print("Prophet available")']);
      
      let output = '';
      pythonProcess.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      pythonProcess.on('close', (code) => {
        if (code === 0 && output.includes('Prophet available')) {
          console.log('✅ Prophet is installed and working');
          resolve(true);
        } else {
          console.error('❌ Prophet not installed or not working');
          resolve(false);
        }
      });
      
      pythonProcess.on('error', () => {
        console.error('❌ Failed to test Prophet');
        resolve(false);
      });
    });
  } catch (error) {
    console.error('❌ Prophet test failed:', error.message);
    return false;
  }
}

async function testForecastGeneration() {
  try {
    console.log('🔮 Testing forecast generation...');
    const response = await axios.post(`${API_BASE}/predictive/forecasts/generate`, {
      forecastDays: 7
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Forecast generation successful');
    console.log(`Generated forecasts for ${response.data.forecasts?.length || 0} products`);
    return true;
  } catch (error) {
    console.error('❌ Forecast generation failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testComprehensiveDashboard() {
  try {
    console.log('📊 Testing comprehensive dashboard...');
    const response = await axios.get(`${API_BASE}/predictive/dashboard/comprehensive`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Comprehensive dashboard accessible');
    return true;
  } catch (error) {
    console.error('❌ Comprehensive dashboard failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function runDiagnostics() {
  console.log('🚀 Starting Demand Forecast Diagnostics...\n');
  
  const results = {
    login: await testLogin(),
    database: await testDatabaseConnection(),
    products: await testProductsExist(),
    instacart: await testInstacartData(),
    python: await testPythonEnvironment(),
    prophet: await testProphetInstallation(),
    forecast: false,
    dashboard: false
  };
  
  if (results.login && results.products) {
    results.forecast = await testForecastGeneration();
    results.dashboard = await testComprehensiveDashboard();
  }
  
  console.log('\n📋 DIAGNOSTIC RESULTS:');
  console.log('='.repeat(50));
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test.toUpperCase()}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  console.log('\n💡 RECOMMENDATIONS:');
  if (!results.python) {
    console.log('- Install Python 3.8+ from https://python.org');
  }
  if (!results.prophet) {
    console.log('- Install Prophet: pip install prophet');
  }
  if (!results.products) {
    console.log('- Run: cd backend && npm run seed');
  }
  if (!results.instacart) {
    console.log('- Import Instacart data: cd backend && npm run import-instacart');
  }
  if (!results.forecast) {
    console.log('- Check Python dependencies and ensure Prophet is working');
  }
}

// Run diagnostics
runDiagnostics().catch(console.error);
