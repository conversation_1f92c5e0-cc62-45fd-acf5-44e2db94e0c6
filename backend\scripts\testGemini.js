require('dotenv').config();
const GeminiService = require('../services/geminiService');

async function testGeminiIntegration() {
  console.log('🧪 Testing Gemini Integration...\n');
  
  const geminiService = new GeminiService();
  
  // Test 1: Check if API key is configured
  console.log('1. Checking API Key Configuration...');
  if (geminiService.isAvailable()) {
    console.log('✅ Gemini API key is configured');
  } else {
    console.log('❌ Gemini API key is not configured');
    console.log('Please set GEMINI_API_KEY in your .env file');
    return;
  }
  
  // Test 2: Test basic connection
  console.log('\n2. Testing Basic Connection...');
  try {
    const connectionTest = await geminiService.testConnection();
    if (connectionTest.success) {
      console.log('✅ Connection successful');
      console.log(`Response: ${connectionTest.response}`);
    } else {
      console.log('❌ Connection failed');
      console.log(`Error: ${connectionTest.error}`);
      return;
    }
  } catch (error) {
    console.log('❌ Connection test failed');
    console.log(`Error: ${error.message}`);
    return;
  }
  
  // Test 3: Test inventory-related question
  console.log('\n3. Testing Inventory Question...');
  try {
    const inventoryResponse = await geminiService.handleGeneralQuestion(
      "What should I consider when managing inventory levels?"
    );
    
    if (inventoryResponse && inventoryResponse.text) {
      console.log('✅ Inventory question handled successfully');
      console.log(`Response: ${inventoryResponse.text.substring(0, 200)}...`);
      console.log(`Confidence: ${inventoryResponse.confidence}`);
    } else {
      console.log('❌ Failed to get inventory response');
    }
  } catch (error) {
    console.log('❌ Inventory question test failed');
    console.log(`Error: ${error.message}`);
  }
  
  // Test 4: Test product recommendation
  console.log('\n4. Testing Product Recommendation...');
  try {
    const productData = {
      recommendations: [
        {
          title: "Organic Coffee Beans",
          rating: 4.5,
          reviews: 1250,
          sentiment: "positive"
        },
        {
          title: "Premium Tea Collection",
          rating: 4.3,
          reviews: 890,
          sentiment: "positive"
        }
      ]
    };
    
    const recommendationResponse = await geminiService.generateProductRecommendations(
      "recommend coffee products",
      productData
    );
    
    if (recommendationResponse && recommendationResponse.text) {
      console.log('✅ Product recommendation generated successfully');
      console.log(`Response: ${recommendationResponse.text.substring(0, 200)}...`);
    } else {
      console.log('❌ Failed to generate product recommendation');
    }
  } catch (error) {
    console.log('❌ Product recommendation test failed');
    console.log(`Error: ${error.message}`);
  }
  
  // Test 5: Test general question handling
  console.log('\n5. Testing General Question...');
  try {
    const generalResponse = await geminiService.handleGeneralQuestion(
      "How does AI work?"
    );
    
    if (generalResponse && generalResponse.text) {
      console.log('✅ General question handled successfully');
      console.log(`Response: ${generalResponse.text.substring(0, 200)}...`);
      console.log(`Confidence: ${generalResponse.confidence}`);
    } else {
      console.log('❌ Failed to handle general question');
    }
  } catch (error) {
    console.log('❌ General question test failed');
    console.log(`Error: ${error.message}`);
  }
  
  console.log('\n🎉 Gemini integration testing completed!');
}

// Run the test
if (require.main === module) {
  testGeminiIntegration().catch(console.error);
}

module.exports = testGeminiIntegration;
