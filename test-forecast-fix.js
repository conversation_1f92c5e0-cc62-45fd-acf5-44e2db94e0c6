const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testForecastGeneration() {
  try {
    console.log('🔍 Testing forecast generation without authentication...');
    
    // First, let's try to register a test user
    try {
      const registerResponse = await axios.post(`${API_BASE}/auth/register`, {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('✅ Test user registered successfully');
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.error?.includes('already exists')) {
        console.log('ℹ️ Test user already exists, continuing...');
      } else {
        console.log('⚠️ Registration failed:', error.response?.data?.error || error.message);
      }
    }

    // Login to get token
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful, token obtained');

    // Test forecast generation
    console.log('🔮 Testing forecast generation...');
    const forecastResponse = await axios.post(`${API_BASE}/predictive/forecasts/generate`, {
      forecastDays: 7
    }, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Forecast generation successful!');
    console.log(`Generated forecasts for ${forecastResponse.data.forecasts?.length || 0} products`);
    
    // Test dashboard summary
    console.log('📊 Testing dashboard summary...');
    const dashboardResponse = await axios.get(`${API_BASE}/predictive/dashboard/summary`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Dashboard summary successful!');
    console.log('Summary:', dashboardResponse.data);
    
    return true;
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.error || error.message);
    if (error.response?.data) {
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// Run the test
testForecastGeneration().then(success => {
  if (success) {
    console.log('🎉 All tests passed! The forecast system should be working now.');
  } else {
    console.log('💥 Tests failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
