const axios = require('axios');

class GeminiService {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY;
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
    
    if (!this.apiKey) {
      console.warn('GEMINI_API_KEY not found in environment variables. Gemini integration will be disabled.');
    }
  }

  /**
   * Check if Gemini service is available
   */
  isAvailable() {
    return !!this.apiKey;
  }

  /**
   * Generate response using Gemini API
   */
  async generateResponse(prompt, context = null) {
    if (!this.isAvailable()) {
      throw new Error('Gemini API key not configured');
    }

    try {
      const systemPrompt = this.buildSystemPrompt(context);
      const fullPrompt = context ? `${systemPrompt}\n\nUser Question: ${prompt}` : prompt;

      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: fullPrompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        }
      };

      const response = await axios.post(this.baseUrl, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'X-goog-api-key': this.apiKey
        },
        timeout: 10000 // 10 second timeout
      });

      if (response.data && response.data.candidates && response.data.candidates.length > 0) {
        const candidate = response.data.candidates[0];
        if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
          return {
            text: candidate.content.parts[0].text,
            confidence: 0.8,
            source: 'gemini'
          };
        }
      }

      throw new Error('Invalid response format from Gemini API');

    } catch (error) {
      console.error('Error calling Gemini API:', error.message);
      
      if (error.response) {
        console.error('Gemini API Error Response:', error.response.data);
      }

      // Return fallback response
      return {
        text: "I'm sorry, I'm having trouble processing your request right now. Please try asking about specific inventory topics like products, stock levels, forecasts, or suppliers.",
        confidence: 0.3,
        source: 'fallback',
        error: error.message
      };
    }
  }

  /**
   * Build system prompt with inventory context
   */
  buildSystemPrompt(context) {
    let systemPrompt = `You are an AI assistant for an inventory management system called "Aura Inventory". You specialize in helping users with inventory-related questions, product analysis, demand forecasting, and supply chain management.

Your capabilities include:
- Product search and recommendations
- Inventory level monitoring and alerts
- Demand forecasting and trend analysis
- Supplier information and management
- Market basket analysis using Instacart data
- Amazon product reviews and sentiment analysis

Guidelines:
1. Always provide helpful, accurate, and actionable information
2. Focus on inventory management and business insights
3. Use tables and structured data when presenting multiple items
4. Suggest specific actions when appropriate (e.g., reorder recommendations)
5. Be concise but comprehensive in your responses
6. If you don't have specific data, explain what information would be helpful

Current context: You are helping a user with their inventory management system.`;

    if (context) {
      if (context.inventory_summary) {
        systemPrompt += `\n\nCurrent Inventory Summary:
- Total Products: ${context.inventory_summary.total_products || 'N/A'}
- Low Stock Items: ${context.inventory_summary.low_stock_items || 'N/A'}
- Categories: ${context.inventory_summary.categories || 'N/A'}`;
      }

      if (context.recent_activity) {
        systemPrompt += `\n\nRecent Activity:
${context.recent_activity}`;
      }

      if (context.user_query_history) {
        systemPrompt += `\n\nUser's Recent Questions:
${context.user_query_history.slice(0, 3).map(q => `- ${q}`).join('\n')}`;
      }
    }

    return systemPrompt;
  }

  /**
   * Generate inventory-specific insights
   */
  async generateInventoryInsights(query, inventoryData) {
    if (!this.isAvailable()) {
      return null;
    }

    const context = {
      inventory_summary: inventoryData.summary,
      recent_activity: inventoryData.recent_activity
    };

    const enhancedPrompt = `Based on the following inventory data, provide insights and recommendations for: "${query}"

Inventory Data:
${JSON.stringify(inventoryData, null, 2)}

Please provide:
1. Direct answer to the question
2. Relevant insights from the data
3. Actionable recommendations
4. Any alerts or warnings if applicable

Format your response in a clear, business-friendly manner.`;

    return await this.generateResponse(enhancedPrompt, context);
  }

  /**
   * Generate product recommendations
   */
  async generateProductRecommendations(userQuery, productData) {
    if (!this.isAvailable()) {
      return null;
    }

    const prompt = `Based on the user query "${userQuery}" and the following product data, provide intelligent product recommendations:

Product Data:
${JSON.stringify(productData, null, 2)}

Please provide:
1. Top 3-5 product recommendations with reasons
2. Comparison of key features/benefits
3. Considerations for inventory management
4. Suggested reorder quantities if applicable

Format as a helpful business recommendation.`;

    return await this.generateResponse(prompt);
  }

  /**
   * Analyze and explain data patterns
   */
  async explainDataPatterns(data, analysisType) {
    if (!this.isAvailable()) {
      return null;
    }

    const prompt = `Analyze the following ${analysisType} data and provide business insights:

Data:
${JSON.stringify(data, null, 2)}

Please provide:
1. Key patterns and trends identified
2. Business implications
3. Actionable recommendations
4. Potential risks or opportunities

Focus on practical inventory management insights.`;

    return await this.generateResponse(prompt);
  }

  /**
   * Generate smart responses for general questions
   */
  async handleGeneralQuestion(question, userContext = null) {
    if (!this.isAvailable()) {
      return {
        text: "I'm your inventory management assistant! I can help you with product searches, stock levels, demand forecasts, supplier information, and market analysis. What would you like to know?",
        confidence: 0.5,
        source: 'fallback'
      };
    }

    const context = userContext ? {
      user_query_history: userContext.recent_queries || [],
      inventory_summary: userContext.inventory_summary || null
    } : null;

    const enhancedPrompt = `User question: "${question}"

Please provide a helpful response. If this is not directly related to inventory management, gently redirect the user to inventory-related topics while still being helpful.

If the question is inventory-related, provide specific guidance on how I can help with:
- Product searches and analysis
- Stock level monitoring
- Demand forecasting
- Supplier management
- Market insights

Be friendly, professional, and actionable in your response.`;

    return await this.generateResponse(enhancedPrompt, context);
  }

  /**
   * Analyze natural language query and determine if it needs database access
   */
  async analyzeQueryIntent(userQuery) {
    if (!this.isAvailable()) {
      return {
        needs_database: false,
        query_type: 'general',
        confidence: 0.5
      };
    }

    const prompt = `Analyze this user query and determine if it requires database access: "${userQuery}"

Database schemas available:
1. Instacart Market Basket Analysis:
   - instacart_products (product_id, product_name, aisle_id, department_id, is_organic)
   - instacart_orders (order_id, user_id, order_number, order_dow, order_hour_of_day, days_since_prior_order)
   - instacart_order_products (order_id, product_id, add_to_cart_order, reordered)
   - instacart_aisles (aisle_id, aisle)
   - instacart_departments (department_id, department)

2. Amazon Reviews:
   - amazon_reviews (product_id, user_id, profile_name, helpfulness_numerator, helpfulness_denominator, score, time, summary, text)

3. Inventory Management:
   - products (id, name, description, category, price, stock_quantity, reorder_level, supplier_id)
   - suppliers (id, name, contact_email, phone, address)
   - sales_orders (id, customer_name, order_date, status, total_amount)

Respond with JSON only:
{
  "needs_database": true/false,
  "query_type": "product_search|order_analysis|customer_behavior|inventory_check|general",
  "database_tables": ["table1", "table2"],
  "confidence": 0.0-1.0,
  "suggested_sql_approach": "brief description if needs_database is true"
}`;

    try {
      const response = await this.generateResponse(prompt);
      const jsonMatch = response.text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.error('Error analyzing query intent:', error);
    }

    return {
      needs_database: false,
      query_type: 'general',
      confidence: 0.5
    };
  }

  /**
   * Generate SQL query from natural language
   */
  async generateSQLQuery(userQuery, queryIntent) {
    if (!this.isAvailable()) {
      return null;
    }

    const prompt = `Convert this natural language query to SQL: "${userQuery}"

Query Intent: ${JSON.stringify(queryIntent)}

Database Schema Context:
- instacart_products: product_id, product_name, aisle_id, department_id, is_organic
- instacart_orders: order_id, user_id, order_number, order_dow, order_hour_of_day, days_since_prior_order
- instacart_order_products: order_id, product_id, add_to_cart_order, reordered
- instacart_aisles: aisle_id, aisle
- instacart_departments: department_id, department
- amazon_reviews: product_id, user_id, profile_name, score, summary, text
- products: id, name, description, category, price, stock_quantity, reorder_level, supplier_id
- suppliers: id, name, contact_email, phone, address

Guidelines:
1. Use MongoDB aggregation syntax (not SQL)
2. Limit results to reasonable numbers (e.g., 10-20 items)
3. Include relevant joins using $lookup
4. Add sorting for meaningful results
5. Use $match for filtering

Respond with JSON only:
{
  "aggregation_pipeline": [...],
  "collection": "collection_name",
  "explanation": "brief explanation of what the query does",
  "confidence": 0.0-1.0
}`;

    try {
      const response = await this.generateResponse(prompt);
      const jsonMatch = response.text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.error('Error generating SQL query:', error);
    }

    return null;
  }

  /**
   * Enhanced query processing with database integration
   */
  async processEnhancedQuery(userQuery, queryResults = null) {
    if (!this.isAvailable()) {
      return {
        text: "I can help you with inventory management questions. Try asking about products, orders, or customer behavior.",
        confidence: 0.5,
        source: 'fallback'
      };
    }

    let prompt = `User Query: "${userQuery}"`;

    if (queryResults) {
      prompt += `\n\nDatabase Query Results:
${JSON.stringify(queryResults, null, 2)}

Based on these results, provide a comprehensive answer that:
1. Directly answers the user's question
2. Highlights key insights from the data
3. Provides business recommendations
4. Suggests follow-up actions if relevant

Format the response in a clear, business-friendly manner with proper structure.`;
    } else {
      prompt += `\n\nThis query doesn't require database access. Provide a helpful response about inventory management, market basket analysis, or general business insights.

If the user is asking about specific data that would require database queries, explain what information you could provide if they asked more specific questions.`;
    }

    return await this.generateResponse(prompt);
  }

  /**
   * Test the Gemini API connection
   */
  async testConnection() {
    if (!this.isAvailable()) {
      return {
        success: false,
        error: 'API key not configured'
      };
    }

    try {
      const response = await this.generateResponse('Hello, this is a test message. Please respond with "Connection successful".');

      return {
        success: true,
        response: response.text,
        confidence: response.confidence
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = GeminiService;
