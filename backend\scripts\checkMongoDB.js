const mongoose = require('mongoose');

async function checkMongoDB() {
  try {
    console.log('🔍 Checking MongoDB connection...');
    
    // Try to connect with a short timeout
    // Use the same connection string as the main app
    require('dotenv').config();
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/inventory_management';

    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 5000
    });
    
    console.log('✅ MongoDB is running and connected successfully!');
    
    // Check database collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`📊 Found ${collections.length} collections in database`);
    
    if (collections.length > 0) {
      console.log('Collections:', collections.map(c => c.name).join(', '));
    }
    
    await mongoose.disconnect();
    console.log('✅ Connection test completed successfully');
    
  } catch (error) {
    console.log('❌ MongoDB connection failed!');
    console.log('Error:', error.message);
    console.log('\n🔧 Troubleshooting steps:');
    console.log('1. Make sure MongoDB is installed and running');
    console.log('2. Check if MongoDB service is started:');
    console.log('   - Windows: net start MongoDB');
    console.log('   - Or start manually: mongod --dbpath C:\\data\\db');
    console.log('3. Verify MongoDB is listening on port 27017');
    console.log('4. Check firewall settings');
    
    process.exit(1);
  }
}

checkMongoDB();
