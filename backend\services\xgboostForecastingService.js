const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const mongoose = require('mongoose');
const DemandForecast = require('../models/DemandForecast');
const SalesVelocity = require('../models/SalesVelocity');
const ReorderSuggestion = require('../models/ReorderSuggestion');
const Product = require('../models/Product');
const InstacartProduct = require('../models/InstacartProduct');
const InstacartOrderProduct = require('../models/InstacartOrderProduct');

class XGBoostForecastingService {
  constructor() {
    this.pythonScriptPath = path.join(__dirname, '../../xgboost_demand_forecasting.py');
    this.dataPath = path.join(__dirname, '../../data');
    this.modelsPath = path.join(__dirname, '../../models_output');
    this.forecastHorizonDays = 30;
    this.modelType = 'XGBoost';
  }

  /**
   * Generate demand forecasts using XGBoost reorder prediction model
   */
  async generateDemandForecasts(productIds = null, options = {}) {
    try {
      console.log('Starting XGBoost reorder prediction forecast generation...');

      // Prepare feature data for XGBoost model based on notebook approach
      const featureData = await this.prepareInstacartFeatures(productIds);

      if (featureData.length === 0) {
        throw new Error('No feature data available for forecasting');
      }

      console.log(`Prepared ${featureData.length} feature records for XGBoost`);

      // Save feature data to CSV for Python script
      const csvPath = await this.saveFeatureDataToCsv(featureData);

      // Run XGBoost reorder prediction
      const forecastResults = await this.runXGBoostReorderPrediction(csvPath, options);

      // Process and save forecast results
      const savedForecasts = await this.processForecastResults(forecastResults);

      console.log(`Generated XGBoost forecasts for ${savedForecasts.length} products`);
      return savedForecasts;

    } catch (error) {
      console.error('Error generating XGBoost demand forecasts:', error);
      throw error;
    }
  }

  /**
   * Prepare Instacart features based on the predictive analysis notebook approach
   */
  async prepareInstacartFeatures(productIds = null) {
    try {
      console.log('Preparing Instacart features for XGBoost reorder prediction...');

      // Get user-product interaction data following the notebook approach
      const pipeline = [
        {
          $lookup: {
            from: 'instacart_orders',
            localField: 'order_id',
            foreignField: 'order_id',
            as: 'order'
          }
        },
        { $unwind: '$order' },
        {
          $lookup: {
            from: 'instacart_products',
            localField: 'product_id',
            foreignField: 'product_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        {
          $group: {
            _id: {
              user_id: '$order.user_id',
              product_id: '$product_id'
            },
            product_name: { $first: '$product.product_name' },
            aisle_id: { $first: '$product.aisle_id' },
            department_id: { $first: '$product.department_id' },
            // User-Product features (following notebook approach)
            total_product_orders_by_user: { $sum: 1 },
            total_product_reorders_by_user: { $sum: '$reordered' },
            avg_add_to_cart_order: { $avg: '$add_to_cart_order' },
            first_order_number: { $min: '$order.order_number' },
            last_order_number: { $max: '$order.order_number' },
            last_order_dow: { $last: '$order.order_dow' },
            last_order_hour: { $last: '$order.order_hour_of_day' }
          }
        },
        {
          $addFields: {
            // Calculate derived features
            user_product_reorder_percentage: {
              $cond: [
                { $gt: ['$total_product_orders_by_user', 0] },
                { $divide: ['$total_product_reorders_by_user', '$total_product_orders_by_user'] },
                0
              ]
            },
            order_count: '$total_product_orders_by_user',
            reorder_count: '$total_product_reorders_by_user',
            avg_days_since_last_bought: {
              $subtract: ['$last_order_number', '$first_order_number']
            }
          }
        },
        {
          $project: {
            user_id: '$_id.user_id',
            product_id: '$_id.product_id',
            product_name: 1,
            aisle_id: 1,
            department_id: 1,
            total_product_orders_by_user: 1,
            total_product_reorders_by_user: 1,
            avg_add_to_cart_order: 1,
            last_order_dow: 1,
            last_order_hour: 1,
            order_count: 1,
            reorder_count: 1,
            user_product_reorder_percentage: 1,
            avg_days_since_last_bought: 1
          }
        },
        { $limit: 10000 } // Limit for performance
      ];

      // Add product filter if specified
      if (productIds && productIds.length > 0) {
        pipeline.splice(3, 0, {
          $match: { product_id: { $in: productIds } }
        });
      }

      const featureData = await InstacartOrderProduct.aggregate(pipeline);

      console.log(`Prepared ${featureData.length} user-product feature records`);
      return featureData;

    } catch (error) {
      console.error('Error preparing Instacart features:', error);
      throw error;
    }
  }

  /**
   * Save feature data to CSV file for XGBoost Python processing
   */
  async saveFeatureDataToCsv(featureData) {
    try {
      // Ensure data directory exists
      await fs.mkdir(this.dataPath, { recursive: true });

      const csvPath = path.join(this.dataPath, `xgboost_features_${Date.now()}.csv`);

      // Helper function to escape CSV values
      const escapeCsvValue = (value) => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

      // Convert to CSV format for XGBoost model
      const csvHeader = 'user_id,product_id,product_name,aisle_id,department_id,total_product_orders_by_user,total_product_reorders_by_user,user_product_reorder_percentage,avg_add_to_cart_order,avg_days_since_last_bought,last_order_dow,last_order_hour,order_count,reorder_count\n';

      const csvRows = featureData.map(row => {
        return [
          row.user_id,
          row.product_id,
          escapeCsvValue(row.product_name),
          row.aisle_id,
          row.department_id,
          row.total_product_orders_by_user,
          row.total_product_reorders_by_user,
          row.user_product_reorder_percentage.toFixed(6),
          row.avg_add_to_cart_order.toFixed(2),
          row.avg_days_since_last_bought.toFixed(2),
          row.last_order_dow,
          row.last_order_hour,
          row.order_count,
          row.reorder_count
        ].join(',');
      }).join('\n');

      const csvContent = csvHeader + csvRows;
      await fs.writeFile(csvPath, csvContent);

      console.log(`XGBoost feature data saved to: ${csvPath}`);
      return csvPath;
    } catch (error) {
      console.error('Error saving feature data to CSV:', error);
      throw error;
    }
  }

  /**
   * Run XGBoost reorder prediction using Python script
   */
  async runXGBoostReorderPrediction(csvPath, options = {}) {
    return new Promise((resolve, reject) => {
      const pythonScript = path.join(__dirname, '..', 'instacart_xgboost_reorder_prediction.py');

      const pythonArgs = [
        pythonScript,
        '--data_path', csvPath,
        '--forecast_days', (options.forecastDays || this.forecastHorizonDays).toString(),
        '--output_path', this.modelsPath,
        '--model_type', 'xgboost_reorder'
      ];

      console.log('Running XGBoost reorder prediction with args:', pythonArgs);

      const pythonProcess = spawn('python', pythonArgs);

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
        console.log('XGBoost Python stdout:', data.toString());
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
        console.error('XGBoost Python stderr:', data.toString());
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          console.log('XGBoost reorder prediction completed successfully');
          resolve({ stdout, stderr, code });
        } else {
          console.error(`XGBoost Python process exited with code ${code}`);
          reject(new Error(`XGBoost reorder prediction failed: ${stderr}`));
        }
      });

      pythonProcess.on('error', (error) => {
        console.error('Error spawning XGBoost Python process:', error);
        reject(error);
      });
    });
  }

  /**
   * Process forecast results and save to database
   */
  async processForecastResults(forecastResults) {
    try {
      // Ensure models output directory exists
      await fs.mkdir(this.modelsPath, { recursive: true });
      
      // Read XGBoost forecast results from output files
      const forecastFiles = await fs.readdir(this.modelsPath);
      const jsonFiles = forecastFiles.filter(file => file.startsWith('forecast_product_') && file.endsWith('.json'));

      const savedForecasts = [];

      for (const file of jsonFiles) {
        const filePath = path.join(this.modelsPath, file);
        const forecastData = JSON.parse(await fs.readFile(filePath, 'utf8'));

        // Save XGBoost forecast to database
        const savedForecast = await this.saveXGBoostForecastToDatabase(forecastData);
        if (savedForecast) {
          savedForecasts.push(savedForecast);
        }
      }
      
      return savedForecasts;
    } catch (error) {
      console.error('Error processing forecast results:', error);
      throw error;
    }
  }

  /**
   * Save XGBoost forecast to database
   */
  async saveXGBoostForecastToDatabase(forecastData) {
    try {
      // Find corresponding Instacart product
      const instacartProduct = await InstacartProduct.findOne({
        product_id: forecastData.product_id
      });

      if (!instacartProduct) {
        console.warn(`Instacart product not found for ID: ${forecastData.product_id}`);
        return null;
      }

      // Create forecast data points based on reorder probabilities
      const forecastDataPoints = [];
      const baseDate = new Date();

      for (let i = 1; i <= this.forecastHorizonDays; i++) {
        const forecastDate = new Date(baseDate);
        forecastDate.setDate(baseDate.getDate() + i);

        // Convert reorder probability to demand prediction
        const reorderProbability = forecastData.reorder_probability || 0.5;
        const baselineOrders = forecastData.historical_avg_orders || 10;
        const predictedDemand = Math.round(baselineOrders * reorderProbability * (1 + Math.random() * 0.2 - 0.1));

        forecastDataPoints.push({
          date: forecastDate,
          predicted_demand: Math.max(0, predictedDemand),
          reorder_probability: reorderProbability,
          confidence_score: forecastData.confidence_score || 0.75,
          lower_bound: Math.max(0, Math.round(predictedDemand * 0.8)),
          upper_bound: Math.round(predictedDemand * 1.2),
          confidence_interval: 0.80
        });
      }

      // Create demand forecast document
      const demandForecast = new DemandForecast({
        product_id: new mongoose.Types.ObjectId(), // Generate a temporary ObjectId for Instacart products
        instacart_product_id: forecastData.product_id,
        sku: `INST_${forecastData.product_id}`,
        product_name: instacartProduct.product_name,
        category: instacartProduct.aisle_id ? `Aisle_${instacartProduct.aisle_id}` : 'Unknown',
        aisle: instacartProduct.aisle_id,
        department: instacartProduct.department_id,
        forecast_horizon_days: this.forecastHorizonDays,
        model_type: 'xgboost',
        model_version: '1.0',
        training_data_start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
        training_data_end: new Date(),
        forecast_data: forecastDataPoints,
        model_performance: {
          accuracy: forecastData.accuracy || 0.74,
          f1_score: forecastData.f1_score || 0.37,
          auc_score: forecastData.auc_score || 0.84,
          precision: forecastData.precision || 0.24,
          recall: forecastData.recall || 0.77
        },
        xgboost_features: forecastData.feature_importance || {},
        status: 'active'
      });

      await demandForecast.save();
      console.log(`Saved XGBoost forecast for product: ${instacartProduct.product_name}`);

      return demandForecast;
    } catch (error) {
      console.error('Error saving XGBoost forecast to database:', error);
      return null;
    }
  }

  /**
   * Generate reorder suggestions based on forecasts
   */
  async generateReorderSuggestions(forecastIds = null) {
    try {
      const query = forecastIds ? 
        { _id: { $in: forecastIds }, status: 'active' } : 
        { status: 'active' };
      
      const forecasts = await DemandForecast.find(query).populate('product_id');
      const suggestions = [];
      
      for (const forecast of forecasts) {
        const suggestion = await this.createReorderSuggestion(forecast);
        if (suggestion) {
          suggestions.push(suggestion);
        }
      }
      
      console.log(`Generated ${suggestions.length} reorder suggestions`);
      return suggestions;
    } catch (error) {
      console.error('Error generating reorder suggestions:', error);
      throw error;
    }
  }

  /**
   * Create individual reorder suggestion
   */
  async createReorderSuggestion(forecast) {
    try {
      const product = forecast.product_id;
      const currentStock = product.quantity;
      
      // Calculate predicted demand for different periods
      const demand7Days = forecast.getAveragePredictedDemand(7) * 7;
      const demand14Days = forecast.getAveragePredictedDemand(14) * 14;
      const demand30Days = forecast.getAveragePredictedDemand(30) * 30;
      
      // Calculate days until stockout
      const dailyDemand = forecast.getAveragePredictedDemand(7);
      const daysUntilStockout = dailyDemand > 0 ? Math.floor(currentStock / dailyDemand) : 999;
      
      // Determine urgency level
      let urgencyLevel = 'Low';
      if (daysUntilStockout <= 1) urgencyLevel = 'Critical';
      else if (daysUntilStockout <= 3) urgencyLevel = 'High';
      else if (daysUntilStockout <= 7) urgencyLevel = 'Normal';
      
      // Calculate stockout risk
      const stockoutRisk = Math.min(100, Math.max(0, 100 - (daysUntilStockout * 10)));
      
      // Calculate suggested reorder quantity
      const leadTime = 7; // Default lead time
      const safetyStock = 3; // Default safety stock days
      const suggestedQuantity = Math.ceil((dailyDemand * (leadTime + safetyStock)) - currentStock);
      
      // Calculate suggested reorder date
      const reorderDate = new Date();
      reorderDate.setDate(reorderDate.getDate() + Math.max(0, daysUntilStockout - leadTime));
      
      const reorderSuggestion = new ReorderSuggestion({
        product_id: product._id,
        demand_forecast_id: forecast._id,
        sku: product.sku,
        product_name: product.name,
        current_stock: currentStock,
        predicted_demand_7_days: Math.round(demand7Days),
        predicted_demand_14_days: Math.round(demand14Days),
        predicted_demand_30_days: Math.round(demand30Days),
        suggested_reorder_quantity: Math.max(0, suggestedQuantity),
        suggested_reorder_date: reorderDate,
        urgency_level: urgencyLevel,
        stockout_risk_percentage: Math.round(stockoutRisk),
        days_until_stockout: daysUntilStockout,
        velocity_trend: 'Stable', // This would be calculated from historical data
        confidence_score: 0.85,
        recommendation_reason: `Based on ${this.forecastHorizonDays}-day demand forecast using XGBoost reorder prediction model`,
        supplier_lead_time_days: leadTime,
        safety_stock_days: safetyStock
      });
      
      await reorderSuggestion.save();
      return reorderSuggestion;
    } catch (error) {
      console.error('Error creating reorder suggestion:', error);
      return null;
    }
  }
}

module.exports = XGBoostForecastingService;
