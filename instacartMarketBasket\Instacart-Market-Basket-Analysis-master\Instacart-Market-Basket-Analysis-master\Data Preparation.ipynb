{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Preparation"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import gc\n", "pd.options.mode.chained_assignment = None\n", "\n", "root = 'C:/Data/instacart-market-basket-analysis/'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Reading all data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["orders = pd.read_csv(root + 'orders.csv', \n", "                 dtype={\n", "                        'order_id': np.int32,\n", "                        'user_id': np.int64,\n", "                        'eval_set': 'category',\n", "                        'order_number': np.int16,\n", "                        'order_dow': np.int8,\n", "                        'order_hour_of_day': np.int8,\n", "                        'days_since_prior_order': np.float32})\n", "\n", "\n", "order_products_train = pd.read_csv(root + 'order_products__train.csv', \n", "                                 dtype={\n", "                                        'order_id': np.int32,\n", "                                        'product_id': np.uint16,\n", "                                        'add_to_cart_order': np.int16,\n", "                                        'reordered': np.int8})\n", "\n", "order_products_prior = pd.read_csv(root + 'order_products__prior.csv', \n", "                                 dtype={\n", "                                        'order_id': np.int32,\n", "                                        'product_id': np.uint16,\n", "                                        'add_to_cart_order': np.int16,\n", "                                        'reordered': np.int8})\n", "\n", "product_features = pd.read_pickle(root + 'product_features.pkl')\n", "\n", "user_features = pd.read_pickle(root + 'user_features.pkl')\n", "\n", "user_product_features = pd.read_pickle(root + 'user_product_features.pkl')\n", "\n", "products = pd.read_csv(root +'products.csv')\n", "\n", "aisles = pd.read_csv(root + 'aisles.csv')\n", "\n", "departments = pd.read_csv(root + 'departments.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### merging train order data with orders"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>user_id</th>\n", "      <th>eval_set</th>\n", "      <th>order_number</th>\n", "      <th>order_dow</th>\n", "      <th>order_hour_of_day</th>\n", "      <th>days_since_prior_order</th>\n", "      <th>product_id</th>\n", "      <th>add_to_cart_order</th>\n", "      <th>reordered</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1187899</td>\n", "      <td>1</td>\n", "      <td>train</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>14.0</td>\n", "      <td>196</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1187899</td>\n", "      <td>1</td>\n", "      <td>train</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>14.0</td>\n", "      <td>25133</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1187899</td>\n", "      <td>1</td>\n", "      <td>train</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>14.0</td>\n", "      <td>38928</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1187899</td>\n", "      <td>1</td>\n", "      <td>train</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>14.0</td>\n", "      <td>26405</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1187899</td>\n", "      <td>1</td>\n", "      <td>train</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>14.0</td>\n", "      <td>39657</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   order_id  user_id eval_set  order_number  order_dow  order_hour_of_day  \\\n", "0   1187899        1    train            11          4                  8   \n", "1   1187899        1    train            11          4                  8   \n", "2   1187899        1    train            11          4                  8   \n", "3   1187899        1    train            11          4                  8   \n", "4   1187899        1    train            11          4                  8   \n", "\n", "   days_since_prior_order  product_id  add_to_cart_order  reordered  \n", "0                    14.0         196                  1          1  \n", "1                    14.0       25133                  2          1  \n", "2                    14.0       38928                  3          1  \n", "3                    14.0       26405                  4          1  \n", "4                    14.0       39657                  5          1  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["train_orders = orders.merge(order_products_train, on = 'order_id', how = 'inner')\n", "train_orders.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["removing unnecessary columns from train_orders"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["train_orders.drop(['eval_set', 'add_to_cart_order', 'order_id'], axis = 1, inplace = True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["unique user_ids in train data"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1,  2,  5,  7,  8,  9, 10, 13, 14, 17], dtype=int64)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["train_users = train_orders.user_id.unique()\n", "train_users[:10]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["keeping only train_users in the data"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["(13307953, 11)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["user_product_features.shape"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>is_reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9</td>\n", "      <td>8</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                            10   \n", "1        1       10258                             9   \n", "2        1       10326                             1   \n", "3        1       12427                            10   \n", "4        1       13032                             3   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                               9                         0.900000   \n", "1                               8                         0.888889   \n", "2                               0                         0.000000   \n", "3                               9                         0.900000   \n", "4                               2                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \\\n", "0                 1.400000                   17.600000               10   \n", "1                 3.333333                   19.555555               10   \n", "2                 5.000000                   28.000000                5   \n", "3                 3.300000                   17.600000               10   \n", "4                 6.333333                   21.666666               10   \n", "\n", "   is_reorder_3  is_reorder_2  is_reorder_1  \n", "0           1.0           1.0           1.0  \n", "1           1.0           1.0           1.0  \n", "2           0.0           0.0           0.0  \n", "3           1.0           1.0           1.0  \n", "4           1.0           0.0           0.0  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["user_product_features.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>is_reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9</td>\n", "      <td>8</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                            10   \n", "1        1       10258                             9   \n", "2        1       10326                             1   \n", "3        1       12427                            10   \n", "4        1       13032                             3   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                               9                         0.900000   \n", "1                               8                         0.888889   \n", "2                               0                         0.000000   \n", "3                               9                         0.900000   \n", "4                               2                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \\\n", "0                 1.400000                   17.600000               10   \n", "1                 3.333333                   19.555555               10   \n", "2                 5.000000                   28.000000                5   \n", "3                 3.300000                   17.600000               10   \n", "4                 6.333333                   21.666666               10   \n", "\n", "   is_reorder_3  is_reorder_2  is_reorder_1  \n", "0           1.0           1.0           1.0  \n", "1           1.0           1.0           1.0  \n", "2           0.0           0.0           0.0  \n", "3           1.0           1.0           1.0  \n", "4           1.0           0.0           0.0  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df = user_product_features[user_product_features.user_id.isin(train_users)]\n", "df.head()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>is_reorder_1</th>\n", "      <th>order_number</th>\n", "      <th>order_dow</th>\n", "      <th>order_hour_of_day</th>\n", "      <th>days_since_prior_order</th>\n", "      <th>reordered</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>14.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9.0</td>\n", "      <td>8.0</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>14.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11.0</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>14.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                          10.0   \n", "1        1       10258                           9.0   \n", "2        1       10326                           1.0   \n", "3        1       12427                          10.0   \n", "4        1       13032                           3.0   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                             9.0                         0.900000   \n", "1                             8.0                         0.888889   \n", "2                             0.0                         0.000000   \n", "3                             9.0                         0.900000   \n", "4                             2.0                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \\\n", "0                 1.400000                   17.600000             10.0   \n", "1                 3.333333                   19.555555             10.0   \n", "2                 5.000000                   28.000000              5.0   \n", "3                 3.300000                   17.600000             10.0   \n", "4                 6.333333                   21.666666             10.0   \n", "\n", "   is_reorder_3  is_reorder_2  is_reorder_1  order_number  order_dow  \\\n", "0           1.0           1.0           1.0          11.0        4.0   \n", "1           1.0           1.0           1.0          11.0        4.0   \n", "2           0.0           0.0           0.0           NaN        NaN   \n", "3           1.0           1.0           1.0           NaN        NaN   \n", "4           1.0           0.0           0.0          11.0        4.0   \n", "\n", "   order_hour_of_day  days_since_prior_order  reordered  \n", "0                8.0                    14.0        1.0  \n", "1                8.0                    14.0        1.0  \n", "2                NaN                     NaN        NaN  \n", "3                NaN                     NaN        NaN  \n", "4                8.0                    14.0        1.0  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.merge(train_orders, on = ['user_id', 'product_id'], how = 'outer')\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["for order_number, order_dow, order_hour_of_day, days_since_prior_order, impute null values with mean values grouped by users as these products will also be potential candidate for order."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"scrolled": true}, "outputs": [], "source": ["df.order_number.fillna(df.groupby('user_id')['order_number'].transform('mean'), inplace = True)\n", "df.order_dow.fillna(df.groupby('user_id')['order_dow'].transform('mean'), inplace = True)\n", "df.order_hour_of_day.fillna(df.groupby('user_id')['order_hour_of_day'].transform('mean'), inplace = True)\n", "df.days_since_prior_order.fillna(df.groupby('user_id')['days_since_prior_order'].\\\n", "                                                             transform('mean'), inplace = True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Removing those products which were bought the first time in last order by a user"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0    828824\n", "0.0    555793\n", "Name: reordered, dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df.reordered.value_counts()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["7645837"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df.reordered.isnull().sum()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["df = df[df.reordered != 0]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["(8474661, 16)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now imputing 0 in reordered as they were not reordered by user in his/her last order."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["user_id                            0\n", "product_id                         0\n", "total_product_orders_by_user       0\n", "total_product_reorders_by_user     0\n", "user_product_reorder_percentage    0\n", "avg_add_to_cart_by_user            0\n", "avg_days_since_last_bought         0\n", "last_ordered_in                    0\n", "is_reorder_3                       0\n", "is_reorder_2                       0\n", "is_reorder_1                       0\n", "order_number                       0\n", "order_dow                          0\n", "order_hour_of_day                  0\n", "days_since_prior_order             0\n", "reordered                          0\n", "dtype: int64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df.reordered.fillna(0, inplace = True)\n", "\n", "df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>is_reorder_1</th>\n", "      <th>order_number</th>\n", "      <th>order_dow</th>\n", "      <th>order_hour_of_day</th>\n", "      <th>days_since_prior_order</th>\n", "      <th>reordered</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>14.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9.0</td>\n", "      <td>8.0</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>14.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11.0</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>14.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>14.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11.0</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>14.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                          10.0   \n", "1        1       10258                           9.0   \n", "2        1       10326                           1.0   \n", "3        1       12427                          10.0   \n", "4        1       13032                           3.0   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                             9.0                         0.900000   \n", "1                             8.0                         0.888889   \n", "2                             0.0                         0.000000   \n", "3                             9.0                         0.900000   \n", "4                             2.0                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \\\n", "0                 1.400000                   17.600000             10.0   \n", "1                 3.333333                   19.555555             10.0   \n", "2                 5.000000                   28.000000              5.0   \n", "3                 3.300000                   17.600000             10.0   \n", "4                 6.333333                   21.666666             10.0   \n", "\n", "   is_reorder_3  is_reorder_2  is_reorder_1  order_number  order_dow  \\\n", "0           1.0           1.0           1.0          11.0        4.0   \n", "1           1.0           1.0           1.0          11.0        4.0   \n", "2           0.0           0.0           0.0          11.0        4.0   \n", "3           1.0           1.0           1.0          11.0        4.0   \n", "4           1.0           0.0           0.0          11.0        4.0   \n", "\n", "   order_hour_of_day  days_since_prior_order  reordered  \n", "0                8.0                    14.0        1.0  \n", "1                8.0                    14.0        1.0  \n", "2                8.0                    14.0        0.0  \n", "3                8.0                    14.0        0.0  \n", "4                8.0                    14.0        1.0  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Merging product and user features"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>mean_add_to_cart_order</th>\n", "      <th>total_orders</th>\n", "      <th>total_reorders</th>\n", "      <th>reorder_percentage</th>\n", "      <th>unique_users</th>\n", "      <th>order_first_time_total_cnt</th>\n", "      <th>order_second_time_total_cnt</th>\n", "      <th>is_organic</th>\n", "      <th>second_time_percent</th>\n", "      <th>...</th>\n", "      <th>department_total_orders</th>\n", "      <th>department_total_reorders</th>\n", "      <th>department_reorder_percentage</th>\n", "      <th>department_unique_users</th>\n", "      <th>department_0</th>\n", "      <th>department_1</th>\n", "      <th>department_2</th>\n", "      <th>department_3</th>\n", "      <th>department_4</th>\n", "      <th>department_5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>5.801836</td>\n", "      <td>1852</td>\n", "      <td>1136.0</td>\n", "      <td>0.613391</td>\n", "      <td>716</td>\n", "      <td>716</td>\n", "      <td>276</td>\n", "      <td>0</td>\n", "      <td>0.385475</td>\n", "      <td>...</td>\n", "      <td>2887550</td>\n", "      <td>1657973.0</td>\n", "      <td>0.574180</td>\n", "      <td>174219</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>9.888889</td>\n", "      <td>90</td>\n", "      <td>12.0</td>\n", "      <td>0.133333</td>\n", "      <td>78</td>\n", "      <td>78</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>0.102564</td>\n", "      <td>...</td>\n", "      <td>1875577</td>\n", "      <td>650301.0</td>\n", "      <td>0.346721</td>\n", "      <td>172755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>6.415162</td>\n", "      <td>277</td>\n", "      <td>203.0</td>\n", "      <td>0.732852</td>\n", "      <td>74</td>\n", "      <td>74</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "      <td>0.486486</td>\n", "      <td>...</td>\n", "      <td>2690129</td>\n", "      <td>1757892.0</td>\n", "      <td>0.653460</td>\n", "      <td>172795</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>9.507599</td>\n", "      <td>329</td>\n", "      <td>147.0</td>\n", "      <td>0.446809</td>\n", "      <td>182</td>\n", "      <td>182</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0.351648</td>\n", "      <td>...</td>\n", "      <td>2236432</td>\n", "      <td>1211890.0</td>\n", "      <td>0.541885</td>\n", "      <td>163233</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>6.466667</td>\n", "      <td>15</td>\n", "      <td>9.0</td>\n", "      <td>0.600000</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0.666667</td>\n", "      <td>...</td>\n", "      <td>1875577</td>\n", "      <td>650301.0</td>\n", "      <td>0.346721</td>\n", "      <td>172755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 37 columns</p>\n", "</div>"], "text/plain": ["   product_id  mean_add_to_cart_order  total_orders  total_reorders  \\\n", "0           1                5.801836          1852          1136.0   \n", "1           2                9.888889            90            12.0   \n", "2           3                6.415162           277           203.0   \n", "3           4                9.507599           329           147.0   \n", "4           5                6.466667            15             9.0   \n", "\n", "   reorder_percentage  unique_users  order_first_time_total_cnt  \\\n", "0            0.613391           716                         716   \n", "1            0.133333            78                          78   \n", "2            0.732852            74                          74   \n", "3            0.446809           182                         182   \n", "4            0.600000             6                           6   \n", "\n", "   order_second_time_total_cnt  is_organic  second_time_percent  ...  \\\n", "0                          276           0             0.385475  ...   \n", "1                            8           0             0.102564  ...   \n", "2                           36           0             0.486486  ...   \n", "3                           64           0             0.351648  ...   \n", "4                            4           0             0.666667  ...   \n", "\n", "   department_total_orders  department_total_reorders  \\\n", "0                  2887550                  1657973.0   \n", "1                  1875577                   650301.0   \n", "2                  2690129                  1757892.0   \n", "3                  2236432                  1211890.0   \n", "4                  1875577                   650301.0   \n", "\n", "   department_reorder_percentage  department_unique_users  department_0  \\\n", "0                       0.574180                   174219             0   \n", "1                       0.346721                   172755             0   \n", "2                       0.653460                   172795             0   \n", "3                       0.541885                   163233             0   \n", "4                       0.346721                   172755             0   \n", "\n", "   department_1  department_2  department_3  department_4  department_5  \n", "0             0             0             0             0             1  \n", "1             0             0             0             1             0  \n", "2             0             0             0             1             1  \n", "3             0             0             1             0             0  \n", "4             0             0             0             1             0  \n", "\n", "[5 rows x 37 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["product_features.head()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>avg_dow</th>\n", "      <th>std_dow</th>\n", "      <th>avg_doh</th>\n", "      <th>std_doh</th>\n", "      <th>avg_since_order</th>\n", "      <th>std_since_order</th>\n", "      <th>total_orders_by_user</th>\n", "      <th>total_products_by_user</th>\n", "      <th>total_unique_product_by_user</th>\n", "      <th>total_reorders_by_user</th>\n", "      <th>reorder_propotion_by_user</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>orders_3</th>\n", "      <th>orders_2</th>\n", "      <th>orders_1</th>\n", "      <th>reorder_3</th>\n", "      <th>reorder_2</th>\n", "      <th>reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2.644068</td>\n", "      <td>1.256194</td>\n", "      <td>10.542373</td>\n", "      <td>3.500355</td>\n", "      <td>18.542374</td>\n", "      <td>10.559066</td>\n", "      <td>10</td>\n", "      <td>59</td>\n", "      <td>18</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.900000</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2.005128</td>\n", "      <td>0.971222</td>\n", "      <td>10.441026</td>\n", "      <td>1.649854</td>\n", "      <td>14.902564</td>\n", "      <td>9.671712</td>\n", "      <td>14</td>\n", "      <td>195</td>\n", "      <td>102</td>\n", "      <td>93.0</td>\n", "      <td>0.476923</td>\n", "      <td>13.928571</td>\n", "      <td>0.447961</td>\n", "      <td>19</td>\n", "      <td>9</td>\n", "      <td>16</td>\n", "      <td>0.578947</td>\n", "      <td>0.0</td>\n", "      <td>0.625000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1.011364</td>\n", "      <td>1.245630</td>\n", "      <td>16.352273</td>\n", "      <td>1.454599</td>\n", "      <td>10.181818</td>\n", "      <td>5.867395</td>\n", "      <td>12</td>\n", "      <td>88</td>\n", "      <td>33</td>\n", "      <td>55.0</td>\n", "      <td>0.625000</td>\n", "      <td>7.333333</td>\n", "      <td>0.658817</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>0.833333</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>4.722222</td>\n", "      <td>0.826442</td>\n", "      <td>13.111111</td>\n", "      <td>1.745208</td>\n", "      <td>11.944445</td>\n", "      <td>9.973330</td>\n", "      <td>5</td>\n", "      <td>18</td>\n", "      <td>17</td>\n", "      <td>1.0</td>\n", "      <td>0.055556</td>\n", "      <td>3.600000</td>\n", "      <td>0.028571</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>0.142857</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1.621622</td>\n", "      <td>1.276961</td>\n", "      <td>15.729730</td>\n", "      <td>2.588958</td>\n", "      <td>10.189189</td>\n", "      <td>7.600577</td>\n", "      <td>4</td>\n", "      <td>37</td>\n", "      <td>23</td>\n", "      <td>14.0</td>\n", "      <td>0.378378</td>\n", "      <td>9.250000</td>\n", "      <td>0.377778</td>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>12</td>\n", "      <td>0.444444</td>\n", "      <td>0.4</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id   avg_dow   std_dow    avg_doh   std_doh  avg_since_order  \\\n", "0        1  2.644068  1.256194  10.542373  3.500355        18.542374   \n", "1        2  2.005128  0.971222  10.441026  1.649854        14.902564   \n", "2        3  1.011364  1.245630  16.352273  1.454599        10.181818   \n", "3        4  4.722222  0.826442  13.111111  1.745208        11.944445   \n", "4        5  1.621622  1.276961  15.729730  2.588958        10.189189   \n", "\n", "   std_since_order  total_orders_by_user  total_products_by_user  \\\n", "0        10.559066                    10                      59   \n", "1         9.671712                    14                     195   \n", "2         5.867395                    12                      88   \n", "3         9.973330                     5                      18   \n", "4         7.600577                     4                      37   \n", "\n", "   total_unique_product_by_user  total_reorders_by_user  \\\n", "0                            18                    41.0   \n", "1                           102                    93.0   \n", "2                            33                    55.0   \n", "3                            17                     1.0   \n", "4                            23                    14.0   \n", "\n", "   reorder_propotion_by_user  average_order_size  reorder_in_order  orders_3  \\\n", "0                   0.694915            5.900000          0.705833         6   \n", "1                   0.476923           13.928571          0.447961        19   \n", "2                   0.625000            7.333333          0.658817         6   \n", "3                   0.055556            3.600000          0.028571         7   \n", "4                   0.378378            9.250000          0.377778         9   \n", "\n", "   orders_2  orders_1  reorder_3  reorder_2  reorder_1  \n", "0         6         9   0.666667        1.0   0.666667  \n", "1         9        16   0.578947        0.0   0.625000  \n", "2         5         6   0.833333        1.0   1.000000  \n", "3         2         3   0.142857        0.0   0.000000  \n", "4         5        12   0.444444        0.4   0.666667  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["user_features.head()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>...</th>\n", "      <th>total_reorders_by_user</th>\n", "      <th>reorder_propotion_by_user</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>orders_3</th>\n", "      <th>orders_2</th>\n", "      <th>orders_1</th>\n", "      <th>reorder_3</th>\n", "      <th>reorder_2</th>\n", "      <th>reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9.0</td>\n", "      <td>8.0</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 71 columns</p>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                          10.0   \n", "1        1       10258                           9.0   \n", "2        1       10326                           1.0   \n", "3        1       12427                          10.0   \n", "4        1       13032                           3.0   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                             9.0                         0.900000   \n", "1                             8.0                         0.888889   \n", "2                             0.0                         0.000000   \n", "3                             9.0                         0.900000   \n", "4                             2.0                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \\\n", "0                 1.400000                   17.600000             10.0   \n", "1                 3.333333                   19.555555             10.0   \n", "2                 5.000000                   28.000000              5.0   \n", "3                 3.300000                   17.600000             10.0   \n", "4                 6.333333                   21.666666             10.0   \n", "\n", "   is_reorder_3  is_reorder_2  ...  total_reorders_by_user  \\\n", "0           1.0           1.0  ...                    41.0   \n", "1           1.0           1.0  ...                    41.0   \n", "2           0.0           0.0  ...                    41.0   \n", "3           1.0           1.0  ...                    41.0   \n", "4           1.0           0.0  ...                    41.0   \n", "\n", "   reorder_propotion_by_user  average_order_size  reorder_in_order  orders_3  \\\n", "0                   0.694915                 5.9          0.705833         6   \n", "1                   0.694915                 5.9          0.705833         6   \n", "2                   0.694915                 5.9          0.705833         6   \n", "3                   0.694915                 5.9          0.705833         6   \n", "4                   0.694915                 5.9          0.705833         6   \n", "\n", "   orders_2  orders_1  reorder_3  reorder_2  reorder_1  \n", "0         6         9   0.666667        1.0   0.666667  \n", "1         6         9   0.666667        1.0   0.666667  \n", "2         6         9   0.666667        1.0   0.666667  \n", "3         6         9   0.666667        1.0   0.666667  \n", "4         6         9   0.666667        1.0   0.666667  \n", "\n", "[5 rows x 71 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.merge(product_features, on = 'product_id', how = 'left')\n", "df = df.merge(user_features, on = 'user_id', how = 'left')\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The dataframe has null values because the product was never bought earlier by a user"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["(8474661, 71)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["reorder_1                            0\n", "aisle_mean_add_to_cart_order         0\n", "reorder_percentage                   0\n", "unique_users                         0\n", "order_first_time_total_cnt           0\n", "order_second_time_total_cnt          0\n", "is_organic                           0\n", "second_time_percent                  0\n", "aisle_std_add_to_cart_order          0\n", "total_orders                         0\n", "aisle_total_orders                   0\n", "aisle_total_reorders                 0\n", "aisle_reorder_percentage             0\n", "aisle_unique_users                   0\n", "aisle_0                              0\n", "aisle_1                              0\n", "total_reorders                       0\n", "mean_add_to_cart_order               0\n", "aisle_3                              0\n", "last_ordered_in                      0\n", "product_id                           0\n", "total_product_orders_by_user         0\n", "total_product_reorders_by_user       0\n", "user_product_reorder_percentage      0\n", "avg_add_to_cart_by_user              0\n", "avg_days_since_last_bought           0\n", "is_reorder_3                         0\n", "reordered                            0\n", "is_reorder_2                         0\n", "is_reorder_1                         0\n", "                                    ..\n", "total_orders_by_user                 0\n", "total_products_by_user               0\n", "total_unique_product_by_user         0\n", "reorder_propotion_by_user            0\n", "std_dow                              0\n", "average_order_size                   0\n", "reorder_in_order                     0\n", "orders_3                             0\n", "orders_2                             0\n", "orders_1                             0\n", "reorder_3                            0\n", "avg_doh                              0\n", "avg_dow                              0\n", "aisle_5                              0\n", "department_total_reorders            0\n", "aisle_6                              0\n", "aisle_7                              0\n", "aisle_8                              0\n", "department_mean_add_to_cart_order    0\n", "department_std_add_to_cart_order     0\n", "department_total_orders              0\n", "department_reorder_percentage        0\n", "department_5                         0\n", "department_unique_users              0\n", "department_0                         0\n", "department_1                         0\n", "department_2                         0\n", "department_3                         0\n", "department_4                         0\n", "user_id                              0\n", "Length: 71, dtype: int64"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isnull().sum().sort_values(ascending = False)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["df.to_pickle(root + 'Finaldata.pkl')"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>...</th>\n", "      <th>total_reorders_by_user</th>\n", "      <th>reorder_propotion_by_user</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>orders_3</th>\n", "      <th>orders_2</th>\n", "      <th>orders_1</th>\n", "      <th>reorder_3</th>\n", "      <th>reorder_2</th>\n", "      <th>reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9.0</td>\n", "      <td>8.0</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 71 columns</p>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                          10.0   \n", "1        1       10258                           9.0   \n", "2        1       10326                           1.0   \n", "3        1       12427                          10.0   \n", "4        1       13032                           3.0   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                             9.0                         0.900000   \n", "1                             8.0                         0.888889   \n", "2                             0.0                         0.000000   \n", "3                             9.0                         0.900000   \n", "4                             2.0                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \\\n", "0                 1.400000                   17.600000             10.0   \n", "1                 3.333333                   19.555555             10.0   \n", "2                 5.000000                   28.000000              5.0   \n", "3                 3.300000                   17.600000             10.0   \n", "4                 6.333333                   21.666666             10.0   \n", "\n", "   is_reorder_3  is_reorder_2  ...  total_reorders_by_user  \\\n", "0           1.0           1.0  ...                    41.0   \n", "1           1.0           1.0  ...                    41.0   \n", "2           0.0           0.0  ...                    41.0   \n", "3           1.0           1.0  ...                    41.0   \n", "4           1.0           0.0  ...                    41.0   \n", "\n", "   reorder_propotion_by_user  average_order_size  reorder_in_order  orders_3  \\\n", "0                   0.694915                 5.9          0.705833         6   \n", "1                   0.694915                 5.9          0.705833         6   \n", "2                   0.694915                 5.9          0.705833         6   \n", "3                   0.694915                 5.9          0.705833         6   \n", "4                   0.694915                 5.9          0.705833         6   \n", "\n", "   orders_2  orders_1  reorder_3  reorder_2  reorder_1  \n", "0         6         9   0.666667        1.0   0.666667  \n", "1         6         9   0.666667        1.0   0.666667  \n", "2         6         9   0.666667        1.0   0.666667  \n", "3         6         9   0.666667        1.0   0.666667  \n", "4         6         9   0.666667        1.0   0.666667  \n", "\n", "[5 rows x 71 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df2 = pd.read_pickle(root +'Finaldata.pkl')\n", "df2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. Ready for some cool modeling now :p"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}