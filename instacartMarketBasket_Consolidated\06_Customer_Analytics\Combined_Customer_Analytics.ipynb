{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comprehensive Customer Analytics\n", "## Instacart Market Basket Analysis\n", "\n", "This notebook provides comprehensive customer analytics including segmentation, behavior analysis, and insights for inventory management.\n", "\n", "### Key Components:\n", "- Customer behavior analysis\n", "- Customer segmentation using machine learning\n", "- RFM (Recency, Frequency, Monetary) analysis\n", "- Customer lifetime value estimation\n", "- Actionable insights for business strategy\n", "\n", "### Business Applications:\n", "- Targeted marketing campaigns\n", "- Inventory optimization by customer segment\n", "- Customer retention strategies\n", "- Personalized product recommendations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries and Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import gc\n", "import os\n", "import warnings\n", "from datetime import datetime, timedelta\n", "\n", "# Machine learning libraries\n", "from sklearn.cluster import KMeans, DBSCAN\n", "from sklearn.preprocessing import StandardScaler, RobustScaler\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics import silhouette_score, calinski_harabasz_score\n", "from sklearn.manifold import TSNE\n", "\n", "# Statistical libraries\n", "from scipy import stats\n", "from scipy.cluster.hierarchy import dendrogram, linkage, fcluster\n", "\n", "# Suppress warnings and set options\n", "warnings.filterwarnings('ignore')\n", "pd.set_option('display.max_columns', 150)\n", "pd.set_option('display.max_rows', 100)\n", "\n", "# Set visualization parameters\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette('husl')\n", "matplotlib.rcParams['figure.dpi'] = 120\n", "matplotlib.rcParams['figure.figsize'] = (12, 8)\n", "\n", "# Data directory path\n", "data_directory_path = '../01_Data/InstarcartMarketBasketAnalysisDataset/'\n", "\n", "print('Libraries imported successfully!')\n", "print('Customer Analytics Environment Ready!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading and Preparation\n", "\n", "Loading and preparing the Instacart dataset for customer analytics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Loading Instacart dataset...')\n", "\n", "# Load all datasets with optimized data types\n", "orders = pd.read_csv(data_directory_path + 'orders.csv',\n", "                     dtype={\n", "                         'order_id': np.int32,\n", "                         'user_id': np.int32,\n", "                         'eval_set': 'category',\n", "                         'order_number': np.int16,\n", "                         'order_dow': np.int8,\n", "                         'order_hour_of_day': np.int8,\n", "                         'days_since_prior_order': np.float32\n", "                     })\n", "\n", "order_products_prior = pd.read_csv(data_directory_path + 'order_products__prior.csv',\n", "                                   dtype={\n", "                                       'order_id': np.int32,\n", "                                       'product_id': np.int32,\n", "                                       'add_to_cart_order': np.int16,\n", "                                       'reordered': np.int8\n", "                                   })\n", "\n", "order_products_train = pd.read_csv(data_directory_path + 'order_products__train.csv',\n", "                                   dtype={\n", "                                       'order_id': np.int32,\n", "                                       'product_id': np.int32,\n", "                                       'add_to_cart_order': np.int16,\n", "                                       'reordered': np.int8\n", "                                   })\n", "\n", "products = pd.read_csv(data_directory_path + 'products.csv')\n", "aisles = pd.read_csv(data_directory_path + 'aisles.csv')\n", "departments = pd.read_csv(data_directory_path + 'departments.csv')\n", "\n", "print(f'Orders: {orders.shape}')\n", "print(f'Order Products Prior: {order_products_prior.shape}')\n", "print(f'Order Products Train: {order_products_train.shape}')\n", "print(f'Products: {products.shape}')\n", "print(f'Aisles: {aisles.shape}')\n", "print(f'Departments: {departments.shape}')\n", "\n", "# Combine order products\n", "order_products = pd.concat([order_products_prior, order_products_train], ignore_index=True)\n", "print(f'Combined Order Products: {order_products.shape}')\n", "\n", "# Create comprehensive dataset\n", "order_data = order_products.merge(orders, on='order_id', how='left')\n", "order_data = order_data.merge(products, on='product_id', how='left')\n", "order_data = order_data.merge(departments, on='department_id', how='left')\n", "order_data = order_data.merge(aisles, on='aisle_id', how='left')\n", "\n", "print(f'\\nComprehensive dataset: {order_data.shape}')\n", "print(f'Unique customers: {order_data[\"user_id\"].nunique():,}')\n", "print(f'Date range: {order_data[\"order_number\"].min()} to {order_data[\"order_number\"].max()} orders')\n", "\n", "# Display sample data\n", "print('\\nSample of comprehensive dataset:')\n", "display(order_data.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Customer Behavior Analysis\n", "\n", "Analyzing customer shopping patterns, preferences, and behaviors."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Analyzing customer behavior patterns...')\n", "\n", "# Customer-level aggregations\n", "customer_behavior = order_data.groupby('user_id').agg({\n", "    'order_id': 'nunique',\n", "    'product_id': ['count', 'nunique'],\n", "    'reordered': ['sum', 'mean'],\n", "    'order_number': 'max',\n", "    'days_since_prior_order': ['mean', 'std'],\n", "    'order_dow': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n", "    'order_hour_of_day': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n", "    'department': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n", "    'aisle': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0]\n", "}).reset_index()\n", "\n", "# Flatten column names\n", "customer_behavior.columns = [\n", "    'user_id', 'total_orders', 'total_products', 'unique_products',\n", "    'total_reorders', 'reorder_rate', 'max_order_number',\n", "    'avg_days_between_orders', 'std_days_between_orders',\n", "    'favorite_dow', 'favorite_hour', 'favorite_department', 'favorite_aisle'\n", "]\n", "\n", "# Additional behavioral metrics\n", "customer_behavior['avg_products_per_order'] = customer_behavior['total_products'] / customer_behavior['total_orders']\n", "customer_behavior['product_diversity'] = customer_behavior['unique_products'] / customer_behavior['total_products']\n", "customer_behavior['customer_lifetime_orders'] = customer_behavior['max_order_number']\n", "\n", "# Customer value metrics (proxy using order frequency and size)\n", "customer_behavior['order_frequency'] = 1 / customer_behavior['avg_days_between_orders'].fillna(30)\n", "customer_behavior['customer_value_score'] = (\n", "    customer_behavior['total_orders'] * \n", "    customer_behavior['avg_products_per_order'] * \n", "    customer_behavior['reorder_rate']\n", ")\n", "\n", "print(f'Customer behavior dataset shape: {customer_behavior.shape}')\n", "print(f'Columns: {list(customer_behavior.columns)}')\n", "\n", "# Display summary statistics\n", "print('\\nCustomer Behavior Summary:')\n", "print('=' * 40)\n", "print(f'Average orders per customer: {customer_behavior[\"total_orders\"].mean():.1f}')\n", "print(f'Average products per order: {customer_behavior[\"avg_products_per_order\"].mean():.1f}')\n", "print(f'Average reorder rate: {customer_behavior[\"reorder_rate\"].mean()*100:.1f}%')\n", "print(f'Average days between orders: {customer_behavior[\"avg_days_between_orders\"].mean():.1f}')\n", "\n", "# Display sample\n", "print('\\nSample customer behavior data:')\n", "display(customer_behavior.head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RFM Analysis (Recency, Frequency, Monetary)\n", "\n", "Implementing RFM analysis to segment customers based on their purchasing behavior."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Performing RFM Analysis...')\n", "\n", "# Calculate RFM metrics\n", "# For this dataset, we'll adapt RFM to available data:\n", "# R (Recency): Days since last order (inverse of max order number)\n", "# F (Frequency): Total number of orders\n", "# M (Monetary): Total products purchased (proxy for monetary value)\n", "\n", "rfm_data = customer_behavior[['user_id', 'total_orders', 'total_products', 'max_order_number']].copy()\n", "\n", "# Calculate recency (higher order number = more recent)\n", "rfm_data['recency'] = rfm_data['max_order_number']\n", "rfm_data['frequency'] = rfm_data['total_orders']\n", "rfm_data['monetary'] = rfm_data['total_products']\n", "\n", "# Create RFM scores (1-5 scale)\n", "rfm_data['R_score'] = pd.qcut(rfm_data['recency'], 5, labels=[1, 2, 3, 4, 5])\n", "rfm_data['F_score'] = pd.qcut(rfm_data['frequency'].rank(method='first'), 5, labels=[1, 2, 3, 4, 5])\n", "rfm_data['M_score'] = pd.qcut(rfm_data['monetary'].rank(method='first'), 5, labels=[1, 2, 3, 4, 5])\n", "\n", "# Convert to numeric\n", "rfm_data['R_score'] = rfm_data['R_score'].astype(int)\n", "rfm_data['F_score'] = rfm_data['F_score'].astype(int)\n", "rfm_data['M_score'] = rfm_data['M_score'].astype(int)\n", "\n", "# Create RFM combined score\n", "rfm_data['RFM_score'] = rfm_data['R_score'].astype(str) + rfm_data['F_score'].astype(str) + rfm_data['M_score'].astype(str)\n", "rfm_data['RFM_score_numeric'] = rfm_data['R_score'] + rfm_data['F_score'] + rfm_data['M_score']\n", "\n", "# Define customer segments based on RFM scores\n", "def segment_customers(row):\n", "    if row['RFM_score_numeric'] >= 12:\n", "        return 'Champions'\n", "    elif row['RFM_score_numeric'] >= 10:\n", "        return 'Loyal Customers'\n", "    elif row['RFM_score_numeric'] >= 8:\n", "        return 'Potential Loyalists'\n", "    elif row['RFM_score_numeric'] >= 6:\n", "        return 'At Risk'\n", "    else:\n", "        return 'Lost Customers'\n", "\n", "rfm_data['customer_segment'] = rfm_data.apply(segment_customers, axis=1)\n", "\n", "print(f'RFM analysis completed for {len(rfm_data)} customers')\n", "\n", "# Display RFM distribution\n", "print('\\nRFM Score Distribution:')\n", "print(rfm_data[['R_score', 'F_score', 'M_score']].describe())\n", "\n", "print('\\nCustomer Segment Distribution:')\n", "segment_counts = rfm_data['customer_segment'].value_counts()\n", "segment_pct = rfm_data['customer_segment'].value_counts(normalize=True) * 100\n", "segment_summary = pd.DataFrame({\n", "    'Count': segment_counts,\n", "    'Percentage': segment_pct\n", "})\n", "display(segment_summary)\n", "\n", "# Visualize RFM segments\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('RFM Analysis Results', fontsize=16, fontweight='bold')\n", "\n", "# RFM score distribution\n", "ax1 = axes[0, 0]\n", "rfm_data['RFM_score_numeric'].hist(bins=20, ax=ax1, color='skyblue', edgecolor='black')\n", "ax1.set_title('RFM Score Distribution')\n", "ax1.set_xlabel('RFM Score')\n", "ax1.set_ylabel('Number of Customers')\n", "\n", "# Customer segments\n", "ax2 = axes[0, 1]\n", "segment_counts.plot(kind='bar', ax=ax2, color='lightcoral')\n", "ax2.set_title('Customer Segments')\n", "ax2.set_xlabel('Segment')\n", "ax2.set_ylabel('Number of Customers')\n", "ax2.tick_params(axis='x', rotation=45)\n", "\n", "# RFM heatmap\n", "ax3 = axes[1, 0]\n", "rfm_pivot = rfm_data.pivot_table(values='user_id', index='R_score', columns='F_score', aggfunc='count', fill_value=0)\n", "sns.heatmap(rfm_pivot, annot=True, fmt='d', ax=ax3, cmap='YlOrRd')\n", "ax3.set_title('RFM Heatmap (Recency vs Frequency)')\n", "\n", "# Segment value analysis\n", "ax4 = axes[1, 1]\n", "segment_value = rfm_data.groupby('customer_segment')['monetary'].mean().sort_values(ascending=True)\n", "segment_value.plot(kind='barh', ax=ax4, color='lightgreen')\n", "ax4.set_title('Average Monetary Value by Segment')\n", "ax4.set_xlabel('Average Products Purchased')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print('\\nRFM Analysis completed successfully!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Customer Segmentation\n", "\n", "Using machine learning techniques for customer segmentation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Performing advanced customer segmentation using K-Means...')\n", "\n", "# Prepare features for clustering\n", "clustering_features = customer_behavior[[\n", "    'total_orders', 'total_products', 'unique_products',\n", "    'reorder_rate', 'avg_products_per_order', 'product_diversity',\n", "    'avg_days_between_orders', 'customer_value_score'\n", "]].copy()\n", "\n", "# Handle missing values\n", "clustering_features = clustering_features.fillna(clustering_features.median())\n", "\n", "# Standardize features\n", "scaler = StandardScaler()\n", "features_scaled = scaler.fit_transform(clustering_features)\n", "features_scaled_df = pd.DataFrame(features_scaled, columns=clustering_features.columns)\n", "\n", "print(f'Features prepared for clustering: {features_scaled.shape}')\n", "\n", "# Determine optimal number of clusters using elbow method and silhouette score\n", "k_range = range(2, 11)\n", "inertias = []\n", "silhouette_scores = []\n", "\n", "for k in k_range:\n", "    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "    kmeans.fit(features_scaled)\n", "    inertias.append(kmeans.inertia_)\n", "    silhouette_scores.append(silhouette_score(features_scaled, kmeans.labels_))\n", "\n", "# Plot elbow curve and silhouette scores\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Elbow curve\n", "axes[0].plot(k_range, inertias, 'bo-')\n", "axes[0].set_title('Elbow Method for Optimal k')\n", "axes[0].set_xlabel('Number of Clusters (k)')\n", "axes[0].set_ylabel('Inertia')\n", "axes[0].grid(True)\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> scores\n", "axes[1].plot(k_range, silhouette_scores, 'ro-')\n", "axes[1].set_title('Silhouette Score vs Number of Clusters')\n", "axes[1].set_xlabel('Number of Clusters (k)')\n", "axes[1].set_ylabel('Silhouette Score')\n", "axes[1].grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Choose optimal k (highest silhouette score)\n", "optimal_k = k_range[np.argmax(silhouette_scores)]\n", "print(f'\\nOptimal number of clusters: {optimal_k}')\n", "print(f'Best silhouette score: {max(silhouette_scores):.3f}')\n", "\n", "# Perform final clustering\n", "final_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)\n", "cluster_labels = final_kmeans.fit_predict(features_scaled)\n", "\n", "# Add cluster labels to customer data\n", "customer_segments = customer_behavior.copy()\n", "customer_segments['ml_cluster'] = cluster_labels\n", "customer_segments['ml_segment'] = customer_segments['ml_cluster'].map({\n", "    0: 'Segment_A', 1: 'Segment_B', 2: 'Segment_C', \n", "    3: 'Segment_D', 4: 'Segment_E'\n", "})\n", "\n", "print(f'\\nCustomer segmentation completed!')\n", "print(f'Cluster distribution:')\n", "cluster_dist = customer_segments['ml_segment'].value_counts()\n", "for segment, count in cluster_dist.items():\n", "    pct = count / len(customer_segments) * 100\n", "    print(f'{segment}: {count:,} customers ({pct:.1f}%)')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Customer Segment Analysis\n", "\n", "Analyzing characteristics of each customer segment for business insights."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Analyzing customer segment characteristics...')\n", "\n", "# Segment profiling\n", "segment_profile = customer_segments.groupby('ml_segment').agg({\n", "    'total_orders': ['mean', 'median'],\n", "    'total_products': ['mean', 'median'],\n", "    'unique_products': ['mean', 'median'],\n", "    'reorder_rate': ['mean', 'median'],\n", "    'avg_products_per_order': ['mean', 'median'],\n", "    'avg_days_between_orders': ['mean', 'median'],\n", "    'customer_value_score': ['mean', 'median'],\n", "    'user_id': 'count'\n", "}).round(2)\n", "\n", "# Flatten column names\n", "segment_profile.columns = ['_'.join(col).strip() for col in segment_profile.columns]\n", "segment_profile = segment_profile.reset_index()\n", "\n", "print('\\nSegment Profile Summary:')\n", "display(segment_profile)\n", "\n", "# Visualize segment characteristics\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "fig.suptitle('Customer Segment Characteristics', fontsize=16, fontweight='bold')\n", "\n", "# Total orders by segment\n", "ax1 = axes[0, 0]\n", "customer_segments.boxplot(column='total_orders', by='ml_segment', ax=ax1)\n", "ax1.set_title('Total Orders by Segment')\n", "ax1.set_xlabel('Segment')\n", "\n", "# Reorder rate by segment\n", "ax2 = axes[0, 1]\n", "customer_segments.boxplot(column='reorder_rate', by='ml_segment', ax=ax2)\n", "ax2.set_title('Reorder Rate by Segment')\n", "ax2.set_xlabel('Segment')\n", "\n", "# Customer value score by segment\n", "ax3 = axes[0, 2]\n", "customer_segments.boxplot(column='customer_value_score', by='ml_segment', ax=ax3)\n", "ax3.set_title('Customer Value Score by Segment')\n", "ax3.set_xlabel('Segment')\n", "\n", "# Average products per order\n", "ax4 = axes[1, 0]\n", "customer_segments.boxplot(column='avg_products_per_order', by='ml_segment', ax=ax4)\n", "ax4.set_title('Avg Products per Order by Segment')\n", "ax4.set_xlabel('Segment')\n", "\n", "# Product diversity\n", "ax5 = axes[1, 1]\n", "customer_segments.boxplot(column='product_diversity', by='ml_segment', ax=ax5)\n", "ax5.set_title('Product Diversity by Segment')\n", "ax5.set_xlabel('Segment')\n", "\n", "# Days between orders\n", "ax6 = axes[1, 2]\n", "customer_segments.boxplot(column='avg_days_between_orders', by='ml_segment', ax=ax6)\n", "ax6.set_title('Avg Days Between Orders by Segment')\n", "ax6.set_xlabel('Segment')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Segment naming based on characteristics\n", "print('\\nSegment Characteristics and Business Names:')\n", "print('=' * 50)\n", "\n", "for segment in customer_segments['ml_segment'].unique():\n", "    seg_data = customer_segments[customer_segments['ml_segment'] == segment]\n", "    avg_orders = seg_data['total_orders'].mean()\n", "    avg_reorder = seg_data['reorder_rate'].mean()\n", "    avg_value = seg_data['customer_value_score'].mean()\n", "    count = len(seg_data)\n", "    \n", "    print(f'\\n{segment}:')\n", "    print(f'  Size: {count:,} customers')\n", "    print(f'  Avg Orders: {avg_orders:.1f}')\n", "    print(f'  Avg Reorder Rate: {avg_reorder:.1%}')\n", "    print(f'  Avg Value Score: {avg_value:.1f}')\n", "    \n", "    # Business interpretation\n", "    if avg_orders > 20 and avg_reorder > 0.6:\n", "        business_name = 'VIP Customers'\n", "    elif avg_orders > 15 and avg_reorder > 0.5:\n", "        business_name = 'Loyal Customers'\n", "    elif avg_orders > 10:\n", "        business_name = 'Regular Customers'\n", "    elif avg_reorder > 0.4:\n", "        business_name = 'Occasional Loyalists'\n", "    else:\n", "        business_name = 'New/Infrequent Customers'\n", "    \n", "    print(f'  Business Category: {business_name}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Business Insights and Recommendations\n", "\n", "Actionable insights for inventory management and customer strategy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('COMPREHENSIVE CUSTOMER ANALYTICS INSIGHTS')\n", "print('=' * 60)\n", "\n", "# Overall customer metrics\n", "total_customers = len(customer_segments)\n", "avg_orders_per_customer = customer_segments['total_orders'].mean()\n", "avg_products_per_customer = customer_segments['total_products'].mean()\n", "overall_reorder_rate = customer_segments['reorder_rate'].mean()\n", "\n", "print(f'\\n1. OVERALL CUSTOMER METRICS:')\n", "print('-' * 30)\n", "print(f'Total Customers Analyzed: {total_customers:,}')\n", "print(f'Average Orders per Customer: {avg_orders_per_customer:.1f}')\n", "print(f'Average Products per Customer: {avg_products_per_customer:.1f}')\n", "print(f'Overall Reorder Rate: {overall_reorder_rate:.1%}')\n", "\n", "# Segment insights\n", "print(f'\\n2. CUSTOMER SEGMENT INSIGHTS:')\n", "print('-' * 35)\n", "\n", "# High-value segment identification\n", "high_value_segment = customer_segments.loc[customer_segments['customer_value_score'].idxmax(), 'ml_segment']\n", "high_value_customers = customer_segments[customer_segments['ml_segment'] == high_value_segment]\n", "\n", "print(f'Highest Value Segment: {high_value_segment}')\n", "print(f'  - Size: {len(high_value_customers):,} customers ({len(high_value_customers)/total_customers:.1%})')\n", "print(f'  - Avg Orders: {high_value_customers[\"total_orders\"].mean():.1f}')\n", "print(f'  - Avg Reorder Rate: {high_value_customers[\"reorder_rate\"].mean():.1%}')\n", "\n", "# Customer lifetime value analysis\n", "print(f'\\n3. CUSTOMER LIFETIME VALUE ANALYSIS:')\n", "print('-' * 40)\n", "\n", "# Calculate CLV proxy\n", "customer_segments['clv_proxy'] = (\n", "    customer_segments['total_orders'] * \n", "    customer_segments['avg_products_per_order'] * \n", "    (1 + customer_segments['reorder_rate'])\n", ")\n", "\n", "clv_segments = customer_segments.groupby('ml_segment')['clv_proxy'].agg(['mean', 'sum']).round(2)\n", "clv_segments['customer_count'] = customer_segments.groupby('ml_segment').size()\n", "clv_segments['revenue_contribution'] = clv_segments['sum'] / clv_segments['sum'].sum() * 100\n", "\n", "print('CLV Analysis by Segment:')\n", "display(clv_segments.sort_values('mean', ascending=False))\n", "\n", "# Inventory management insights\n", "print(f'\\n4. INVENTORY MANAGEMENT INSIGHTS:')\n", "print('-' * 40)\n", "\n", "# Favorite departments by segment\n", "segment_departments = customer_segments.groupby(['ml_segment', 'favorite_department']).size().unstack(fill_value=0)\n", "print('Top Departments by Segment:')\n", "for segment in customer_segments['ml_segment'].unique():\n", "    top_dept = segment_departments.loc[segment].idxmax()\n", "    print(f'  {segment}: {top_dept}')\n", "\n", "# Shopping timing insights\n", "print(f'\\n5. SHOPPING PATTERN INSIGHTS:')\n", "print('-' * 35)\n", "\n", "# Peak shopping times by segment\n", "segment_timing = customer_segments.groupby('ml_segment').agg({\n", "    'favorite_dow': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n", "    'favorite_hour': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n", "    'avg_days_between_orders': 'mean'\n", "})\n", "\n", "print('Shopping Patterns by Segment:')\n", "display(segment_timing)\n", "\n", "print(f'\\n6. ACTIONABLE RECOMMENDATIONS:')\n", "print('-' * 35)\n", "print('\\nINVENTORY OPTIMIZATION:')\n", "print('• Focus inventory on high-value customer segments')\n", "print('• Stock popular departments for each segment')\n", "print('• Align inventory levels with segment shopping patterns')\n", "\n", "print('\\nCUSTOMER RETENTION:')\n", "print('• Implement loyalty programs for high-value segments')\n", "print('• Create targeted promotions based on reorder patterns')\n", "print('• Develop personalized product recommendations')\n", "\n", "print('\\nDEMAND FORECASTING:')\n", "print('• Use segment behavior for predictive modeling')\n", "print('• Account for segment-specific seasonality')\n", "print('• Optimize reorder timing predictions')\n", "\n", "print('\\nOPERATIONAL EFFICIENCY:')\n", "print('• Schedule staff based on segment shopping times')\n", "print('• Optimize delivery routes for high-value customers')\n", "print('• Prioritize inventory for segments with short reorder cycles')\n", "\n", "print('\\n' + '=' * 60)\n", "print('CUSTOMER ANALYTICS COMPLETED SUCCESSFULLY!')\n", "print('Ready for implementation in inventory management system.')\n", "print('=' * 60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.0", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}}, "nbformat": 4, "nbformat_minor": 4}