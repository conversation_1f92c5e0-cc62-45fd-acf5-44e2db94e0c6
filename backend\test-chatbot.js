const mongoose = require('mongoose');
require('dotenv').config();

// Import chatbot service
const ChatbotService = require('./services/chatbotService');

async function testChatbotQueries() {
  console.log('🤖 Testing Chatbot Database Queries\n');
  
  try {
    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB connected successfully\n');

    const chatbot = new ChatbotService();
    const sessionId = 'test-session-' + Date.now();
    const userId = 'test-user';

    console.log('🧪 Testing Chatbot Queries:');
    console.log('=' .repeat(60));

    // Test 1: Product Search (Instacart)
    console.log('\n1️⃣ Testing Product Search Query:');
    console.log('Query: "Find products with pasta"');
    const productSearchResult = await chatbot.processQuery(
      'Find products with pasta', 
      sessionId, 
      userId
    );
    console.log('Response:', productSearchResult.response.substring(0, 200) + '...');
    console.log('Response Type:', productSearchResult.response_type);
    console.log('Confidence:', productSearchResult.confidence);
    console.log('Processing Time:', productSearchResult.processing_time + 'ms');

    // Test 2: General Product Search
    console.log('\n2️⃣ Testing General Product Search:');
    console.log('Query: "Show me products in dairy department"');
    const dairySearchResult = await chatbot.processQuery(
      'Show me products in dairy department', 
      sessionId, 
      userId
    );
    console.log('Response:', dairySearchResult.response.substring(0, 200) + '...');
    console.log('Response Type:', dairySearchResult.response_type);
    console.log('Confidence:', dairySearchResult.confidence);

    // Test 3: Inventory Query
    console.log('\n3️⃣ Testing Inventory Query:');
    console.log('Query: "Check inventory levels"');
    const inventoryResult = await chatbot.processQuery(
      'Check inventory levels', 
      sessionId, 
      userId
    );
    console.log('Response:', inventoryResult.response.substring(0, 200) + '...');
    console.log('Response Type:', inventoryResult.response_type);
    console.log('Confidence:', inventoryResult.confidence);

    // Test 4: Specific Product Inventory
    console.log('\n4️⃣ Testing Specific Product Inventory:');
    console.log('Query: "How many Cotton T-Shirt in stock"');
    const specificInventoryResult = await chatbot.processQuery(
      'How many Cotton T-Shirt in stock', 
      sessionId, 
      userId
    );
    console.log('Response:', specificInventoryResult.response.substring(0, 200) + '...');
    console.log('Response Type:', specificInventoryResult.response_type);
    console.log('Confidence:', specificInventoryResult.confidence);

    // Test 5: Demand Forecast Query
    console.log('\n5️⃣ Testing Demand Forecast Query:');
    console.log('Query: "Predict demand for next week"');
    const forecastResult = await chatbot.processQuery(
      'Predict demand for next week', 
      sessionId, 
      userId
    );
    console.log('Response:', forecastResult.response.substring(0, 200) + '...');
    console.log('Response Type:', forecastResult.response_type);
    console.log('Confidence:', forecastResult.confidence);

    // Test 6: Amazon Review Query (should handle gracefully)
    console.log('\n6️⃣ Testing Amazon Review Query (No Data Expected):');
    console.log('Query: "What are reviews for organic honey"');
    const reviewResult = await chatbot.processQuery(
      'What are reviews for organic honey', 
      sessionId, 
      userId
    );
    console.log('Response:', reviewResult.response.substring(0, 200) + '...');
    console.log('Response Type:', reviewResult.response_type);
    console.log('Confidence:', reviewResult.confidence);

    // Test 7: Recommendation Query
    console.log('\n7️⃣ Testing Recommendation Query:');
    console.log('Query: "Recommend some products"');
    const recommendationResult = await chatbot.processQuery(
      'Recommend some products', 
      sessionId, 
      userId
    );
    console.log('Response:', recommendationResult.response.substring(0, 200) + '...');
    console.log('Response Type:', recommendationResult.response_type);
    console.log('Confidence:', recommendationResult.confidence);

    // Test 8: General Question
    console.log('\n8️⃣ Testing General Question:');
    console.log('Query: "What can you help me with?"');
    const generalResult = await chatbot.processQuery(
      'What can you help me with?', 
      sessionId, 
      userId
    );
    console.log('Response:', generalResult.response.substring(0, 200) + '...');
    console.log('Response Type:', generalResult.response_type);
    console.log('Confidence:', generalResult.confidence);

    // Test Session History
    console.log('\n📜 Testing Session History:');
    const sessionHistory = await chatbot.getSessionHistory(sessionId, 5);
    console.log(`Session History: ${sessionHistory.length} queries found`);
    if (sessionHistory.length > 0) {
      console.log('Latest Query:', sessionHistory[0].query_text);
      console.log('Latest Response:', sessionHistory[0].response_text.substring(0, 100) + '...');
    }

    console.log('\n📊 Chatbot Test Summary:');
    console.log('=' .repeat(60));
    console.log('✅ All chatbot queries processed successfully');
    console.log('✅ Instacart product search working');
    console.log('✅ Inventory queries working');
    console.log('✅ Session history tracking working');
    console.log('⚠️  Amazon review queries return no data (expected)');
    console.log('⚠️  Demand forecast queries return no data (expected)');
    console.log('\n🎉 Chatbot testing completed successfully!');

  } catch (error) {
    console.error('❌ Chatbot test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n📡 Database connection closed.');
  }
}

// Run the test
testChatbotQueries();
