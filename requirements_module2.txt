# Module 2: Predictive Demand & Flow Engine Requirements
# =====================================================

# Core Data Science Libraries
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.4.0
seaborn>=0.11.0

# Time Series Forecasting
prophet>=1.1.0
scikit-learn>=1.0.0

# Interactive Visualizations
plotly>=5.0.0

# Additional Utilities
python-dateutil>=2.8.0
pytz>=2021.1

# Optional: External Data Integration
requests>=2.25.0
beautifulsoup4>=4.9.0

# Optional: Advanced Analytics
scipy>=1.7.0
statsmodels>=0.12.0

# Development and Testing
pytest>=6.0.0
jupyter>=1.0.0
