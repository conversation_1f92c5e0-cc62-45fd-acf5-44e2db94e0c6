@echo off
title Inventory Management System Startup
color 0A

echo.
echo ========================================
echo   🚀 INVENTORY MANAGEMENT SYSTEM 🚀
echo ========================================
echo.

echo 📋 Starting system components...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo ✅ Node.js and Python are available
echo.

REM Install dependencies if needed
if not exist node_modules (
    echo 📦 Installing frontend dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install frontend dependencies
        pause
        exit /b 1
    )
)

if not exist backend\node_modules (
    echo 📦 Installing backend dependencies...
    cd backend
    call npm install
    cd ..
    if %errorlevel% neq 0 (
        echo ❌ Failed to install backend dependencies
        pause
        exit /b 1
    )
)

REM Install Python dependencies
echo 📦 Installing Python dependencies...
pip install -r requirements_module2.txt >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Warning: Some Python dependencies may not have installed correctly
    echo This might affect demand forecasting functionality
)

echo.
echo 🗄️ Setting up database and initial data...
cd backend

REM Create admin user
call npm run create-admin >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Warning: Could not create admin user (may already exist)
)

REM Seed initial data
call npm run seed >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Warning: Could not seed data (may already exist)
)

cd ..

echo.
echo 🌐 Starting servers...
echo.

REM Start backend server in new window
echo 🔧 Starting backend server (Port 5000)...
start "Backend Server" cmd /k "cd backend && npm run dev"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend in new window
echo 🎨 Starting frontend application (Port 3000)...
start "Frontend App" cmd /k "npm start"

REM Wait for servers to start
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo   ✅ SYSTEM STARTUP COMPLETE!
echo ========================================
echo.
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend:  http://localhost:5000
echo.
echo 🔑 Login Credentials:
echo    Email:    <EMAIL>
echo    Password: admin123
echo.
echo 📊 Available Features:
echo    • Dashboard with real-time analytics
echo    • Inventory management
echo    • Demand forecasting (AI-powered)
echo    • Chatbot with Gemini AI
echo    • Amazon Fine Food Reviews analysis
echo    • Instacart Market Basket analysis
echo    • Supplier management
echo    • Order tracking
echo.
echo 🛠️ Troubleshooting:
echo    • If demand forecast doesn't work, run: node test-demand-forecast.js
echo    • To setup Amazon reviews, run: node setup-amazon-reviews.js
echo    • Check browser console for any errors
echo.
echo Press any key to run system diagnostics...
pause >nul

echo.
echo 🔍 Running system diagnostics...
node test-demand-forecast.js

echo.
echo 📊 Setting up Amazon Fine Food Reviews...
node setup-amazon-reviews.js

echo.
echo ========================================
echo   🎉 ALL SYSTEMS READY!
echo ========================================
echo.
echo Your inventory management system is now running!
echo Open http://localhost:3000 in your browser to get started.
echo.
pause
