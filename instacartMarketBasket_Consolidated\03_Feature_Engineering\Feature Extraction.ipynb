{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Feature Extraction"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import gc\n", "\n", "import category_encoders as ce\n", "\n", "root = 'C:/Data/instacart-market-basket-analysis/'"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["aisles = pd.read_csv(root + 'aisles.csv')\n", "\n", "departments = pd.read_csv(root + 'departments.csv')\n", "\n", "orders = pd.read_csv(root + 'orders.csv', \n", "                 dtype={\n", "                        'order_id': np.int32,\n", "                        'user_id': np.int64,\n", "                        'eval_set': 'category',\n", "                        'order_number': np.int16,\n", "                        'order_dow': np.int8,\n", "                        'order_hour_of_day': np.int8,\n", "                        'days_since_prior_order': np.float32})\n", "\n", "order_products_prior = pd.read_csv(root + 'order_products__prior.csv', \n", "                                 dtype={\n", "                                        'order_id': np.int32,\n", "                                        'product_id': np.uint16,\n", "                                        'add_to_cart_order': np.int16,\n", "                                        'reordered': np.int8})\n", "\n", "order_products_train = pd.read_csv(root + 'order_products__train.csv', \n", "                                 dtype={\n", "                                        'order_id': np.int32,\n", "                                        'product_id': np.uint16,\n", "                                        'add_to_cart_order': np.int16,\n", "                                        'reordered': np.int8})\n", "products = pd.read_csv(root + 'products.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Preparing Data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>product_id</th>\n", "      <th>add_to_cart_order</th>\n", "      <th>reordered</th>\n", "      <th>user_id</th>\n", "      <th>eval_set</th>\n", "      <th>order_number</th>\n", "      <th>order_dow</th>\n", "      <th>order_hour_of_day</th>\n", "      <th>days_since_prior_order</th>\n", "      <th>product_name</th>\n", "      <th>aisle_id</th>\n", "      <th>department_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>33120</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Organic Egg Whites</td>\n", "      <td>86</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>28985</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Michigan Organic Kale</td>\n", "      <td>83</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>9327</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>104</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>45918</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Coconut Butter</td>\n", "      <td>19</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>30035</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Natural Sweetener</td>\n", "      <td>17</td>\n", "      <td>13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   order_id  product_id  add_to_cart_order  reordered  user_id eval_set  \\\n", "0         2       33120                  1          1   202279    prior   \n", "1         2       28985                  2          1   202279    prior   \n", "2         2        9327                  3          0   202279    prior   \n", "3         2       45918                  4          1   202279    prior   \n", "4         2       30035                  5          0   202279    prior   \n", "\n", "   order_number  order_dow  order_hour_of_day  days_since_prior_order  \\\n", "0             3          5                  9                     8.0   \n", "1             3          5                  9                     8.0   \n", "2             3          5                  9                     8.0   \n", "3             3          5                  9                     8.0   \n", "4             3          5                  9                     8.0   \n", "\n", "            product_name  aisle_id  department_id  \n", "0     Organic Egg Whites        86             16  \n", "1  Michigan Organic Kale        83              4  \n", "2          <PERSON><PERSON><PERSON>       104             13  \n", "3         Coconut Butter        19             13  \n", "4      Natural Sweetener        17             13  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["prior_df = order_products_prior.merge(orders, on ='order_id', how='inner')\n", "prior_df = prior_df.merge(products, on = 'product_id', how = 'left')\n", "prior_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Features creation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Calculating how many times a user buy the product"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>product_id</th>\n", "      <th>add_to_cart_order</th>\n", "      <th>reordered</th>\n", "      <th>user_id</th>\n", "      <th>eval_set</th>\n", "      <th>order_number</th>\n", "      <th>order_dow</th>\n", "      <th>order_hour_of_day</th>\n", "      <th>days_since_prior_order</th>\n", "      <th>product_name</th>\n", "      <th>aisle_id</th>\n", "      <th>department_id</th>\n", "      <th>user_buy_product_times</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>33120</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Organic Egg Whites</td>\n", "      <td>86</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>28985</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Michigan Organic Kale</td>\n", "      <td>83</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>9327</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>104</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>45918</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Coconut Butter</td>\n", "      <td>19</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>30035</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Natural Sweetener</td>\n", "      <td>17</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   order_id  product_id  add_to_cart_order  reordered  user_id eval_set  \\\n", "0         2       33120                  1          1   202279    prior   \n", "1         2       28985                  2          1   202279    prior   \n", "2         2        9327                  3          0   202279    prior   \n", "3         2       45918                  4          1   202279    prior   \n", "4         2       30035                  5          0   202279    prior   \n", "\n", "   order_number  order_dow  order_hour_of_day  days_since_prior_order  \\\n", "0             3          5                  9                     8.0   \n", "1             3          5                  9                     8.0   \n", "2             3          5                  9                     8.0   \n", "3             3          5                  9                     8.0   \n", "4             3          5                  9                     8.0   \n", "\n", "            product_name  aisle_id  department_id  user_buy_product_times  \n", "0     Organic Egg Whites        86             16                       1  \n", "1  Michigan Organic Kale        83              4                       1  \n", "2          <PERSON><PERSON><PERSON>       104             13                       1  \n", "3         Coconut Butter        19             13                       1  \n", "4      Natural Sweetener        17             13                       1  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["prior_df['user_buy_product_times'] = prior_df.groupby(['user_id', 'product_id']).cumcount() + 1\n", "prior_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Product level features "]}, {"cell_type": "markdown", "metadata": {}, "source": ["(1) Product's average add-to-cart-order\n", "\n", "(2) Total times the product was ordered\n", "\n", "(3) Total times the product was reordered\n", "\n", "(4) Reorder percentage of a product\n", "\n", "(5) Total unique users of a product\n", "\n", "(6) Is the product Organic?\n", "\n", "(7) Percentage of users that buy the product second time"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["agg_dict1 = {'add_to_cart_order' : {'mean_add_to_cart_order':'mean'}, \n", "           'reordered' : {'total_orders':'count', 'total_reorders':'sum', 'reorder_percentage':'mean'},\n", "           'user_id': {'unique_users' :lambda x: x.nunique()},\n", "           'user_buy_product_times': {'order_first_time_total_cnt' : lambda x: sum(x==1), \n", "                                      'order_second_time_total_cnt' :lambda x: sum(x==2)},\n", "           'product_name':{'is_organic': lambda x: 1 if 'Organic' in x else 0}}"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Anaconda3\\lib\\site-packages\\pandas\\core\\groupby\\generic.py:1315: FutureWarning: using a dict with renaming is deprecated and will be removed in a future version\n", "  return super(DataFrameGroupBy, self).aggregate(arg, *args, **kwargs)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>mean_add_to_cart_order</th>\n", "      <th>total_orders</th>\n", "      <th>total_reorders</th>\n", "      <th>reorder_percentage</th>\n", "      <th>unique_users</th>\n", "      <th>order_first_time_total_cnt</th>\n", "      <th>order_second_time_total_cnt</th>\n", "      <th>is_organic</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>5.801836</td>\n", "      <td>1852</td>\n", "      <td>1136.0</td>\n", "      <td>0.613391</td>\n", "      <td>716</td>\n", "      <td>716</td>\n", "      <td>276</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>9.888889</td>\n", "      <td>90</td>\n", "      <td>12.0</td>\n", "      <td>0.133333</td>\n", "      <td>78</td>\n", "      <td>78</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>6.415162</td>\n", "      <td>277</td>\n", "      <td>203.0</td>\n", "      <td>0.732852</td>\n", "      <td>74</td>\n", "      <td>74</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>9.507599</td>\n", "      <td>329</td>\n", "      <td>147.0</td>\n", "      <td>0.446809</td>\n", "      <td>182</td>\n", "      <td>182</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>6.466667</td>\n", "      <td>15</td>\n", "      <td>9.0</td>\n", "      <td>0.600000</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   product_id  mean_add_to_cart_order  total_orders  total_reorders  \\\n", "0           1                5.801836          1852          1136.0   \n", "1           2                9.888889            90            12.0   \n", "2           3                6.415162           277           203.0   \n", "3           4                9.507599           329           147.0   \n", "4           5                6.466667            15             9.0   \n", "\n", "   reorder_percentage  unique_users  order_first_time_total_cnt  \\\n", "0            0.613391           716                         716   \n", "1            0.133333            78                          78   \n", "2            0.732852            74                          74   \n", "3            0.446809           182                         182   \n", "4            0.600000             6                           6   \n", "\n", "   order_second_time_total_cnt  is_organic  \n", "0                          276           0  \n", "1                            8           0  \n", "2                           36           0  \n", "3                           64           0  \n", "4                            4           0  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["prod_feats1 = prior_df.groupby('product_id').agg(agg_dict1)\n", "prod_feats1.columns = prod_feats1.columns.droplevel(0)\n", "prod_feats1.reset_index(inplace = True)\n", "prod_feats1.head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["prod_feats1['second_time_percent'] = prod_feats1.order_second_time_total_cnt/prod_feats1.order_first_time_total_cnt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Aisle and department features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["(8) Reorder percentage, Total orders and reorders of a product  aisle\n", "\n", "(9) Mean and std of aisle add-to-cart-order\n", "\n", "(10) Aisle unique users"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["agg_dict2 = {'add_to_cart_order' : {'aisle_mean_add_to_cart_order':'mean',\n", "                                   'aisle_std_add_to_cart_order':'std'}, \n", "           'reordered' : {'aisle_total_orders':'count', 'aisle_total_reorders':'sum', 'aisle_reorder_percentage':'mean'},\n", "           'user_id': {'aisle_unique_users' :lambda x: x.nunique()}}"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>aisle_id</th>\n", "      <th>aisle_mean_add_to_cart_order</th>\n", "      <th>aisle_std_add_to_cart_order</th>\n", "      <th>aisle_total_orders</th>\n", "      <th>aisle_total_reorders</th>\n", "      <th>aisle_reorder_percentage</th>\n", "      <th>aisle_unique_users</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>8.167640</td>\n", "      <td>7.104166</td>\n", "      <td>71928</td>\n", "      <td>42912.0</td>\n", "      <td>0.596597</td>\n", "      <td>20711</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>9.275497</td>\n", "      <td>7.473802</td>\n", "      <td>82491</td>\n", "      <td>40365.0</td>\n", "      <td>0.489326</td>\n", "      <td>31222</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>9.571935</td>\n", "      <td>7.899672</td>\n", "      <td>456386</td>\n", "      <td>272922.0</td>\n", "      <td>0.598007</td>\n", "      <td>63592</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>10.161450</td>\n", "      <td>7.745705</td>\n", "      <td>200687</td>\n", "      <td>98243.0</td>\n", "      <td>0.489533</td>\n", "      <td>53892</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>10.297600</td>\n", "      <td>8.187047</td>\n", "      <td>62510</td>\n", "      <td>17542.0</td>\n", "      <td>0.280627</td>\n", "      <td>32312</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   aisle_id  aisle_mean_add_to_cart_order  aisle_std_add_to_cart_order  \\\n", "0         1                      8.167640                     7.104166   \n", "1         2                      9.275497                     7.473802   \n", "2         3                      9.571935                     7.899672   \n", "3         4                     10.161450                     7.745705   \n", "4         5                     10.297600                     8.187047   \n", "\n", "   aisle_total_orders  aisle_total_reorders  aisle_reorder_percentage  \\\n", "0               71928               42912.0                  0.596597   \n", "1               82491               40365.0                  0.489326   \n", "2              456386              272922.0                  0.598007   \n", "3              200687               98243.0                  0.489533   \n", "4               62510               17542.0                  0.280627   \n", "\n", "   aisle_unique_users  \n", "0               20711  \n", "1               31222  \n", "2               63592  \n", "3               53892  \n", "4               32312  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["aisle_feats = prior_df.groupby('aisle_id').agg(agg_dict2)\n", "aisle_feats.columns = aisle_feats.columns.droplevel(0)\n", "aisle_feats.reset_index(inplace = True)\n", "aisle_feats.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**features**\n", "\n", "(10) Reorder percentage, Total orders and reorders of a product department\n", "\n", "(11) Mean and std of department add-to-cart-order\n", "\n", "(12) Department unique users"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["agg_dict3 = {'add_to_cart_order' : {'department_mean_add_to_cart_order':'mean',\n", "                                   'department_std_add_to_cart_order':'std'}, \n", "           'reordered' : {'department_total_orders':'count', 'department_total_reorders':'sum',\n", "                          'department_reorder_percentage':'mean'},\n", "           'user_id': {'department_unique_users' :lambda x: x.nunique()}}"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>department_id</th>\n", "      <th>department_mean_add_to_cart_order</th>\n", "      <th>department_std_add_to_cart_order</th>\n", "      <th>department_total_orders</th>\n", "      <th>department_total_reorders</th>\n", "      <th>department_reorder_percentage</th>\n", "      <th>department_unique_users</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>8.996414</td>\n", "      <td>7.393502</td>\n", "      <td>2236432</td>\n", "      <td>1211890.0</td>\n", "      <td>0.541885</td>\n", "      <td>163233</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>8.277645</td>\n", "      <td>7.526272</td>\n", "      <td>36291</td>\n", "      <td>14806.0</td>\n", "      <td>0.407980</td>\n", "      <td>17875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>8.084397</td>\n", "      <td>6.904849</td>\n", "      <td>1176787</td>\n", "      <td>739188.0</td>\n", "      <td>0.628141</td>\n", "      <td>140612</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>8.022875</td>\n", "      <td>6.658899</td>\n", "      <td>9479291</td>\n", "      <td>6160710.0</td>\n", "      <td>0.649913</td>\n", "      <td>193237</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>5.428346</td>\n", "      <td>5.778253</td>\n", "      <td>153696</td>\n", "      <td>87595.0</td>\n", "      <td>0.569924</td>\n", "      <td>15798</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   department_id  department_mean_add_to_cart_order  \\\n", "0              1                           8.996414   \n", "1              2                           8.277645   \n", "2              3                           8.084397   \n", "3              4                           8.022875   \n", "4              5                           5.428346   \n", "\n", "   department_std_add_to_cart_order  department_total_orders  \\\n", "0                          7.393502                  2236432   \n", "1                          7.526272                    36291   \n", "2                          6.904849                  1176787   \n", "3                          6.658899                  9479291   \n", "4                          5.778253                   153696   \n", "\n", "   department_total_reorders  department_reorder_percentage  \\\n", "0                  1211890.0                       0.541885   \n", "1                    14806.0                       0.407980   \n", "2                   739188.0                       0.628141   \n", "3                  6160710.0                       0.649913   \n", "4                    87595.0                       0.569924   \n", "\n", "   department_unique_users  \n", "0                   163233  \n", "1                    17875  \n", "2                   140612  \n", "3                   193237  \n", "4                    15798  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["dpt_feats = prior_df.groupby('department_id').agg(agg_dict3)\n", "dpt_feats.columns = dpt_feats.columns.droplevel(0)\n", "dpt_feats.reset_index(inplace = True)\n", "dpt_feats.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**features**\n", "\n", "(13) Binary encoding of aisle feature\n", "\n", "(14) Binary encoding of department feature"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>mean_add_to_cart_order</th>\n", "      <th>total_orders</th>\n", "      <th>total_reorders</th>\n", "      <th>reorder_percentage</th>\n", "      <th>unique_users</th>\n", "      <th>order_first_time_total_cnt</th>\n", "      <th>order_second_time_total_cnt</th>\n", "      <th>is_organic</th>\n", "      <th>second_time_percent</th>\n", "      <th>...</th>\n", "      <th>aisle_reorder_percentage</th>\n", "      <th>aisle_unique_users</th>\n", "      <th>aisle</th>\n", "      <th>department_mean_add_to_cart_order</th>\n", "      <th>department_std_add_to_cart_order</th>\n", "      <th>department_total_orders</th>\n", "      <th>department_total_reorders</th>\n", "      <th>department_reorder_percentage</th>\n", "      <th>department_unique_users</th>\n", "      <th>department</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>5.801836</td>\n", "      <td>1852</td>\n", "      <td>1136.0</td>\n", "      <td>0.613391</td>\n", "      <td>716</td>\n", "      <td>716</td>\n", "      <td>276</td>\n", "      <td>0</td>\n", "      <td>0.385475</td>\n", "      <td>...</td>\n", "      <td>0.548698</td>\n", "      <td>54202</td>\n", "      <td>cookies cakes</td>\n", "      <td>9.187743</td>\n", "      <td>7.692492</td>\n", "      <td>2887550</td>\n", "      <td>1657973.0</td>\n", "      <td>0.574180</td>\n", "      <td>174219</td>\n", "      <td>snacks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>9.888889</td>\n", "      <td>90</td>\n", "      <td>12.0</td>\n", "      <td>0.133333</td>\n", "      <td>78</td>\n", "      <td>78</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>0.102564</td>\n", "      <td>...</td>\n", "      <td>0.152391</td>\n", "      <td>76402</td>\n", "      <td>spices seasonings</td>\n", "      <td>9.593425</td>\n", "      <td>7.875241</td>\n", "      <td>1875577</td>\n", "      <td>650301.0</td>\n", "      <td>0.346721</td>\n", "      <td>172755</td>\n", "      <td>pantry</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>6.415162</td>\n", "      <td>277</td>\n", "      <td>203.0</td>\n", "      <td>0.732852</td>\n", "      <td>74</td>\n", "      <td>74</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "      <td>0.486486</td>\n", "      <td>...</td>\n", "      <td>0.527615</td>\n", "      <td>53197</td>\n", "      <td>tea</td>\n", "      <td>6.976699</td>\n", "      <td>6.711172</td>\n", "      <td>2690129</td>\n", "      <td>1757892.0</td>\n", "      <td>0.653460</td>\n", "      <td>172795</td>\n", "      <td>beverages</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>9.507599</td>\n", "      <td>329</td>\n", "      <td>147.0</td>\n", "      <td>0.446809</td>\n", "      <td>182</td>\n", "      <td>182</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0.351648</td>\n", "      <td>...</td>\n", "      <td>0.556655</td>\n", "      <td>58749</td>\n", "      <td>frozen meals</td>\n", "      <td>8.996414</td>\n", "      <td>7.393502</td>\n", "      <td>2236432</td>\n", "      <td>1211890.0</td>\n", "      <td>0.541885</td>\n", "      <td>163233</td>\n", "      <td>frozen</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>6.466667</td>\n", "      <td>15</td>\n", "      <td>9.0</td>\n", "      <td>0.600000</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0.666667</td>\n", "      <td>...</td>\n", "      <td>0.280627</td>\n", "      <td>32312</td>\n", "      <td>marinades meat preparation</td>\n", "      <td>9.593425</td>\n", "      <td>7.875241</td>\n", "      <td>1875577</td>\n", "      <td>650301.0</td>\n", "      <td>0.346721</td>\n", "      <td>172755</td>\n", "      <td>pantry</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 27 columns</p>\n", "</div>"], "text/plain": ["   product_id  mean_add_to_cart_order  total_orders  total_reorders  \\\n", "0           1                5.801836          1852          1136.0   \n", "1           2                9.888889            90            12.0   \n", "2           3                6.415162           277           203.0   \n", "3           4                9.507599           329           147.0   \n", "4           5                6.466667            15             9.0   \n", "\n", "   reorder_percentage  unique_users  order_first_time_total_cnt  \\\n", "0            0.613391           716                         716   \n", "1            0.133333            78                          78   \n", "2            0.732852            74                          74   \n", "3            0.446809           182                         182   \n", "4            0.600000             6                           6   \n", "\n", "   order_second_time_total_cnt  is_organic  second_time_percent  ...  \\\n", "0                          276           0             0.385475  ...   \n", "1                            8           0             0.102564  ...   \n", "2                           36           0             0.486486  ...   \n", "3                           64           0             0.351648  ...   \n", "4                            4           0             0.666667  ...   \n", "\n", "  aisle_reorder_percentage  aisle_unique_users                       aisle  \\\n", "0                 0.548698               54202               cookies cakes   \n", "1                 0.152391               76402           spices seasonings   \n", "2                 0.527615               53197                         tea   \n", "3                 0.556655               58749                frozen meals   \n", "4                 0.280627               32312  marinades meat preparation   \n", "\n", "   department_mean_add_to_cart_order  department_std_add_to_cart_order  \\\n", "0                           9.187743                          7.692492   \n", "1                           9.593425                          7.875241   \n", "2                           6.976699                          6.711172   \n", "3                           8.996414                          7.393502   \n", "4                           9.593425                          7.875241   \n", "\n", "   department_total_orders  department_total_reorders  \\\n", "0                  2887550                  1657973.0   \n", "1                  1875577                   650301.0   \n", "2                  2690129                  1757892.0   \n", "3                  2236432                  1211890.0   \n", "4                  1875577                   650301.0   \n", "\n", "   department_reorder_percentage  department_unique_users department  \n", "0                       0.574180                   174219     snacks  \n", "1                       0.346721                   172755     pantry  \n", "2                       0.653460                   172795  beverages  \n", "3                       0.541885                   163233     frozen  \n", "4                       0.346721                   172755     pantry  \n", "\n", "[5 rows x 27 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["prod_feats1 = prod_feats1.merge(products, on = 'product_id', how = 'left')\n", "prod_feats1 = prod_feats1.merge(aisle_feats, on = 'aisle_id', how = 'left')\n", "prod_feats1 = prod_feats1.merge(aisles, on = 'aisle_id', how = 'left')\n", "prod_feats1 = prod_feats1.merge(dpt_feats, on = 'department_id', how = 'left')\n", "prod_feats1 = prod_feats1.merge(departments, on = 'department_id', how = 'left')\n", "prod_feats1.head()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>mean_add_to_cart_order</th>\n", "      <th>total_orders</th>\n", "      <th>total_reorders</th>\n", "      <th>reorder_percentage</th>\n", "      <th>unique_users</th>\n", "      <th>order_first_time_total_cnt</th>\n", "      <th>order_second_time_total_cnt</th>\n", "      <th>is_organic</th>\n", "      <th>second_time_percent</th>\n", "      <th>...</th>\n", "      <th>aisle_reorder_percentage</th>\n", "      <th>aisle_unique_users</th>\n", "      <th>aisle</th>\n", "      <th>department_mean_add_to_cart_order</th>\n", "      <th>department_std_add_to_cart_order</th>\n", "      <th>department_total_orders</th>\n", "      <th>department_total_reorders</th>\n", "      <th>department_reorder_percentage</th>\n", "      <th>department_unique_users</th>\n", "      <th>department</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>5.801836</td>\n", "      <td>1852</td>\n", "      <td>1136.0</td>\n", "      <td>0.613391</td>\n", "      <td>716</td>\n", "      <td>716</td>\n", "      <td>276</td>\n", "      <td>0</td>\n", "      <td>0.385475</td>\n", "      <td>...</td>\n", "      <td>0.548698</td>\n", "      <td>54202</td>\n", "      <td>cookies cakes</td>\n", "      <td>9.187743</td>\n", "      <td>7.692492</td>\n", "      <td>2887550</td>\n", "      <td>1657973.0</td>\n", "      <td>0.574180</td>\n", "      <td>174219</td>\n", "      <td>snacks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>9.888889</td>\n", "      <td>90</td>\n", "      <td>12.0</td>\n", "      <td>0.133333</td>\n", "      <td>78</td>\n", "      <td>78</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>0.102564</td>\n", "      <td>...</td>\n", "      <td>0.152391</td>\n", "      <td>76402</td>\n", "      <td>spices seasonings</td>\n", "      <td>9.593425</td>\n", "      <td>7.875241</td>\n", "      <td>1875577</td>\n", "      <td>650301.0</td>\n", "      <td>0.346721</td>\n", "      <td>172755</td>\n", "      <td>pantry</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>6.415162</td>\n", "      <td>277</td>\n", "      <td>203.0</td>\n", "      <td>0.732852</td>\n", "      <td>74</td>\n", "      <td>74</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "      <td>0.486486</td>\n", "      <td>...</td>\n", "      <td>0.527615</td>\n", "      <td>53197</td>\n", "      <td>tea</td>\n", "      <td>6.976699</td>\n", "      <td>6.711172</td>\n", "      <td>2690129</td>\n", "      <td>1757892.0</td>\n", "      <td>0.653460</td>\n", "      <td>172795</td>\n", "      <td>beverages</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>9.507599</td>\n", "      <td>329</td>\n", "      <td>147.0</td>\n", "      <td>0.446809</td>\n", "      <td>182</td>\n", "      <td>182</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0.351648</td>\n", "      <td>...</td>\n", "      <td>0.556655</td>\n", "      <td>58749</td>\n", "      <td>frozen meals</td>\n", "      <td>8.996414</td>\n", "      <td>7.393502</td>\n", "      <td>2236432</td>\n", "      <td>1211890.0</td>\n", "      <td>0.541885</td>\n", "      <td>163233</td>\n", "      <td>frozen</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>6.466667</td>\n", "      <td>15</td>\n", "      <td>9.0</td>\n", "      <td>0.600000</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0.666667</td>\n", "      <td>...</td>\n", "      <td>0.280627</td>\n", "      <td>32312</td>\n", "      <td>marinades meat preparation</td>\n", "      <td>9.593425</td>\n", "      <td>7.875241</td>\n", "      <td>1875577</td>\n", "      <td>650301.0</td>\n", "      <td>0.346721</td>\n", "      <td>172755</td>\n", "      <td>pantry</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["   product_id  mean_add_to_cart_order  total_orders  total_reorders  \\\n", "0           1                5.801836          1852          1136.0   \n", "1           2                9.888889            90            12.0   \n", "2           3                6.415162           277           203.0   \n", "3           4                9.507599           329           147.0   \n", "4           5                6.466667            15             9.0   \n", "\n", "   reorder_percentage  unique_users  order_first_time_total_cnt  \\\n", "0            0.613391           716                         716   \n", "1            0.133333            78                          78   \n", "2            0.732852            74                          74   \n", "3            0.446809           182                         182   \n", "4            0.600000             6                           6   \n", "\n", "   order_second_time_total_cnt  is_organic  second_time_percent  ...  \\\n", "0                          276           0             0.385475  ...   \n", "1                            8           0             0.102564  ...   \n", "2                           36           0             0.486486  ...   \n", "3                           64           0             0.351648  ...   \n", "4                            4           0             0.666667  ...   \n", "\n", "   aisle_reorder_percentage  aisle_unique_users                       aisle  \\\n", "0                  0.548698               54202               cookies cakes   \n", "1                  0.152391               76402           spices seasonings   \n", "2                  0.527615               53197                         tea   \n", "3                  0.556655               58749                frozen meals   \n", "4                  0.280627               32312  marinades meat preparation   \n", "\n", "   department_mean_add_to_cart_order  department_std_add_to_cart_order  \\\n", "0                           9.187743                          7.692492   \n", "1                           9.593425                          7.875241   \n", "2                           6.976699                          6.711172   \n", "3                           8.996414                          7.393502   \n", "4                           9.593425                          7.875241   \n", "\n", "   department_total_orders department_total_reorders  \\\n", "0                  2887550                 1657973.0   \n", "1                  1875577                  650301.0   \n", "2                  2690129                 1757892.0   \n", "3                  2236432                 1211890.0   \n", "4                  1875577                  650301.0   \n", "\n", "   department_reorder_percentage  department_unique_users  department  \n", "0                       0.574180                   174219      snacks  \n", "1                       0.346721                   172755      pantry  \n", "2                       0.653460                   172795   beverages  \n", "3                       0.541885                   163233      frozen  \n", "4                       0.346721                   172755      pantry  \n", "\n", "[5 rows x 24 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["prod_feats1.drop(['product_name', 'aisle_id', 'department_id'], axis = 1, inplace = True)\n", "prod_feats1.head()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["(49677, 24)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["prod_feats1.shape"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["product_id                            uint64\n", "mean_add_to_cart_order               float64\n", "total_orders                           int64\n", "total_reorders                       float64\n", "reorder_percentage                   float64\n", "unique_users                           int64\n", "order_first_time_total_cnt             int64\n", "order_second_time_total_cnt            int64\n", "is_organic                             int64\n", "second_time_percent                  float64\n", "aisle_mean_add_to_cart_order         float64\n", "aisle_std_add_to_cart_order          float64\n", "aisle_total_orders                     int64\n", "aisle_total_reorders                 float64\n", "aisle_reorder_percentage             float64\n", "aisle_unique_users                     int64\n", "aisle                                 object\n", "department_mean_add_to_cart_order    float64\n", "department_std_add_to_cart_order     float64\n", "department_total_orders                int64\n", "department_total_reorders            float64\n", "department_reorder_percentage        float64\n", "department_unique_users                int64\n", "department                            object\n", "dtype: object"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["prod_feats1.dtypes"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["encoder= ce.BinaryEncoder(cols=['aisle', 'department'],return_df=True)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>mean_add_to_cart_order</th>\n", "      <th>total_orders</th>\n", "      <th>total_reorders</th>\n", "      <th>reorder_percentage</th>\n", "      <th>unique_users</th>\n", "      <th>order_first_time_total_cnt</th>\n", "      <th>order_second_time_total_cnt</th>\n", "      <th>is_organic</th>\n", "      <th>second_time_percent</th>\n", "      <th>...</th>\n", "      <th>department_total_orders</th>\n", "      <th>department_total_reorders</th>\n", "      <th>department_reorder_percentage</th>\n", "      <th>department_unique_users</th>\n", "      <th>department_0</th>\n", "      <th>department_1</th>\n", "      <th>department_2</th>\n", "      <th>department_3</th>\n", "      <th>department_4</th>\n", "      <th>department_5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>5.801836</td>\n", "      <td>1852</td>\n", "      <td>1136.0</td>\n", "      <td>0.613391</td>\n", "      <td>716</td>\n", "      <td>716</td>\n", "      <td>276</td>\n", "      <td>0</td>\n", "      <td>0.385475</td>\n", "      <td>...</td>\n", "      <td>2887550</td>\n", "      <td>1657973.0</td>\n", "      <td>0.574180</td>\n", "      <td>174219</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>9.888889</td>\n", "      <td>90</td>\n", "      <td>12.0</td>\n", "      <td>0.133333</td>\n", "      <td>78</td>\n", "      <td>78</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>0.102564</td>\n", "      <td>...</td>\n", "      <td>1875577</td>\n", "      <td>650301.0</td>\n", "      <td>0.346721</td>\n", "      <td>172755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>6.415162</td>\n", "      <td>277</td>\n", "      <td>203.0</td>\n", "      <td>0.732852</td>\n", "      <td>74</td>\n", "      <td>74</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "      <td>0.486486</td>\n", "      <td>...</td>\n", "      <td>2690129</td>\n", "      <td>1757892.0</td>\n", "      <td>0.653460</td>\n", "      <td>172795</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>9.507599</td>\n", "      <td>329</td>\n", "      <td>147.0</td>\n", "      <td>0.446809</td>\n", "      <td>182</td>\n", "      <td>182</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0.351648</td>\n", "      <td>...</td>\n", "      <td>2236432</td>\n", "      <td>1211890.0</td>\n", "      <td>0.541885</td>\n", "      <td>163233</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>6.466667</td>\n", "      <td>15</td>\n", "      <td>9.0</td>\n", "      <td>0.600000</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0.666667</td>\n", "      <td>...</td>\n", "      <td>1875577</td>\n", "      <td>650301.0</td>\n", "      <td>0.346721</td>\n", "      <td>172755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 37 columns</p>\n", "</div>"], "text/plain": ["   product_id  mean_add_to_cart_order  total_orders  total_reorders  \\\n", "0           1                5.801836          1852          1136.0   \n", "1           2                9.888889            90            12.0   \n", "2           3                6.415162           277           203.0   \n", "3           4                9.507599           329           147.0   \n", "4           5                6.466667            15             9.0   \n", "\n", "   reorder_percentage  unique_users  order_first_time_total_cnt  \\\n", "0            0.613391           716                         716   \n", "1            0.133333            78                          78   \n", "2            0.732852            74                          74   \n", "3            0.446809           182                         182   \n", "4            0.600000             6                           6   \n", "\n", "   order_second_time_total_cnt  is_organic  second_time_percent  ...  \\\n", "0                          276           0             0.385475  ...   \n", "1                            8           0             0.102564  ...   \n", "2                           36           0             0.486486  ...   \n", "3                           64           0             0.351648  ...   \n", "4                            4           0             0.666667  ...   \n", "\n", "   department_total_orders  department_total_reorders  \\\n", "0                  2887550                  1657973.0   \n", "1                  1875577                   650301.0   \n", "2                  2690129                  1757892.0   \n", "3                  2236432                  1211890.0   \n", "4                  1875577                   650301.0   \n", "\n", "   department_reorder_percentage  department_unique_users  department_0  \\\n", "0                       0.574180                   174219             0   \n", "1                       0.346721                   172755             0   \n", "2                       0.653460                   172795             0   \n", "3                       0.541885                   163233             0   \n", "4                       0.346721                   172755             0   \n", "\n", "   department_1  department_2  department_3  department_4  department_5  \n", "0             0             0             0             0             1  \n", "1             0             0             0             1             0  \n", "2             0             0             0             1             1  \n", "3             0             0             1             0             0  \n", "4             0             0             0             1             0  \n", "\n", "[5 rows x 37 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["prod_feats1 = encoder.fit_transform(prod_feats1)\n", "prod_feats1.head()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["(49677, 37)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["prod_feats1.shape"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['product_id', 'mean_add_to_cart_order', 'total_orders',\n", "       'total_reorders', 'reorder_percentage', 'unique_users',\n", "       'order_first_time_total_cnt', 'order_second_time_total_cnt',\n", "       'is_organic', 'second_time_percent', 'aisle_mean_add_to_cart_order',\n", "       'aisle_std_add_to_cart_order', 'aisle_total_orders',\n", "       'aisle_total_reorders', 'aisle_reorder_percentage',\n", "       'aisle_unique_users', 'aisle_0', 'aisle_1', 'aisle_2', 'aisle_3',\n", "       'aisle_4', 'aisle_5', 'aisle_6', 'aisle_7', 'aisle_8',\n", "       'department_mean_add_to_cart_order', 'department_std_add_to_cart_order',\n", "       'department_total_orders', 'department_total_reorders',\n", "       'department_reorder_percentage', 'department_unique_users',\n", "       'department_0', 'department_1', 'department_2', 'department_3',\n", "       'department_4', 'department_5'],\n", "      dtype='object')"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["prod_feats1.columns"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["prod_feats1.isnull().any().any()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["3084"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# free some memory\n", "del aisle_feats, dpt_feats, aisles, departments\n", "gc.collect()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### User level features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["(15) User's average and std day-of-week of order\n", "\n", "(16) User's average and std hour-of-day of order\n", "\n", "(17) User's average and std days-since-prior-order\n", "\n", "(18) Total orders by a user\n", "\n", "(19) Total products user has bought\n", "\n", "(20) Total unique products user has bought\n", "\n", "(21) user's total reordered products\n", "\n", "(22) User's overall reorder percentage"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["order_id                  False\n", "product_id                False\n", "add_to_cart_order         False\n", "reordered                 False\n", "user_id                   False\n", "eval_set                  False\n", "order_number              False\n", "order_dow                 False\n", "order_hour_of_day         False\n", "days_since_prior_order     True\n", "product_name              False\n", "aisle_id                  False\n", "department_id             False\n", "user_buy_product_times    False\n", "dtype: bool"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["prior_df.isnull().any()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# when no prior order, the value is null. Imputing as 0\n", "prior_df.days_since_prior_order = prior_df.days_since_prior_order.fillna(0)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>product_id</th>\n", "      <th>add_to_cart_order</th>\n", "      <th>reordered</th>\n", "      <th>user_id</th>\n", "      <th>eval_set</th>\n", "      <th>order_number</th>\n", "      <th>order_dow</th>\n", "      <th>order_hour_of_day</th>\n", "      <th>days_since_prior_order</th>\n", "      <th>product_name</th>\n", "      <th>aisle_id</th>\n", "      <th>department_id</th>\n", "      <th>user_buy_product_times</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>33120</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Organic Egg Whites</td>\n", "      <td>86</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>28985</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Michigan Organic Kale</td>\n", "      <td>83</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>9327</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>104</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>45918</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Coconut Butter</td>\n", "      <td>19</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>30035</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>202279</td>\n", "      <td>prior</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>8.0</td>\n", "      <td>Natural Sweetener</td>\n", "      <td>17</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   order_id  product_id  add_to_cart_order  reordered  user_id eval_set  \\\n", "0         2       33120                  1          1   202279    prior   \n", "1         2       28985                  2          1   202279    prior   \n", "2         2        9327                  3          0   202279    prior   \n", "3         2       45918                  4          1   202279    prior   \n", "4         2       30035                  5          0   202279    prior   \n", "\n", "   order_number  order_dow  order_hour_of_day  days_since_prior_order  \\\n", "0             3          5                  9                     8.0   \n", "1             3          5                  9                     8.0   \n", "2             3          5                  9                     8.0   \n", "3             3          5                  9                     8.0   \n", "4             3          5                  9                     8.0   \n", "\n", "            product_name  aisle_id  department_id  user_buy_product_times  \n", "0     Organic Egg Whites        86             16                       1  \n", "1  Michigan Organic Kale        83              4                       1  \n", "2          <PERSON><PERSON><PERSON>       104             13                       1  \n", "3         Coconut Butter        19             13                       1  \n", "4      Natural Sweetener        17             13                       1  "]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["prior_df.head()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["agg_dict4 = {'order_dow': {'avg_dow':'mean', 'std_dow':'std'},\n", "           'order_hour_of_day': {'avg_doh':'mean', 'std_doh':'std'},\n", "           'days_since_prior_order': {'avg_since_order':'mean', 'std_since_order':'std'},\n", "           'order_number': {'total_orders_by_user': lambda x: x.nunique()},\n", "           'product_id': {'total_products_by_user': 'count',\n", "                         'total_unique_product_by_user': lambda x: x.nunique()},\n", "           'reordered': {'total_reorders_by_user':'sum', \n", "                        'reorder_propotion_by_user':'mean'}}"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Anaconda3\\lib\\site-packages\\pandas\\core\\groupby\\generic.py:1315: FutureWarning: using a dict with renaming is deprecated and will be removed in a future version\n", "  return super(DataFrameGroupBy, self).aggregate(arg, *args, **kwargs)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>avg_dow</th>\n", "      <th>std_dow</th>\n", "      <th>avg_doh</th>\n", "      <th>std_doh</th>\n", "      <th>avg_since_order</th>\n", "      <th>std_since_order</th>\n", "      <th>total_orders_by_user</th>\n", "      <th>total_products_by_user</th>\n", "      <th>total_unique_product_by_user</th>\n", "      <th>total_reorders_by_user</th>\n", "      <th>reorder_propotion_by_user</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2.644068</td>\n", "      <td>1.256194</td>\n", "      <td>10.542373</td>\n", "      <td>3.500355</td>\n", "      <td>18.542374</td>\n", "      <td>10.559066</td>\n", "      <td>10</td>\n", "      <td>59</td>\n", "      <td>18</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2.005128</td>\n", "      <td>0.971222</td>\n", "      <td>10.441026</td>\n", "      <td>1.649854</td>\n", "      <td>14.902564</td>\n", "      <td>9.671712</td>\n", "      <td>14</td>\n", "      <td>195</td>\n", "      <td>102</td>\n", "      <td>93.0</td>\n", "      <td>0.476923</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1.011364</td>\n", "      <td>1.245630</td>\n", "      <td>16.352273</td>\n", "      <td>1.454599</td>\n", "      <td>10.181818</td>\n", "      <td>5.867395</td>\n", "      <td>12</td>\n", "      <td>88</td>\n", "      <td>33</td>\n", "      <td>55.0</td>\n", "      <td>0.625000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>4.722222</td>\n", "      <td>0.826442</td>\n", "      <td>13.111111</td>\n", "      <td>1.745208</td>\n", "      <td>11.944445</td>\n", "      <td>9.973330</td>\n", "      <td>5</td>\n", "      <td>18</td>\n", "      <td>17</td>\n", "      <td>1.0</td>\n", "      <td>0.055556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1.621622</td>\n", "      <td>1.276961</td>\n", "      <td>15.729730</td>\n", "      <td>2.588958</td>\n", "      <td>10.189189</td>\n", "      <td>7.600577</td>\n", "      <td>4</td>\n", "      <td>37</td>\n", "      <td>23</td>\n", "      <td>14.0</td>\n", "      <td>0.378378</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id   avg_dow   std_dow    avg_doh   std_doh  avg_since_order  \\\n", "0        1  2.644068  1.256194  10.542373  3.500355        18.542374   \n", "1        2  2.005128  0.971222  10.441026  1.649854        14.902564   \n", "2        3  1.011364  1.245630  16.352273  1.454599        10.181818   \n", "3        4  4.722222  0.826442  13.111111  1.745208        11.944445   \n", "4        5  1.621622  1.276961  15.729730  2.588958        10.189189   \n", "\n", "   std_since_order  total_orders_by_user  total_products_by_user  \\\n", "0        10.559066                    10                      59   \n", "1         9.671712                    14                     195   \n", "2         5.867395                    12                      88   \n", "3         9.973330                     5                      18   \n", "4         7.600577                     4                      37   \n", "\n", "   total_unique_product_by_user  total_reorders_by_user  \\\n", "0                            18                    41.0   \n", "1                           102                    93.0   \n", "2                            33                    55.0   \n", "3                            17                     1.0   \n", "4                            23                    14.0   \n", "\n", "   reorder_propotion_by_user  \n", "0                   0.694915  \n", "1                   0.476923  \n", "2                   0.625000  \n", "3                   0.055556  \n", "4                   0.378378  "]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["user_feats = prior_df.groupby('user_id').agg(agg_dict4)\n", "user_feats.columns = user_feats.columns.droplevel(0)\n", "user_feats.reset_index(inplace = True)\n", "user_feats.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**features**\n", "\n", "(23) Average order size of a user\n", "\n", "(24) User's mean of reordered items of all orders"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>order_number</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>6</td>\n", "      <td>0.500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>0.600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "      <td>1.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>8</td>\n", "      <td>0.625</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  order_number  average_order_size  reorder_in_order\n", "0        1             1                   5             0.000\n", "1        1             2                   6             0.500\n", "2        1             3                   5             0.600\n", "3        1             4                   5             1.000\n", "4        1             5                   8             0.625"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["agg_dict5 = {'reordered': {'average_order_size':'count', \n", "                        'reorder_in_order':'mean'}}\n", "\n", "user_feats2 = prior_df.groupby(['user_id', 'order_number']).agg(agg_dict5)\n", "user_feats2.columns = user_feats2.columns.droplevel(0)\n", "user_feats2.reset_index(inplace = True)\n", "user_feats2.head()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>5.900000</td>\n", "      <td>0.705833</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>13.928571</td>\n", "      <td>0.447961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7.333333</td>\n", "      <td>0.658817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>3.600000</td>\n", "      <td>0.028571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>9.250000</td>\n", "      <td>0.377778</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  average_order_size  reorder_in_order\n", "0        1            5.900000          0.705833\n", "1        2           13.928571          0.447961\n", "2        3            7.333333          0.658817\n", "3        4            3.600000          0.028571\n", "4        5            9.250000          0.377778"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["user_feats3 = user_feats2.groupby('user_id').agg({'average_order_size' : 'mean', \n", "                                   'reorder_in_order':'mean'})\n", "user_feats3 = user_feats3.reset_index()\n", "user_feats3.head()"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>avg_dow</th>\n", "      <th>std_dow</th>\n", "      <th>avg_doh</th>\n", "      <th>std_doh</th>\n", "      <th>avg_since_order</th>\n", "      <th>std_since_order</th>\n", "      <th>total_orders_by_user</th>\n", "      <th>total_products_by_user</th>\n", "      <th>total_unique_product_by_user</th>\n", "      <th>total_reorders_by_user</th>\n", "      <th>reorder_propotion_by_user</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2.644068</td>\n", "      <td>1.256194</td>\n", "      <td>10.542373</td>\n", "      <td>3.500355</td>\n", "      <td>18.542374</td>\n", "      <td>10.559066</td>\n", "      <td>10</td>\n", "      <td>59</td>\n", "      <td>18</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.900000</td>\n", "      <td>0.705833</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2.005128</td>\n", "      <td>0.971222</td>\n", "      <td>10.441026</td>\n", "      <td>1.649854</td>\n", "      <td>14.902564</td>\n", "      <td>9.671712</td>\n", "      <td>14</td>\n", "      <td>195</td>\n", "      <td>102</td>\n", "      <td>93.0</td>\n", "      <td>0.476923</td>\n", "      <td>13.928571</td>\n", "      <td>0.447961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1.011364</td>\n", "      <td>1.245630</td>\n", "      <td>16.352273</td>\n", "      <td>1.454599</td>\n", "      <td>10.181818</td>\n", "      <td>5.867395</td>\n", "      <td>12</td>\n", "      <td>88</td>\n", "      <td>33</td>\n", "      <td>55.0</td>\n", "      <td>0.625000</td>\n", "      <td>7.333333</td>\n", "      <td>0.658817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>4.722222</td>\n", "      <td>0.826442</td>\n", "      <td>13.111111</td>\n", "      <td>1.745208</td>\n", "      <td>11.944445</td>\n", "      <td>9.973330</td>\n", "      <td>5</td>\n", "      <td>18</td>\n", "      <td>17</td>\n", "      <td>1.0</td>\n", "      <td>0.055556</td>\n", "      <td>3.600000</td>\n", "      <td>0.028571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1.621622</td>\n", "      <td>1.276961</td>\n", "      <td>15.729730</td>\n", "      <td>2.588958</td>\n", "      <td>10.189189</td>\n", "      <td>7.600577</td>\n", "      <td>4</td>\n", "      <td>37</td>\n", "      <td>23</td>\n", "      <td>14.0</td>\n", "      <td>0.378378</td>\n", "      <td>9.250000</td>\n", "      <td>0.377778</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id   avg_dow   std_dow    avg_doh   std_doh  avg_since_order  \\\n", "0        1  2.644068  1.256194  10.542373  3.500355        18.542374   \n", "1        2  2.005128  0.971222  10.441026  1.649854        14.902564   \n", "2        3  1.011364  1.245630  16.352273  1.454599        10.181818   \n", "3        4  4.722222  0.826442  13.111111  1.745208        11.944445   \n", "4        5  1.621622  1.276961  15.729730  2.588958        10.189189   \n", "\n", "   std_since_order  total_orders_by_user  total_products_by_user  \\\n", "0        10.559066                    10                      59   \n", "1         9.671712                    14                     195   \n", "2         5.867395                    12                      88   \n", "3         9.973330                     5                      18   \n", "4         7.600577                     4                      37   \n", "\n", "   total_unique_product_by_user  total_reorders_by_user  \\\n", "0                            18                    41.0   \n", "1                           102                    93.0   \n", "2                            33                    55.0   \n", "3                            17                     1.0   \n", "4                            23                    14.0   \n", "\n", "   reorder_propotion_by_user  average_order_size  reorder_in_order  \n", "0                   0.694915            5.900000          0.705833  \n", "1                   0.476923           13.928571          0.447961  \n", "2                   0.625000            7.333333          0.658817  \n", "3                   0.055556            3.600000          0.028571  \n", "4                   0.378378            9.250000          0.377778  "]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["user_feats = user_feats.merge(user_feats3, on = 'user_id', how = 'left')\n", "user_feats.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**features**\n", "\n", "(25) Percentage of reordered itmes in user's last three orders\n", "\n", "(26) Total orders in user's last three orders"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Last 3 orders of a user"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>level_1</th>\n", "      <th>order_number</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>22</td>\n", "      <td>13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  level_1  order_number\n", "0        1        9            10\n", "1        1        8             9\n", "2        1        7             8\n", "3        2       23            14\n", "4        2       22            13"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["last_three_orders = user_feats2.groupby('user_id')['order_number'].nlargest(3).reset_index()\n", "last_three_orders.head()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>order_number</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>level_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>6</td>\n", "      <td>0.666667</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>6</td>\n", "      <td>1.000000</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>12</td>\n", "      <td>19</td>\n", "      <td>0.578947</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>13</td>\n", "      <td>9</td>\n", "      <td>0.000000</td>\n", "      <td>22</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  order_number  average_order_size  reorder_in_order  level_1\n", "0        1             8                   6          0.666667        7\n", "1        1             9                   6          1.000000        8\n", "2        1            10                   9          0.666667        9\n", "3        2            12                  19          0.578947       21\n", "4        2            13                   9          0.000000       22"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["last_three_orders = user_feats2.merge(last_three_orders, on = ['user_id', 'order_number'], how = 'inner')\n", "last_three_orders.head()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["last_three_orders['rank'] = last_three_orders.groupby(\"user_id\")[\"order_number\"].rank(\"dense\", ascending=True)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>orders_3</th>\n", "      <th>orders_2</th>\n", "      <th>orders_1</th>\n", "      <th>reorder_3</th>\n", "      <th>reorder_2</th>\n", "      <th>reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>19</td>\n", "      <td>9</td>\n", "      <td>16</td>\n", "      <td>0.578947</td>\n", "      <td>0.0</td>\n", "      <td>0.625000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>0.833333</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>0.142857</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>12</td>\n", "      <td>0.444444</td>\n", "      <td>0.4</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  orders_3  orders_2  orders_1  reorder_3  reorder_2  reorder_1\n", "0        1         6         6         9   0.666667        1.0   0.666667\n", "1        2        19         9        16   0.578947        0.0   0.625000\n", "2        3         6         5         6   0.833333        1.0   1.000000\n", "3        4         7         2         3   0.142857        0.0   0.000000\n", "4        5         9         5        12   0.444444        0.4   0.666667"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["last_order_feats = last_three_orders.pivot_table(index = 'user_id', columns = ['rank'], \\\n", "                                                 values=['average_order_size', 'reorder_in_order']).\\\n", "                                                reset_index(drop = False)\n", "last_order_feats.columns = ['user_id','orders_3', 'orders_2', 'orders_1', 'reorder_3', 'reorder_2', 'reorder_1']\n", "last_order_feats.head()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>avg_dow</th>\n", "      <th>std_dow</th>\n", "      <th>avg_doh</th>\n", "      <th>std_doh</th>\n", "      <th>avg_since_order</th>\n", "      <th>std_since_order</th>\n", "      <th>total_orders_by_user</th>\n", "      <th>total_products_by_user</th>\n", "      <th>total_unique_product_by_user</th>\n", "      <th>total_reorders_by_user</th>\n", "      <th>reorder_propotion_by_user</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>orders_3</th>\n", "      <th>orders_2</th>\n", "      <th>orders_1</th>\n", "      <th>reorder_3</th>\n", "      <th>reorder_2</th>\n", "      <th>reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2.644068</td>\n", "      <td>1.256194</td>\n", "      <td>10.542373</td>\n", "      <td>3.500355</td>\n", "      <td>18.542374</td>\n", "      <td>10.559066</td>\n", "      <td>10</td>\n", "      <td>59</td>\n", "      <td>18</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.900000</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2.005128</td>\n", "      <td>0.971222</td>\n", "      <td>10.441026</td>\n", "      <td>1.649854</td>\n", "      <td>14.902564</td>\n", "      <td>9.671712</td>\n", "      <td>14</td>\n", "      <td>195</td>\n", "      <td>102</td>\n", "      <td>93.0</td>\n", "      <td>0.476923</td>\n", "      <td>13.928571</td>\n", "      <td>0.447961</td>\n", "      <td>19</td>\n", "      <td>9</td>\n", "      <td>16</td>\n", "      <td>0.578947</td>\n", "      <td>0.0</td>\n", "      <td>0.625000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1.011364</td>\n", "      <td>1.245630</td>\n", "      <td>16.352273</td>\n", "      <td>1.454599</td>\n", "      <td>10.181818</td>\n", "      <td>5.867395</td>\n", "      <td>12</td>\n", "      <td>88</td>\n", "      <td>33</td>\n", "      <td>55.0</td>\n", "      <td>0.625000</td>\n", "      <td>7.333333</td>\n", "      <td>0.658817</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>0.833333</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>4.722222</td>\n", "      <td>0.826442</td>\n", "      <td>13.111111</td>\n", "      <td>1.745208</td>\n", "      <td>11.944445</td>\n", "      <td>9.973330</td>\n", "      <td>5</td>\n", "      <td>18</td>\n", "      <td>17</td>\n", "      <td>1.0</td>\n", "      <td>0.055556</td>\n", "      <td>3.600000</td>\n", "      <td>0.028571</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>0.142857</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1.621622</td>\n", "      <td>1.276961</td>\n", "      <td>15.729730</td>\n", "      <td>2.588958</td>\n", "      <td>10.189189</td>\n", "      <td>7.600577</td>\n", "      <td>4</td>\n", "      <td>37</td>\n", "      <td>23</td>\n", "      <td>14.0</td>\n", "      <td>0.378378</td>\n", "      <td>9.250000</td>\n", "      <td>0.377778</td>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>12</td>\n", "      <td>0.444444</td>\n", "      <td>0.4</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id   avg_dow   std_dow    avg_doh   std_doh  avg_since_order  \\\n", "0        1  2.644068  1.256194  10.542373  3.500355        18.542374   \n", "1        2  2.005128  0.971222  10.441026  1.649854        14.902564   \n", "2        3  1.011364  1.245630  16.352273  1.454599        10.181818   \n", "3        4  4.722222  0.826442  13.111111  1.745208        11.944445   \n", "4        5  1.621622  1.276961  15.729730  2.588958        10.189189   \n", "\n", "   std_since_order  total_orders_by_user  total_products_by_user  \\\n", "0        10.559066                    10                      59   \n", "1         9.671712                    14                     195   \n", "2         5.867395                    12                      88   \n", "3         9.973330                     5                      18   \n", "4         7.600577                     4                      37   \n", "\n", "   total_unique_product_by_user  total_reorders_by_user  \\\n", "0                            18                    41.0   \n", "1                           102                    93.0   \n", "2                            33                    55.0   \n", "3                            17                     1.0   \n", "4                            23                    14.0   \n", "\n", "   reorder_propotion_by_user  average_order_size  reorder_in_order  orders_3  \\\n", "0                   0.694915            5.900000          0.705833         6   \n", "1                   0.476923           13.928571          0.447961        19   \n", "2                   0.625000            7.333333          0.658817         6   \n", "3                   0.055556            3.600000          0.028571         7   \n", "4                   0.378378            9.250000          0.377778         9   \n", "\n", "   orders_2  orders_1  reorder_3  reorder_2  reorder_1  \n", "0         6         9   0.666667        1.0   0.666667  \n", "1         9        16   0.578947        0.0   0.625000  \n", "2         5         6   0.833333        1.0   1.000000  \n", "3         2         3   0.142857        0.0   0.000000  \n", "4         5        12   0.444444        0.4   0.666667  "]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["user_feats = user_feats.merge(last_order_feats, on = 'user_id', how = 'left')\n", "user_feats.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### User and Product level features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["(27) User's avg add-to-cart-order for a product\n", "\n", "(28) User's avg days_since_prior_order for a product\n", "\n", "(29) User's product total orders, reorders and reorders percentage\n", "\n", "(30) User's order number when the product was bought last"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["agg_dict6 = {'reordered': {'total_product_orders_by_user':'count', \n", "                          'total_product_reorders_by_user':'sum',\n", "                          'user_product_reorder_percentage': 'mean'},\n", "            'add_to_cart_order': {'avg_add_to_cart_by_user':'mean'},\n", "            'days_since_prior_order': {'avg_days_since_last_bought':'mean'},\n", "            'order_number': {'last_ordered_in':'max'}}"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9</td>\n", "      <td>8</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                            10   \n", "1        1       10258                             9   \n", "2        1       10326                             1   \n", "3        1       12427                            10   \n", "4        1       13032                             3   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                               9                         0.900000   \n", "1                               8                         0.888889   \n", "2                               0                         0.000000   \n", "3                               9                         0.900000   \n", "4                               2                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \n", "0                 1.400000                   17.600000               10  \n", "1                 3.333333                   19.555555               10  \n", "2                 5.000000                   28.000000                5  \n", "3                 3.300000                   17.600000               10  \n", "4                 6.333333                   21.666666               10  "]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["user_product_feats = prior_df.groupby(['user_id', 'product_id']).agg(agg_dict6)\n", "user_product_feats.columns = user_product_feats.columns.droplevel(0)\n", "user_product_feats.reset_index(inplace = True)\n", "user_product_feats.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**features**\n", "\n", "(31) User's product purchase history of last three orders"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>order_number</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>level_1</th>\n", "      <th>rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>6</td>\n", "      <td>0.666667</td>\n", "      <td>7</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>6</td>\n", "      <td>1.000000</td>\n", "      <td>8</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>9</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>12</td>\n", "      <td>19</td>\n", "      <td>0.578947</td>\n", "      <td>21</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>13</td>\n", "      <td>9</td>\n", "      <td>0.000000</td>\n", "      <td>22</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  order_number  average_order_size  reorder_in_order  level_1  rank\n", "0        1             8                   6          0.666667        7   1.0\n", "1        1             9                   6          1.000000        8   2.0\n", "2        1            10                   9          0.666667        9   3.0\n", "3        2            12                  19          0.578947       21   1.0\n", "4        2            13                   9          0.000000       22   2.0"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["last_three_orders.head()"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>product_id</th>\n", "      <th>add_to_cart_order</th>\n", "      <th>reordered</th>\n", "      <th>user_id</th>\n", "      <th>eval_set</th>\n", "      <th>order_number</th>\n", "      <th>order_dow</th>\n", "      <th>order_hour_of_day</th>\n", "      <th>days_since_prior_order</th>\n", "      <th>product_name</th>\n", "      <th>aisle_id</th>\n", "      <th>department_id</th>\n", "      <th>user_buy_product_times</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>level_1</th>\n", "      <th>rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7</td>\n", "      <td>34050</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>142903</td>\n", "      <td>prior</td>\n", "      <td>11</td>\n", "      <td>2</td>\n", "      <td>14</td>\n", "      <td>30.0</td>\n", "      <td>Orange Juice</td>\n", "      <td>31</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0.000000</td>\n", "      <td>2231251</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>7</td>\n", "      <td>46802</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>142903</td>\n", "      <td>prior</td>\n", "      <td>11</td>\n", "      <td>2</td>\n", "      <td>14</td>\n", "      <td>30.0</td>\n", "      <td>Pineapple Chunks</td>\n", "      <td>116</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0.000000</td>\n", "      <td>2231251</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>14</td>\n", "      <td>20392</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>18194</td>\n", "      <td>prior</td>\n", "      <td>49</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>3.0</td>\n", "      <td>Hair Bender Whole Bean Coffee</td>\n", "      <td>26</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "      <td>0.818182</td>\n", "      <td>282882</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>14</td>\n", "      <td>27845</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>18194</td>\n", "      <td>prior</td>\n", "      <td>49</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>3.0</td>\n", "      <td>Organic Whole Milk</td>\n", "      <td>84</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "      <td>0.818182</td>\n", "      <td>282882</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>14</td>\n", "      <td>162</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>18194</td>\n", "      <td>prior</td>\n", "      <td>49</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>3.0</td>\n", "      <td>Organic Mini Homestyle Waffles</td>\n", "      <td>52</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "      <td>0.818182</td>\n", "      <td>282882</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   order_id  product_id  add_to_cart_order  reordered  user_id eval_set  \\\n", "0         7       34050                  1          0   142903    prior   \n", "1         7       46802                  2          0   142903    prior   \n", "2        14       20392                  1          1    18194    prior   \n", "3        14       27845                  2          1    18194    prior   \n", "4        14         162                  3          1    18194    prior   \n", "\n", "   order_number  order_dow  order_hour_of_day  days_since_prior_order  \\\n", "0            11          2                 14                    30.0   \n", "1            11          2                 14                    30.0   \n", "2            49          3                 15                     3.0   \n", "3            49          3                 15                     3.0   \n", "4            49          3                 15                     3.0   \n", "\n", "                     product_name  aisle_id  department_id  \\\n", "0                    Orange Juice        31              7   \n", "1                Pineapple Chunks       116              1   \n", "2   Hair Bender Whole Bean Coffee        26              7   \n", "3              Organic Whole Milk        84             16   \n", "4  Organic Mini Homestyle Waffles        52              1   \n", "\n", "   user_buy_product_times  average_order_size  reorder_in_order  level_1  rank  \n", "0                       1                   2          0.000000  2231251   2.0  \n", "1                       1                   2          0.000000  2231251   2.0  \n", "2                       1                  11          0.818182   282882   1.0  \n", "3                       1                  11          0.818182   282882   1.0  \n", "4                       1                  11          0.818182   282882   1.0  "]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["last_orders = prior_df.merge(last_three_orders, on = ['user_id', 'order_number'], how = 'inner')\n", "last_orders.head()"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["last_orders['rank'] = last_orders.groupby(['user_id', 'product_id'])['order_number'].rank(\"dense\", ascending=True)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>is_reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>25133</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  product_id  is_reorder_3  is_reorder_2  is_reorder_1\n", "0        1         196           1.0           1.0           1.0\n", "1        1       10258           1.0           1.0           1.0\n", "2        1       12427           1.0           1.0           1.0\n", "3        1       13032           1.0           0.0           0.0\n", "4        1       25133           1.0           1.0           1.0"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["product_purchase_history = last_orders.pivot_table(index = ['user_id', 'product_id'],\\\n", "                                                   columns='rank', values = 'reordered').reset_index()\n", "product_purchase_history.columns = ['user_id', 'product_id', 'is_reorder_3', 'is_reorder_2', 'is_reorder_1']\n", "product_purchase_history.fillna(0, inplace = True)\n", "product_purchase_history.head()"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>is_reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9</td>\n", "      <td>8</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                            10   \n", "1        1       10258                             9   \n", "2        1       10326                             1   \n", "3        1       12427                            10   \n", "4        1       13032                             3   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                               9                         0.900000   \n", "1                               8                         0.888889   \n", "2                               0                         0.000000   \n", "3                               9                         0.900000   \n", "4                               2                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \\\n", "0                 1.400000                   17.600000               10   \n", "1                 3.333333                   19.555555               10   \n", "2                 5.000000                   28.000000                5   \n", "3                 3.300000                   17.600000               10   \n", "4                 6.333333                   21.666666               10   \n", "\n", "   is_reorder_3  is_reorder_2  is_reorder_1  \n", "0           1.0           1.0           1.0  \n", "1           1.0           1.0           1.0  \n", "2           NaN           NaN           NaN  \n", "3           1.0           1.0           1.0  \n", "4           1.0           0.0           0.0  "]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["user_product_feats = user_product_feats.merge(product_purchase_history, on=['user_id', 'product_id'], how = 'left')\n", "user_product_feats.head()"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/plain": ["user_id                                  0\n", "product_id                               0\n", "total_product_orders_by_user             0\n", "total_product_reorders_by_user           0\n", "user_product_reorder_percentage          0\n", "avg_add_to_cart_by_user                  0\n", "avg_days_since_last_bought               0\n", "last_ordered_in                          0\n", "is_reorder_3                       8382738\n", "is_reorder_2                       8382738\n", "is_reorder_1                       8382738\n", "dtype: int64"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["user_product_feats.isnull().sum()"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["user_product_feats.fillna(0, inplace = True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Saving all features"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["prod_feats1.to_pickle(root + 'product_features.pkl')\n", "user_feats.to_pickle(root +'user_features.pkl')\n", "user_product_feats.to_pickle(root +'user_product_features.pkl')"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>mean_add_to_cart_order</th>\n", "      <th>total_orders</th>\n", "      <th>total_reorders</th>\n", "      <th>reorder_percentage</th>\n", "      <th>unique_users</th>\n", "      <th>order_first_time_total_cnt</th>\n", "      <th>order_second_time_total_cnt</th>\n", "      <th>is_organic</th>\n", "      <th>second_time_percent</th>\n", "      <th>...</th>\n", "      <th>department_total_orders</th>\n", "      <th>department_total_reorders</th>\n", "      <th>department_reorder_percentage</th>\n", "      <th>department_unique_users</th>\n", "      <th>department_0</th>\n", "      <th>department_1</th>\n", "      <th>department_2</th>\n", "      <th>department_3</th>\n", "      <th>department_4</th>\n", "      <th>department_5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>5.801836</td>\n", "      <td>1852</td>\n", "      <td>1136.0</td>\n", "      <td>0.613391</td>\n", "      <td>716</td>\n", "      <td>716</td>\n", "      <td>276</td>\n", "      <td>0</td>\n", "      <td>0.385475</td>\n", "      <td>...</td>\n", "      <td>2887550</td>\n", "      <td>1657973.0</td>\n", "      <td>0.574180</td>\n", "      <td>174219</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>9.888889</td>\n", "      <td>90</td>\n", "      <td>12.0</td>\n", "      <td>0.133333</td>\n", "      <td>78</td>\n", "      <td>78</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>0.102564</td>\n", "      <td>...</td>\n", "      <td>1875577</td>\n", "      <td>650301.0</td>\n", "      <td>0.346721</td>\n", "      <td>172755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>6.415162</td>\n", "      <td>277</td>\n", "      <td>203.0</td>\n", "      <td>0.732852</td>\n", "      <td>74</td>\n", "      <td>74</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "      <td>0.486486</td>\n", "      <td>...</td>\n", "      <td>2690129</td>\n", "      <td>1757892.0</td>\n", "      <td>0.653460</td>\n", "      <td>172795</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>9.507599</td>\n", "      <td>329</td>\n", "      <td>147.0</td>\n", "      <td>0.446809</td>\n", "      <td>182</td>\n", "      <td>182</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0.351648</td>\n", "      <td>...</td>\n", "      <td>2236432</td>\n", "      <td>1211890.0</td>\n", "      <td>0.541885</td>\n", "      <td>163233</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>6.466667</td>\n", "      <td>15</td>\n", "      <td>9.0</td>\n", "      <td>0.600000</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0.666667</td>\n", "      <td>...</td>\n", "      <td>1875577</td>\n", "      <td>650301.0</td>\n", "      <td>0.346721</td>\n", "      <td>172755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 37 columns</p>\n", "</div>"], "text/plain": ["   product_id  mean_add_to_cart_order  total_orders  total_reorders  \\\n", "0           1                5.801836          1852          1136.0   \n", "1           2                9.888889            90            12.0   \n", "2           3                6.415162           277           203.0   \n", "3           4                9.507599           329           147.0   \n", "4           5                6.466667            15             9.0   \n", "\n", "   reorder_percentage  unique_users  order_first_time_total_cnt  \\\n", "0            0.613391           716                         716   \n", "1            0.133333            78                          78   \n", "2            0.732852            74                          74   \n", "3            0.446809           182                         182   \n", "4            0.600000             6                           6   \n", "\n", "   order_second_time_total_cnt  is_organic  second_time_percent  ...  \\\n", "0                          276           0             0.385475  ...   \n", "1                            8           0             0.102564  ...   \n", "2                           36           0             0.486486  ...   \n", "3                           64           0             0.351648  ...   \n", "4                            4           0             0.666667  ...   \n", "\n", "   department_total_orders  department_total_reorders  \\\n", "0                  2887550                  1657973.0   \n", "1                  1875577                   650301.0   \n", "2                  2690129                  1757892.0   \n", "3                  2236432                  1211890.0   \n", "4                  1875577                   650301.0   \n", "\n", "   department_reorder_percentage  department_unique_users  department_0  \\\n", "0                       0.574180                   174219             0   \n", "1                       0.346721                   172755             0   \n", "2                       0.653460                   172795             0   \n", "3                       0.541885                   163233             0   \n", "4                       0.346721                   172755             0   \n", "\n", "   department_1  department_2  department_3  department_4  department_5  \n", "0             0             0             0             0             1  \n", "1             0             0             0             1             0  \n", "2             0             0             0             1             1  \n", "3             0             0             1             0             0  \n", "4             0             0             0             1             0  \n", "\n", "[5 rows x 37 columns]"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_pickle(root +'product_features.pkl')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>avg_dow</th>\n", "      <th>std_dow</th>\n", "      <th>avg_doh</th>\n", "      <th>std_doh</th>\n", "      <th>avg_since_order</th>\n", "      <th>std_since_order</th>\n", "      <th>total_orders_by_user</th>\n", "      <th>total_products_by_user</th>\n", "      <th>total_unique_product_by_user</th>\n", "      <th>total_reorders_by_user</th>\n", "      <th>reorder_propotion_by_user</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>orders_3</th>\n", "      <th>orders_2</th>\n", "      <th>orders_1</th>\n", "      <th>reorder_3</th>\n", "      <th>reorder_2</th>\n", "      <th>reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2.644068</td>\n", "      <td>1.256194</td>\n", "      <td>10.542373</td>\n", "      <td>3.500355</td>\n", "      <td>18.542374</td>\n", "      <td>10.559066</td>\n", "      <td>10</td>\n", "      <td>59</td>\n", "      <td>18</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.900000</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2.005128</td>\n", "      <td>0.971222</td>\n", "      <td>10.441026</td>\n", "      <td>1.649854</td>\n", "      <td>14.902564</td>\n", "      <td>9.671712</td>\n", "      <td>14</td>\n", "      <td>195</td>\n", "      <td>102</td>\n", "      <td>93.0</td>\n", "      <td>0.476923</td>\n", "      <td>13.928571</td>\n", "      <td>0.447961</td>\n", "      <td>19</td>\n", "      <td>9</td>\n", "      <td>16</td>\n", "      <td>0.578947</td>\n", "      <td>0.0</td>\n", "      <td>0.625000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1.011364</td>\n", "      <td>1.245630</td>\n", "      <td>16.352273</td>\n", "      <td>1.454599</td>\n", "      <td>10.181818</td>\n", "      <td>5.867395</td>\n", "      <td>12</td>\n", "      <td>88</td>\n", "      <td>33</td>\n", "      <td>55.0</td>\n", "      <td>0.625000</td>\n", "      <td>7.333333</td>\n", "      <td>0.658817</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>0.833333</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>4.722222</td>\n", "      <td>0.826442</td>\n", "      <td>13.111111</td>\n", "      <td>1.745208</td>\n", "      <td>11.944445</td>\n", "      <td>9.973330</td>\n", "      <td>5</td>\n", "      <td>18</td>\n", "      <td>17</td>\n", "      <td>1.0</td>\n", "      <td>0.055556</td>\n", "      <td>3.600000</td>\n", "      <td>0.028571</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>0.142857</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1.621622</td>\n", "      <td>1.276961</td>\n", "      <td>15.729730</td>\n", "      <td>2.588958</td>\n", "      <td>10.189189</td>\n", "      <td>7.600577</td>\n", "      <td>4</td>\n", "      <td>37</td>\n", "      <td>23</td>\n", "      <td>14.0</td>\n", "      <td>0.378378</td>\n", "      <td>9.250000</td>\n", "      <td>0.377778</td>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>12</td>\n", "      <td>0.444444</td>\n", "      <td>0.4</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id   avg_dow   std_dow    avg_doh   std_doh  avg_since_order  \\\n", "0        1  2.644068  1.256194  10.542373  3.500355        18.542374   \n", "1        2  2.005128  0.971222  10.441026  1.649854        14.902564   \n", "2        3  1.011364  1.245630  16.352273  1.454599        10.181818   \n", "3        4  4.722222  0.826442  13.111111  1.745208        11.944445   \n", "4        5  1.621622  1.276961  15.729730  2.588958        10.189189   \n", "\n", "   std_since_order  total_orders_by_user  total_products_by_user  \\\n", "0        10.559066                    10                      59   \n", "1         9.671712                    14                     195   \n", "2         5.867395                    12                      88   \n", "3         9.973330                     5                      18   \n", "4         7.600577                     4                      37   \n", "\n", "   total_unique_product_by_user  total_reorders_by_user  \\\n", "0                            18                    41.0   \n", "1                           102                    93.0   \n", "2                            33                    55.0   \n", "3                            17                     1.0   \n", "4                            23                    14.0   \n", "\n", "   reorder_propotion_by_user  average_order_size  reorder_in_order  orders_3  \\\n", "0                   0.694915            5.900000          0.705833         6   \n", "1                   0.476923           13.928571          0.447961        19   \n", "2                   0.625000            7.333333          0.658817         6   \n", "3                   0.055556            3.600000          0.028571         7   \n", "4                   0.378378            9.250000          0.377778         9   \n", "\n", "   orders_2  orders_1  reorder_3  reorder_2  reorder_1  \n", "0         6         9   0.666667        1.0   0.666667  \n", "1         9        16   0.578947        0.0   0.625000  \n", "2         5         6   0.833333        1.0   1.000000  \n", "3         2         3   0.142857        0.0   0.000000  \n", "4         5        12   0.444444        0.4   0.666667  "]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_pickle(root+'user_features.pkl')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>is_reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9</td>\n", "      <td>8</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                            10   \n", "1        1       10258                             9   \n", "2        1       10326                             1   \n", "3        1       12427                            10   \n", "4        1       13032                             3   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                               9                         0.900000   \n", "1                               8                         0.888889   \n", "2                               0                         0.000000   \n", "3                               9                         0.900000   \n", "4                               2                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \\\n", "0                 1.400000                   17.600000               10   \n", "1                 3.333333                   19.555555               10   \n", "2                 5.000000                   28.000000                5   \n", "3                 3.300000                   17.600000               10   \n", "4                 6.333333                   21.666666               10   \n", "\n", "   is_reorder_3  is_reorder_2  is_reorder_1  \n", "0           1.0           1.0           1.0  \n", "1           1.0           1.0           1.0  \n", "2           0.0           0.0           0.0  \n", "3           1.0           1.0           1.0  \n", "4           1.0           0.0           0.0  "]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_pickle(root + 'user_product_features.pkl')\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}