{"cells": [{"cell_type": "markdown", "id": "3dba301f", "metadata": {}, "source": ["## This notebok implements functions to extract rules from <PERSON><PERSON><PERSON><PERSON>'s data\n", "These functions should later by used as API calls.\n", "\n", "| Function                                \t| Documentation                                                                                                               \t|\n", "|-----------------------------------------\t|-----------------------------------------------------------------------------------------------------------------------------\t|\n", "| most_10_frequent_items                  \t| Takes the cardinality of the itemset and return the most 10 frequent item-sets of that cardinality.                         \t|\n", "| all_items_with_at_least_support_and_len \t| Returns the itemset of a specific cardinality and satisfying a minimum support.                                             \t|\n", "| show_itemset_support                    \t| Returns the support of a given item-set                                                                                     \t|\n", "| rules_with_specific_threshold           \t| Return (Filter) rules satisfying a given threshold. Threshold can be on confidence, support, lift, leverage and conviction. \t|\n", "| select_rules_with_antecedents_length    \t| Return rules with a specific antecedents cardinality.                                                                       \t|\n", "| select_rules_with_antecedents_names     \t| Return rules of a specific antecedent.                                                                                      \t|\n", "| select_rules_with_consequents_names     \t| Return rules of a specific consequent.                                                                                      \t|"]}, {"cell_type": "code", "execution_count": 1, "id": "674d5118", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "caaebba2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>order_id</th>\n", "      <th>product_id</th>\n", "      <th>aisle_id</th>\n", "      <th>department_id</th>\n", "      <th>add_to_cart_order</th>\n", "      <th>reordered</th>\n", "      <th>product_name</th>\n", "      <th>aisle</th>\n", "      <th>department</th>\n", "      <th>eval_set</th>\n", "      <th>order_number</th>\n", "      <th>order_dow</th>\n", "      <th>order_hour_of_day</th>\n", "      <th>days_since_prior_order</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>112108</td>\n", "      <td>1</td>\n", "      <td>49302</td>\n", "      <td>120</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Bulgarian Yogurt</td>\n", "      <td>yogurt</td>\n", "      <td>dairy eggs</td>\n", "      <td>train</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>10</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>112108</td>\n", "      <td>1</td>\n", "      <td>49683</td>\n", "      <td>83</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>fresh vegetables</td>\n", "      <td>produce</td>\n", "      <td>train</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>10</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>112108</td>\n", "      <td>1</td>\n", "      <td>13176</td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>Bag of Organic Bananas</td>\n", "      <td>fresh fruits</td>\n", "      <td>produce</td>\n", "      <td>train</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>10</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>112108</td>\n", "      <td>1</td>\n", "      <td>43633</td>\n", "      <td>95</td>\n", "      <td>15</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>Lightly Smoked Sardines in Olive Oil</td>\n", "      <td>canned meat seafood</td>\n", "      <td>canned goods</td>\n", "      <td>train</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>10</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>112108</td>\n", "      <td>1</td>\n", "      <td>10246</td>\n", "      <td>83</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>Organic Celery Hearts</td>\n", "      <td>fresh vegetables</td>\n", "      <td>produce</td>\n", "      <td>train</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>10</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33818974</th>\n", "      <td>169679</td>\n", "      <td>3421063</td>\n", "      <td>49235</td>\n", "      <td>53</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Organic Half &amp; Half</td>\n", "      <td>cream</td>\n", "      <td>dairy eggs</td>\n", "      <td>train</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33818975</th>\n", "      <td>169679</td>\n", "      <td>3421063</td>\n", "      <td>14233</td>\n", "      <td>115</td>\n", "      <td>7</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Natural Artesian Water</td>\n", "      <td>water seltzer sparkling water</td>\n", "      <td>beverages</td>\n", "      <td>train</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33819017</th>\n", "      <td>139822</td>\n", "      <td>3421070</td>\n", "      <td>16953</td>\n", "      <td>88</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>Creamy Peanut Butter</td>\n", "      <td>spreads</td>\n", "      <td>pantry</td>\n", "      <td>train</td>\n", "      <td>15</td>\n", "      <td>6</td>\n", "      <td>10</td>\n", "      <td>8.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33819018</th>\n", "      <td>139822</td>\n", "      <td>3421070</td>\n", "      <td>35951</td>\n", "      <td>91</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Organic Unsweetened Almond Milk</td>\n", "      <td>soy lactosefree</td>\n", "      <td>dairy eggs</td>\n", "      <td>train</td>\n", "      <td>15</td>\n", "      <td>6</td>\n", "      <td>10</td>\n", "      <td>8.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33819019</th>\n", "      <td>139822</td>\n", "      <td>3421070</td>\n", "      <td>4724</td>\n", "      <td>32</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Bro<PERSON>li Florettes</td>\n", "      <td>packaged produce</td>\n", "      <td>produce</td>\n", "      <td>train</td>\n", "      <td>15</td>\n", "      <td>6</td>\n", "      <td>10</td>\n", "      <td>8.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1384617 rows × 15 columns</p>\n", "</div>"], "text/plain": ["          user_id  order_id  product_id  aisle_id  department_id  \\\n", "0          112108         1       49302       120             16   \n", "1          112108         1       49683        83              4   \n", "2          112108         1       13176        24              4   \n", "3          112108         1       43633        95             15   \n", "4          112108         1       10246        83              4   \n", "...           ...       ...         ...       ...            ...   \n", "33818974   169679   3421063       49235        53             16   \n", "33818975   169679   3421063       14233       115              7   \n", "33819017   139822   3421070       16953        88             13   \n", "33819018   139822   3421070       35951        91             16   \n", "33819019   139822   3421070        4724        32              4   \n", "\n", "          add_to_cart_order  reordered                          product_name  \\\n", "0                         1          1                      Bulgarian Yogurt   \n", "1                         4          0                        Cucumber Kirby   \n", "2                         6          0                Bag of Organic Bananas   \n", "3                         5          1  Lightly Smoked Sardines in Olive Oil   \n", "4                         3          0                 Organic Celery Hearts   \n", "...                     ...        ...                                   ...   \n", "33818974                  1          1                   Organic Half & Half   \n", "33818975                  3          1                Natural Artesian Water   \n", "33819017                  2          1                  Creamy Peanut Butter   \n", "33819018                  1          1       Organic Unsweetened Almond Milk   \n", "33819019                  3          1                    Broccoli Florettes   \n", "\n", "                                  aisle    department eval_set  order_number  \\\n", "0                                yogurt    dairy eggs    train             4   \n", "1                      fresh vegetables       produce    train             4   \n", "2                          fresh fruits       produce    train             4   \n", "3                   canned meat seafood  canned goods    train             4   \n", "4                      fresh vegetables       produce    train             4   \n", "...                                 ...           ...      ...           ...   \n", "33818974                          cream    dairy eggs    train            30   \n", "33818975  water seltzer sparkling water     beverages    train            30   \n", "33819017                        spreads        pantry    train            15   \n", "33819018                soy lactosefree    dairy eggs    train            15   \n", "33819019               packaged produce       produce    train            15   \n", "\n", "          order_dow  order_hour_of_day  days_since_prior_order  \n", "0                 4                 10                     9.0  \n", "1                 4                 10                     9.0  \n", "2                 4                 10                     9.0  \n", "3                 4                 10                     9.0  \n", "4                 4                 10                     9.0  \n", "...             ...                ...                     ...  \n", "33818974          0                 10                     4.0  \n", "33818975          0                 10                     4.0  \n", "33819017          6                 10                     8.0  \n", "33819018          6                 10                     8.0  \n", "33819019          6                 10                     8.0  \n", "\n", "[1384617 rows x 15 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"prior_train_orders.csv\")\n", "df = df[df['eval_set'] == 'train']\n", "df"]}, {"cell_type": "markdown", "id": "267f5c0a", "metadata": {}, "source": ["### Create a basket\n", "As the dataset contains huge amout of data, let us take a subset of the data to extract the association rules from it.\n", "\n", "**Assumptions**: Segment the data by considering the 100 most frequent ordered items. Please note it is just an assumption. You can consider 'n frequent order items as per your choice."]}, {"cell_type": "code", "execution_count": 3, "id": "1f481492", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>frequency</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>24852</td>\n", "      <td>18726</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>13176</td>\n", "      <td>15480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21137</td>\n", "      <td>10894</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21903</td>\n", "      <td>9784</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>47626</td>\n", "      <td>8135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>8193</td>\n", "      <td>1418</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>9387</td>\n", "      <td>1379</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>37687</td>\n", "      <td>1362</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>20995</td>\n", "      <td>1361</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>34243</td>\n", "      <td>1351</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 2 columns</p>\n", "</div>"], "text/plain": ["    product_id  frequency\n", "0        24852      18726\n", "1        13176      15480\n", "2        21137      10894\n", "3        21903       9784\n", "4        47626       8135\n", "..         ...        ...\n", "95        8193       1418\n", "96        9387       1379\n", "97       37687       1362\n", "98       20995       1361\n", "99       34243       1351\n", "\n", "[100 rows x 2 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["product_counts = df.groupby('product_id')['order_id'].count().reset_index().rename(columns = {'order_id':'frequency'})\n", "product_counts = product_counts.sort_values('frequency', ascending=False)[0:100].reset_index(drop=True)\n", "product_counts\n"]}, {"cell_type": "code", "execution_count": 4, "id": "0a93230b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[13176, 21137, 21903, 47626, 47766, 47209, 16797, 26209, 27966]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["freq_products = list(product_counts.product_id)\n", "del product_counts \n", "freq_products[1:10]\n"]}, {"cell_type": "code", "execution_count": 5, "id": "1da101ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["(314227, 15)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["order_products = df[df.product_id.isin(freq_products)]\n", "del df\n", "order_products.shape\n"]}, {"cell_type": "code", "execution_count": 6, "id": "3dbeb5f2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_name</th>\n", "      <th>reordered</th>\n", "    </tr>\n", "    <tr>\n", "      <th>order_id</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bag of Organic Bananas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Organic Hass Avocado</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Organic Whole String Cheese</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>Organic Garnet Sweet Potato (Yam)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3420998</th>\n", "      <td>Organic Cilantro</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421049</th>\n", "      <td>Organic Baby <PERSON></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421056</th>\n", "      <td>Sparkling Lemon Water</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421063</th>\n", "      <td>Organic Half &amp; Half</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421070</th>\n", "      <td>Organic Unsweetened Almond Milk</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>314227 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                               product_name  reordered\n", "order_id                                              \n", "1                            Cucumber Kirby          0\n", "1                    Bag of Organic Bananas          0\n", "1                      Organic Hass Avocado          0\n", "1               Organic Whole String Cheese          1\n", "36        Organic Garnet Sweet Potato (Yam)          1\n", "...                                     ...        ...\n", "3420998                    Organic Cilantro          1\n", "3421049               Organic Baby <PERSON>li          0\n", "3421056               Sparkling Lemon Water          1\n", "3421063                 Organic Half & Half          1\n", "3421070     Organic Unsweetened Almond Milk          1\n", "\n", "[314227 rows x 2 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df=order_products[['order_id','product_name','reordered']].set_index('order_id')\n", "df"]}, {"cell_type": "code", "execution_count": 7, "id": "8cf117c6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>product_name</th>\n", "      <th>100% Whole Wheat Bread</th>\n", "      <th>2% Reduced Fat Milk</th>\n", "      <th>Apple Honeycrisp Organic</th>\n", "      <th>As<PERSON>agu<PERSON></th>\n", "      <th>Bag of Organic Bananas</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Blueberries</th>\n", "      <th>Boneless Skinless Chicken Breasts</th>\n", "      <th>B<PERSON><PERSON>li Crown</th>\n", "      <th>Bunched <PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>...</th>\n", "      <th>Sparkling Lemon Water</th>\n", "      <th>Sparkling Natural Mineral Water</th>\n", "      <th>Sparkling Water Grapefruit</th>\n", "      <th>Spring Water</th>\n", "      <th>Strawberries</th>\n", "      <th>Uncured Genoa Salami</th>\n", "      <th>Unsalted Butter</th>\n", "      <th>Unsweetened Almondmilk</th>\n", "      <th>Unsweetened Original Almond Breeze Almond Milk</th>\n", "      <th>Yellow Onions</th>\n", "    </tr>\n", "    <tr>\n", "      <th>order_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3420998</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421049</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421056</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421063</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421070</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>93850 rows × 100 columns</p>\n", "</div>"], "text/plain": ["product_name  100% Whole Wheat Bread  2% Reduced Fat Milk  \\\n", "order_id                                                    \n", "1                                0.0                  0.0   \n", "36                               0.0                  0.0   \n", "38                               0.0                  0.0   \n", "96                               0.0                  0.0   \n", "98                               0.0                  0.0   \n", "...                              ...                  ...   \n", "3420998                          0.0                  0.0   \n", "3421049                          0.0                  0.0   \n", "3421056                          0.0                  0.0   \n", "3421063                          0.0                  0.0   \n", "3421070                          0.0                  0.0   \n", "\n", "product_name  Apple Honeycrisp Organic  Asparagus  Bag of Organic Bananas  \\\n", "order_id                                                                    \n", "1                                  0.0        0.0                     0.0   \n", "36                                 0.0        1.0                     0.0   \n", "38                                 0.0        0.0                     0.0   \n", "96                                 0.0        0.0                     0.0   \n", "98                                 0.0        0.0                     1.0   \n", "...                                ...        ...                     ...   \n", "3420998                            1.0        0.0                     0.0   \n", "3421049                            0.0        0.0                     0.0   \n", "3421056                            0.0        0.0                     0.0   \n", "3421063                            0.0        0.0                     0.0   \n", "3421070                            0.0        0.0                     0.0   \n", "\n", "product_name  Banana  Blueberries  Boneless Skinless Chicken Breasts  \\\n", "order_id                                                               \n", "1                0.0          0.0                                0.0   \n", "36               0.0          0.0                                0.0   \n", "38               0.0          0.0                                0.0   \n", "96               0.0          0.0                                0.0   \n", "98               0.0          0.0                                0.0   \n", "...              ...          ...                                ...   \n", "3420998          0.0          0.0                                0.0   \n", "3421049          0.0          0.0                                0.0   \n", "3421056          0.0          0.0                                0.0   \n", "3421063          0.0          0.0                                0.0   \n", "3421070          0.0          0.0                                0.0   \n", "\n", "product_name  Broccoli Crown  Bunched Cilantro  ...  Sparkling Lemon Water  \\\n", "order_id                                        ...                          \n", "1                        0.0               0.0  ...                    0.0   \n", "36                       0.0               0.0  ...                    0.0   \n", "38                       0.0               0.0  ...                    0.0   \n", "96                       0.0               0.0  ...                    0.0   \n", "98                       0.0               0.0  ...                    0.0   \n", "...                      ...               ...  ...                    ...   \n", "3420998                  0.0               0.0  ...                    0.0   \n", "3421049                  0.0               0.0  ...                    0.0   \n", "3421056                  0.0               0.0  ...                    1.0   \n", "3421063                  0.0               0.0  ...                    0.0   \n", "3421070                  0.0               0.0  ...                    0.0   \n", "\n", "product_name  Sparkling Natural Mineral Water  Sparkling Water Grapefruit  \\\n", "order_id                                                                    \n", "1                                         0.0                         0.0   \n", "36                                        0.0                         0.0   \n", "38                                        0.0                         0.0   \n", "96                                        0.0                         0.0   \n", "98                                        0.0                         0.0   \n", "...                                       ...                         ...   \n", "3420998                                   0.0                         0.0   \n", "3421049                                   0.0                         0.0   \n", "3421056                                   0.0                         0.0   \n", "3421063                                   0.0                         0.0   \n", "3421070                                   0.0                         0.0   \n", "\n", "product_name  Spring Water  Strawberries  Uncured Genoa Salami  \\\n", "order_id                                                         \n", "1                      0.0           0.0                   0.0   \n", "36                     1.0           0.0                   0.0   \n", "38                     0.0           0.0                   0.0   \n", "96                     0.0           0.0                   0.0   \n", "98                     0.0           0.0                   1.0   \n", "...                    ...           ...                   ...   \n", "3420998                0.0           0.0                   1.0   \n", "3421049                0.0           0.0                   0.0   \n", "3421056                0.0           0.0                   0.0   \n", "3421063                0.0           0.0                   0.0   \n", "3421070                0.0           0.0                   0.0   \n", "\n", "product_name  Unsalted Butter  Unsweetened Almondmilk  \\\n", "order_id                                                \n", "1                         0.0                     0.0   \n", "36                        0.0                     0.0   \n", "38                        0.0                     0.0   \n", "96                        0.0                     0.0   \n", "98                        0.0                     0.0   \n", "...                       ...                     ...   \n", "3420998                   0.0                     0.0   \n", "3421049                   0.0                     0.0   \n", "3421056                   0.0                     0.0   \n", "3421063                   0.0                     0.0   \n", "3421070                   0.0                     0.0   \n", "\n", "product_name  Unsweetened Original Almond Breeze Almond Milk  Yellow Onions  \n", "order_id                                                                     \n", "1                                                        0.0            0.0  \n", "36                                                       0.0            0.0  \n", "38                                                       0.0            0.0  \n", "96                                                       0.0            0.0  \n", "98                                                       0.0            0.0  \n", "...                                                      ...            ...  \n", "3420998                                                  0.0            0.0  \n", "3421049                                                  0.0            0.0  \n", "3421056                                                  0.0            0.0  \n", "3421063                                                  0.0            0.0  \n", "3421070                                                  0.0            0.0  \n", "\n", "[93850 rows x 100 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["basket = df.pivot_table(columns='product_name', values='reordered', index='order_id').reset_index().fillna(0).set_index('order_id')\n", "basket"]}, {"cell_type": "code", "execution_count": 8, "id": "a409ab66", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>product_name</th>\n", "      <th>100% Whole Wheat Bread</th>\n", "      <th>2% Reduced Fat Milk</th>\n", "      <th>Apple Honeycrisp Organic</th>\n", "      <th>As<PERSON>agu<PERSON></th>\n", "      <th>Bag of Organic Bananas</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Blueberries</th>\n", "      <th>Boneless Skinless Chicken Breasts</th>\n", "      <th>B<PERSON><PERSON>li Crown</th>\n", "      <th>Bunched <PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>...</th>\n", "      <th>Sparkling Lemon Water</th>\n", "      <th>Sparkling Natural Mineral Water</th>\n", "      <th>Sparkling Water Grapefruit</th>\n", "      <th>Spring Water</th>\n", "      <th>Strawberries</th>\n", "      <th>Uncured Genoa Salami</th>\n", "      <th>Unsalted Butter</th>\n", "      <th>Unsweetened Almondmilk</th>\n", "      <th>Unsweetened Original Almond Breeze Almond Milk</th>\n", "      <th>Yellow Onions</th>\n", "    </tr>\n", "    <tr>\n", "      <th>order_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 100 columns</p>\n", "</div>"], "text/plain": ["product_name  100% Whole Wheat Bread  2% Reduced Fat Milk  \\\n", "order_id                                                    \n", "1                                  0                    0   \n", "36                                 0                    0   \n", "38                                 0                    0   \n", "96                                 0                    0   \n", "98                                 0                    0   \n", "\n", "product_name  Apple Honeycrisp Organic  Asparagus  Bag of Organic Bananas  \\\n", "order_id                                                                    \n", "1                                    0          0                       0   \n", "36                                   0          1                       0   \n", "38                                   0          0                       0   \n", "96                                   0          0                       0   \n", "98                                   0          0                       1   \n", "\n", "product_name  Banana  Blueberries  Boneless Skinless Chicken Breasts  \\\n", "order_id                                                               \n", "1                  0            0                                  0   \n", "36                 0            0                                  0   \n", "38                 0            0                                  0   \n", "96                 0            0                                  0   \n", "98                 0            0                                  0   \n", "\n", "product_name  Broccoli Crown  Bunched Cilantro  ...  Sparkling Lemon Water  \\\n", "order_id                                        ...                          \n", "1                          0                 0  ...                      0   \n", "36                         0                 0  ...                      0   \n", "38                         0                 0  ...                      0   \n", "96                         0                 0  ...                      0   \n", "98                         0                 0  ...                      0   \n", "\n", "product_name  Sparkling Natural Mineral Water  Sparkling Water Grapefruit  \\\n", "order_id                                                                    \n", "1                                           0                           0   \n", "36                                          0                           0   \n", "38                                          0                           0   \n", "96                                          0                           0   \n", "98                                          0                           0   \n", "\n", "product_name  Spring Water  Strawberries  Uncured Genoa Salami  \\\n", "order_id                                                         \n", "1                        0             0                     0   \n", "36                       1             0                     0   \n", "38                       0             0                     0   \n", "96                       0             0                     0   \n", "98                       0             0                     1   \n", "\n", "product_name  Unsalted Butter  Unsweetened Almondmilk  \\\n", "order_id                                                \n", "1                           0                       0   \n", "36                          0                       0   \n", "38                          0                       0   \n", "96                          0                       0   \n", "98                          0                       0   \n", "\n", "product_name  Unsweetened Original Almond Breeze Almond Milk  Yellow Onions  \n", "order_id                                                                     \n", "1                                                          0              0  \n", "36                                                         0              0  \n", "38                                                         0              0  \n", "96                                                         0              0  \n", "98                                                         0              0  \n", "\n", "[5 rows x 100 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["def encode_units(x):\n", "    if x <= 0:\n", "        return 0\n", "    if x >= 1:\n", "        return 1\n", "    \n", "basket = basket.applymap(encode_units)\n", "basket.head()\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "03ce8e99", "metadata": {}, "source": ["### Apply Apriori algorithm\n", "As the dataset contains huge amount of data, let us take a subset of the data to extract the association rules from it.\n", "\n", "**Assumptions**: Segment the basket by considering 100000 record. Please note its just an assumption, you can consider 'n' records as per choice."]}, {"cell_type": "code", "execution_count": 9, "id": "196ebd43", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>product_name</th>\n", "      <th>100% Whole Wheat Bread</th>\n", "      <th>2% Reduced Fat Milk</th>\n", "      <th>Apple Honeycrisp Organic</th>\n", "      <th>As<PERSON>agu<PERSON></th>\n", "      <th>Bag of Organic Bananas</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Blueberries</th>\n", "      <th>Boneless Skinless Chicken Breasts</th>\n", "      <th>B<PERSON><PERSON>li Crown</th>\n", "      <th>Bunched <PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>...</th>\n", "      <th>Sparkling Lemon Water</th>\n", "      <th>Sparkling Natural Mineral Water</th>\n", "      <th>Sparkling Water Grapefruit</th>\n", "      <th>Spring Water</th>\n", "      <th>Strawberries</th>\n", "      <th>Uncured Genoa Salami</th>\n", "      <th>Unsalted Butter</th>\n", "      <th>Unsweetened Almondmilk</th>\n", "      <th>Unsweetened Original Almond Breeze Almond Milk</th>\n", "      <th>Yellow Onions</th>\n", "    </tr>\n", "    <tr>\n", "      <th>order_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3420998</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421049</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421056</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421063</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3421070</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>93850 rows × 100 columns</p>\n", "</div>"], "text/plain": ["product_name  100% Whole Wheat Bread  2% Reduced Fat Milk  \\\n", "order_id                                                    \n", "1                                  0                    0   \n", "36                                 0                    0   \n", "38                                 0                    0   \n", "96                                 0                    0   \n", "98                                 0                    0   \n", "...                              ...                  ...   \n", "3420998                            0                    0   \n", "3421049                            0                    0   \n", "3421056                            0                    0   \n", "3421063                            0                    0   \n", "3421070                            0                    0   \n", "\n", "product_name  Apple Honeycrisp Organic  Asparagus  Bag of Organic Bananas  \\\n", "order_id                                                                    \n", "1                                    0          0                       0   \n", "36                                   0          1                       0   \n", "38                                   0          0                       0   \n", "96                                   0          0                       0   \n", "98                                   0          0                       1   \n", "...                                ...        ...                     ...   \n", "3420998                              1          0                       0   \n", "3421049                              0          0                       0   \n", "3421056                              0          0                       0   \n", "3421063                              0          0                       0   \n", "3421070                              0          0                       0   \n", "\n", "product_name  Banana  Blueberries  Boneless Skinless Chicken Breasts  \\\n", "order_id                                                               \n", "1                  0            0                                  0   \n", "36                 0            0                                  0   \n", "38                 0            0                                  0   \n", "96                 0            0                                  0   \n", "98                 0            0                                  0   \n", "...              ...          ...                                ...   \n", "3420998            0            0                                  0   \n", "3421049            0            0                                  0   \n", "3421056            0            0                                  0   \n", "3421063            0            0                                  0   \n", "3421070            0            0                                  0   \n", "\n", "product_name  Broccoli Crown  Bunched Cilantro  ...  Sparkling Lemon Water  \\\n", "order_id                                        ...                          \n", "1                          0                 0  ...                      0   \n", "36                         0                 0  ...                      0   \n", "38                         0                 0  ...                      0   \n", "96                         0                 0  ...                      0   \n", "98                         0                 0  ...                      0   \n", "...                      ...               ...  ...                    ...   \n", "3420998                    0                 0  ...                      0   \n", "3421049                    0                 0  ...                      0   \n", "3421056                    0                 0  ...                      1   \n", "3421063                    0                 0  ...                      0   \n", "3421070                    0                 0  ...                      0   \n", "\n", "product_name  Sparkling Natural Mineral Water  Sparkling Water Grapefruit  \\\n", "order_id                                                                    \n", "1                                           0                           0   \n", "36                                          0                           0   \n", "38                                          0                           0   \n", "96                                          0                           0   \n", "98                                          0                           0   \n", "...                                       ...                         ...   \n", "3420998                                     0                           0   \n", "3421049                                     0                           0   \n", "3421056                                     0                           0   \n", "3421063                                     0                           0   \n", "3421070                                     0                           0   \n", "\n", "product_name  Spring Water  Strawberries  Uncured Genoa Salami  \\\n", "order_id                                                         \n", "1                        0             0                     0   \n", "36                       1             0                     0   \n", "38                       0             0                     0   \n", "96                       0             0                     0   \n", "98                       0             0                     1   \n", "...                    ...           ...                   ...   \n", "3420998                  0             0                     1   \n", "3421049                  0             0                     0   \n", "3421056                  0             0                     0   \n", "3421063                  0             0                     0   \n", "3421070                  0             0                     0   \n", "\n", "product_name  Unsalted Butter  Unsweetened Almondmilk  \\\n", "order_id                                                \n", "1                           0                       0   \n", "36                          0                       0   \n", "38                          0                       0   \n", "96                          0                       0   \n", "98                          0                       0   \n", "...                       ...                     ...   \n", "3420998                     0                       0   \n", "3421049                     0                       0   \n", "3421056                     0                       0   \n", "3421063                     0                       0   \n", "3421070                     0                       0   \n", "\n", "product_name  Unsweetened Original Almond Breeze Almond Milk  Yellow Onions  \n", "order_id                                                                     \n", "1                                                          0              0  \n", "36                                                         0              0  \n", "38                                                         0              0  \n", "96                                                         0              0  \n", "98                                                         0              0  \n", "...                                                      ...            ...  \n", "3420998                                                    0              0  \n", "3421049                                                    0              0  \n", "3421056                                                    0              0  \n", "3421063                                                    0              0  \n", "3421070                                                    0              0  \n", "\n", "[93850 rows x 100 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["shortbasket = basket[:100000]\n", "shortbasket"]}, {"cell_type": "markdown", "id": "0b083b61", "metadata": {}, "source": ["####  Apriori is a popular algorithm  for extracting frequent itemsets with applications in association rule learning. The apriori algorithm has been designed to operate on databases containing transactions, such as purchases by customers of a store. An itemset is considered as \"frequent\" if it meets a user-specified support threshold.\n", "\n", "For instance, if the support threshold is set to 0.5 (50%), a frequent itemset is defined as a set of items that occur together in at least 50% of all transactions in the database."]}, {"cell_type": "code", "execution_count": 10, "id": "6b4305ad", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\mlxtend\\frequent_patterns\\fpcommon.py:111: DeprecationWarning: DataFrames with non-bool types result in worse computationalperformance and their support might be discontinued in the future.Please use a DataFrame with bool type\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing 15 combinations | Sampling itemset size 4 3\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "      <th>length</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.018668</td>\n", "      <td>(100% Whole Wheat Bread)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.013298</td>\n", "      <td>(2% Reduced Fat Milk)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.017304</td>\n", "      <td>(Apple Honeycrisp Organic)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.026180</td>\n", "      <td>(<PERSON><PERSON><PERSON><PERSON>)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.142376</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1570</th>\n", "      <td>0.001289</td>\n", "      <td>(Organic Whole Milk, Organic Raspberries, Orga...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1571</th>\n", "      <td>0.001002</td>\n", "      <td>(Organic Yellow Onion, Organic Raspberries, Or...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1572</th>\n", "      <td>0.001385</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1573</th>\n", "      <td>0.001087</td>\n", "      <td>(Bag of Organic Bananas, Organic Cucumber, Org...</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1574</th>\n", "      <td>0.001811</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1575 rows × 3 columns</p>\n", "</div>"], "text/plain": ["       support                                           itemsets  length\n", "0     0.018668                           (100% Whole Wheat Bread)       1\n", "1     0.013298                              (2% Reduced Fat Milk)       1\n", "2     0.017304                         (Apple Honeycrisp Organic)       1\n", "3     0.026180                                        (<PERSON>paragu<PERSON>)       1\n", "4     0.142376                           (Bag of Organic Bananas)       1\n", "...        ...                                                ...     ...\n", "1570  0.001289  (Organic Whole Milk, Organic Raspberries, Orga...       3\n", "1571  0.001002  (Organic Yellow Onion, Organic Raspberries, Or...       3\n", "1572  0.001385  (Bag of Organic Bananas, Organic Hass Avocado,...       4\n", "1573  0.001087  (Bag of Organic Bananas, Organic Cucumber, Org...       4\n", "1574  0.001811  (Bag of Organic Bananas, Organic Hass Avocado,...       4\n", "\n", "[1575 rows x 3 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from mlxtend.frequent_patterns import apriori\n", "from mlxtend.frequent_patterns import association_rules\n", "\n", "frequent_items = apriori(shortbasket, min_support=0.001, use_colnames=True , verbose =1 , low_memory=True)\n", "\n", "# The length column has been added to increase ease of filtering.\n", "frequent_items['length'] = frequent_items['itemsets'].apply(lambda x: len(x))\n", "frequent_items\n", "\n", "# min_support=0.01--> 108 item set a frequent itemset is defined as a set of items that occur together in at least 1% of all transactions \n", "# min_support=0.02--> 37 item set  a frequent itemset is defined as a set of items that occur together in at least 2% of all transactions\n", "# min_support=0.001--> 1575  item set  a frequent itemset is defined as a set of items that occur together in at least .1% of all transactions "]}, {"cell_type": "code", "execution_count": 11, "id": "598cb96c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "      <th>length</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.17642</td>\n", "      <td>(Banana)</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   support  itemsets  length\n", "5  0.17642  (<PERSON><PERSON>)       1"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["frequent_items[frequent_items['support'] == frequent_items.support.max()]"]}, {"cell_type": "code", "execution_count": 12, "id": "35db236e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}], "source": ["# the most 10 frequently occurring item in our dataset with item set of given length  \n", "def most_10_frequent_items(length):\n", "    return frequent_items.sort_values('support', ascending=False)[frequent_items['length'] == length].head(10)\n", "\n", "    "]}, {"cell_type": "code", "execution_count": 13, "id": "1642b6db", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n", "<ipython-input-12-576cd02e6170>:3: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  return  frequent_items.sort_values('support', ascending=False)[frequent_items['length'] == length].head(10)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "      <th>length</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.176420</td>\n", "      <td>(Banana)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.142376</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>0.091668</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>0.085828</td>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>0.066340</td>\n", "      <td>(Organic Avocado)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>0.064379</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>0.063111</td>\n", "      <td>(<PERSON> Lemon)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>0.050996</td>\n", "      <td>(Strawberries)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>0.045594</td>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>0.045115</td>\n", "      <td>(<PERSON><PERSON>)</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     support                  itemsets  length\n", "5   0.176420                  (Banana)       1\n", "4   0.142376  (Bag of Organic Bananas)       1\n", "71  0.091668    (Organic Strawberries)       1\n", "37  0.085828    (<PERSON>)       1\n", "33  0.066340         (Organic Avocado)       1\n", "57  0.064379    (Organic Hass Avocado)       1\n", "28  0.063111             (<PERSON>)       1\n", "94  0.050996            (Straw<PERSON>)       1\n", "65  0.045594     (Organic Raspberries)       1\n", "30  0.045115                   (<PERSON><PERSON>)       1"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# the most 10 frequently occurring item in our dataset with item set of length 1 \n", "\n", "most_10_frequent_items(1)\n", "# The output shows the Banana is the most frequently occurring item in our dataset"]}, {"cell_type": "code", "execution_count": 14, "id": "43f9d06f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n", "<ipython-input-12-576cd02e6170>:3: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  return  frequent_items.sort_values('support', ascending=False)[frequent_items['length'] == length].head(10)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "      <th>length</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>225</th>\n", "      <td>0.025456</td>\n", "      <td>(Bag of Organic Bananas, Organic Strawberries)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>0.021449</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>277</th>\n", "      <td>0.019318</td>\n", "      <td>(Banana, Organic Avocado)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>192</th>\n", "      <td>0.018498</td>\n", "      <td>(Bag of Organic Bananas, Organic Baby Spinach)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>314</th>\n", "      <td>0.017315</td>\n", "      <td>(Banana, Organic Strawberries)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>281</th>\n", "      <td>0.016665</td>\n", "      <td>(<PERSON><PERSON>, Organic Baby Spinach)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>272</th>\n", "      <td>0.016537</td>\n", "      <td>(Large Lemon, Banana)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>336</th>\n", "      <td>0.015248</td>\n", "      <td>(Banana, Strawberries)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>0.014811</td>\n", "      <td>(Bag of Organic Bananas, Organic Raspberries)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1171</th>\n", "      <td>0.013074</td>\n", "      <td>(Organic Raspberries, Organic Strawberries)</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       support                                        itemsets  length\n", "225   0.025456  (Bag of Organic Bananas, Organic Strawberries)       2\n", "211   0.021449  (Bag of Organic Bananas, Organic Hass Avocado)       2\n", "277   0.019318                       (<PERSON>ana, Organic Avocado)       2\n", "192   0.018498  (Bag of Organic Bananas, Organic Baby Spinach)       2\n", "314   0.017315                  (<PERSON><PERSON>, Organic Strawberries)       2\n", "281   0.016665                  (<PERSON><PERSON>, <PERSON> Baby <PERSON>)       2\n", "272   0.016537                           (Large <PERSON>, Banana)       2\n", "336   0.015248                          (<PERSON><PERSON>, Strawberries)       2\n", "219   0.014811   (Bag of Organic Bananas, Organic Raspberries)       2\n", "1171  0.013074     (Organic Raspberries, Organic Strawberries)       2"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# the most 10 frequently occurring item in our dataset with item set of length 2\n", "\n", "most_10_frequent_items(2)\n", "# The output shows that the Organic Strawberries & Bag of Organic Bananas combination are the most frequently occurring items when the length of the itemset is two."]}, {"cell_type": "code", "execution_count": 15, "id": "4575bfc3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n", "<ipython-input-12-576cd02e6170>:3: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  return  frequent_items.sort_values('support', ascending=False)[frequent_items['length'] == length].head(10)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "      <th>length</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1381</th>\n", "      <td>0.006063</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1400</th>\n", "      <td>0.005306</td>\n", "      <td>(Bag of Organic Bananas, Organic Raspberries, ...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1332</th>\n", "      <td>0.004752</td>\n", "      <td>(Bag of Organic Bananas, Organic Strawberries,...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1377</th>\n", "      <td>0.004454</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1326</th>\n", "      <td>0.004188</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1448</th>\n", "      <td>0.003964</td>\n", "      <td>(Banana, Organic Avocado, Organic Baby Spinach)</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1428</th>\n", "      <td>0.003580</td>\n", "      <td>(Large Lemon, Organic Avocado, Banana)</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1352</th>\n", "      <td>0.003101</td>\n", "      <td>(Bag of Organic Bananas, Organic Cucumber, Org...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1457</th>\n", "      <td>0.002952</td>\n", "      <td>(Banana, Organic Avocado, Organic Strawberries)</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1426</th>\n", "      <td>0.002941</td>\n", "      <td>(Large Lemon, Limes, Banana)</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       support                                           itemsets  length\n", "1381  0.006063  (Bag of Organic Bananas, Organic Hass Avocado,...       3\n", "1400  0.005306  (Bag of Organic Bananas, Organic Raspberries, ...       3\n", "1332  0.004752  (Bag of Organic Bananas, Organic Strawberries,...       3\n", "1377  0.004454  (Bag of Organic Bananas, Organic Hass Avocado,...       3\n", "1326  0.004188  (Bag of Organic Bananas, Organic Hass Avocado,...       3\n", "1448  0.003964    (<PERSON><PERSON>, Organic Avocado, Organic Baby Spinach)       3\n", "1428  0.003580             (Large Lemon, Organic Avocado, Banana)       3\n", "1352  0.003101  (Bag of Organic Bananas, Organic Cucumber, Org...       3\n", "1457  0.002952    (Banana, Organic Avocado, Organic Strawberries)       3\n", "1426  0.002941                       (Large <PERSON>, Limes, Banana)       3"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# the most 10 frequently occurring item in our dataset with item set of length 3 \n", "\n", "most_10_frequent_items(3)\n", "\n", "# The output shows the (Organic Hass Avocado, Organic Strawberries, Bag of Organic Bananas combination are the most frequently occurring items when the length of the itemset is three."]}, {"cell_type": "code", "execution_count": 16, "id": "5f26769a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n", "<ipython-input-12-576cd02e6170>:3: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  return  frequent_items.sort_values('support', ascending=False)[frequent_items['length'] == length].head(10)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "      <th>length</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1574</th>\n", "      <td>0.001811</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1572</th>\n", "      <td>0.001385</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1573</th>\n", "      <td>0.001087</td>\n", "      <td>(Bag of Organic Bananas, Organic Cucumber, Org...</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       support                                           itemsets  length\n", "1574  0.001811  (Bag of Organic Bananas, Organic Hass Avocado,...       4\n", "1572  0.001385  (Bag of Organic Bananas, Organic Hass Avocado,...       4\n", "1573  0.001087  (Bag of Organic Bananas, Organic Cucumber, Org...       4"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# the most 10 frequently occurring item in our dataset with item set of length 4 \n", "\n", "most_10_frequent_items(4)\n", "\n", "# The output shows the 'Organic Hass Avocado', 'Organic Raspberries', 'Organic Strawberries', 'Bag of Organic Bananas' combination are the most frequently occurring items when the length of the itemset is four."]}, {"cell_type": "code", "execution_count": 17, "id": "799afdb4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}], "source": ["#all items with a given length and at least support\n", "def all_items_with_at_least_support_and_len(support , len):\n", "    return frequent_items[ (frequent_items['length'] == len) & (frequent_items['support'] >= support) ]\n", "    "]}, {"cell_type": "code", "execution_count": 18, "id": "a23f7710", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "      <th>length</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>0.021449</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>225</th>\n", "      <td>0.025456</td>\n", "      <td>(Bag of Organic Bananas, Organic Strawberries)</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      support                                        itemsets  length\n", "211  0.021449  (Bag of Organic Bananas, Organic Hass Avocado)       2\n", "225  0.025456  (Bag of Organic Bananas, Organic Strawberries)       2"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["#all items with a length of 2, and the minimum support is more than 0.02 (occur together in at least 2% of all transactions)\n", "\n", "all_items_with_at_least_support_and_len(0.02 , 2)"]}, {"cell_type": "code", "execution_count": 19, "id": "8456ab31", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "      <th>length</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.142376</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.176420</td>\n", "      <td>(Banana)</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    support                  itemsets  length\n", "4  0.142376  (Bag of Organic Bananas)       1\n", "5  0.176420                  (Banana)       1"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["#all items with a length of 1, and the minimum support is more than 0.1 (occur in at least 10% of all transactions)\n", "\n", "all_items_with_at_least_support_and_len(0.1 , 1)"]}, {"cell_type": "code", "execution_count": 20, "id": "f706fbf7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}], "source": ["# showing support of itemset of product\n", "def show_itemset_support(frozenset):\n", "    return frequent_items[ frequent_items['itemsets'] == frozenset ]\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": 21, "id": "9ecca08e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "      <th>length</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1574</th>\n", "      <td>0.001811</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       support                                           itemsets  length\n", "1574  0.001811  (Bag of Organic Bananas, Organic Hass Avocado,...       4"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# show_itemset_support({'Organic Strawberries', 'Organic Baby Spinach'})\n", "# show_itemset_support({'Organic Hass Avocado','Bag of Organic Bananas'})\n", "# show_itemset_support({'Organic Hass Avocado','Organic Strawberries','Bag of Organic Bananas'})\n", "show_itemset_support({'Organic Hass Avocado','Organic Raspberries','Organic Strawberries','Bag of Organic Bananas'})\n"]}, {"cell_type": "markdown", "id": "785de704", "metadata": {}, "source": ["### The association rules :\n", "are simply the if-else statements. The IF component of an association rule is known as the antecedent. The THEN component is known as the consequent. The antecedent and the consequent are disjoint; they have no items in common."]}, {"cell_type": "markdown", "id": "dd495075", "metadata": {}, "source": ["> 1) **Support**: This measure gives an idea of how frequent an itemset is in all the transactions,  the fraction of the total number of transactions in which the itemset occurs.<br>\n", "2) **Confidence**: This says how likely item Y is purchased when item X is purchased, expressed as {X -> Y} , is the conditional probability of occurrence of consequent given the antecedent.<br>\n", "3) **Lift**: This says how likely item Y is purchased when item X is purchased, while controlling for how popular item Y is.<br>\n", "4) **Leverage** : how different is the co-occurrence of the antecedent X and the consequent Y of a rule from independence it takes values in the range [−0.25,0.25].<br>\n", "\n", "\n", "> low confidence **-->** due to few purchases of male cosmetics in general<br>\n", "left > 1  **-->**  item Y is likely to be bought if item X is bought<br>\n", "left < 1  **-->**  item Y is unlikely to be bought if item X is bought.<br>\n", "left = 1  **-->**  which implies no association between items<br>\n", "Finally, it should be noted that Support values equal to unity implies Lift values close to unity, but the opposite is not necessarily true<br>"]}, {"cell_type": "code", "execution_count": 22, "id": "dcf34362", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>antecedents</th>\n", "      <th>consequents</th>\n", "      <th>antecedent support</th>\n", "      <th>consequent support</th>\n", "      <th>support</th>\n", "      <th>confidence</th>\n", "      <th>lift</th>\n", "      <th>leverage</th>\n", "      <th>conviction</th>\n", "      <th>antecedent_len</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>(100% Whole Wheat Bread)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.018668</td>\n", "      <td>0.176420</td>\n", "      <td>0.004869</td>\n", "      <td>0.260845</td>\n", "      <td>1.478546</td>\n", "      <td>0.001576</td>\n", "      <td>1.114218</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>(Banana)</td>\n", "      <td>(100% Whole Wheat Bread)</td>\n", "      <td>0.176420</td>\n", "      <td>0.018668</td>\n", "      <td>0.004869</td>\n", "      <td>0.027602</td>\n", "      <td>1.478546</td>\n", "      <td>0.001576</td>\n", "      <td>1.009187</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>(100% Whole Wheat Bread)</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.018668</td>\n", "      <td>0.064379</td>\n", "      <td>0.001481</td>\n", "      <td>0.079338</td>\n", "      <td>1.232351</td>\n", "      <td>0.000279</td>\n", "      <td>1.016248</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(100% Whole Wheat Bread)</td>\n", "      <td>0.064379</td>\n", "      <td>0.018668</td>\n", "      <td>0.001481</td>\n", "      <td>0.023006</td>\n", "      <td>1.232351</td>\n", "      <td>0.000279</td>\n", "      <td>1.004440</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>(100% Whole Wheat Bread)</td>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>0.018668</td>\n", "      <td>0.045594</td>\n", "      <td>0.001183</td>\n", "      <td>0.063356</td>\n", "      <td>1.389571</td>\n", "      <td>0.000332</td>\n", "      <td>1.018964</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3863</th>\n", "      <td>(Organic Raspberries, Organic Strawberries)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.013074</td>\n", "      <td>0.021449</td>\n", "      <td>0.001811</td>\n", "      <td>0.138549</td>\n", "      <td>6.459440</td>\n", "      <td>0.001531</td>\n", "      <td>1.135934</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3864</th>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>(Organic Hass Avocado, Organic Raspberries, Or...</td>\n", "      <td>0.142376</td>\n", "      <td>0.002866</td>\n", "      <td>0.001811</td>\n", "      <td>0.012723</td>\n", "      <td>4.438737</td>\n", "      <td>0.001403</td>\n", "      <td>1.009983</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3865</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Bag of Organic Bananas, Organic Raspberries, ...</td>\n", "      <td>0.064379</td>\n", "      <td>0.005306</td>\n", "      <td>0.001811</td>\n", "      <td>0.028136</td>\n", "      <td>5.302408</td>\n", "      <td>0.001470</td>\n", "      <td>1.023491</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3866</th>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>0.045594</td>\n", "      <td>0.006063</td>\n", "      <td>0.001811</td>\n", "      <td>0.039729</td>\n", "      <td>6.552826</td>\n", "      <td>0.001535</td>\n", "      <td>1.035059</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3867</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>0.091668</td>\n", "      <td>0.004454</td>\n", "      <td>0.001811</td>\n", "      <td>0.019761</td>\n", "      <td>4.436669</td>\n", "      <td>0.001403</td>\n", "      <td>1.015615</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3868 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                                      antecedents  \\\n", "0                        (100% Whole Wheat Bread)   \n", "1                                        (<PERSON><PERSON>)   \n", "2                        (100% Whole Wheat Bread)   \n", "3                          (Organic Hass Avocado)   \n", "4                        (100% Whole Wheat Bread)   \n", "...                                           ...   \n", "3863  (Organic Raspberries, Organic Strawberries)   \n", "3864                     (Bag of Organic Bananas)   \n", "3865                       (Organic Hass Avocado)   \n", "3866                        (Organic Raspberries)   \n", "3867                       (Organic Strawberries)   \n", "\n", "                                            consequents  antecedent support  \\\n", "0                                              (Banana)            0.018668   \n", "1                              (100% Whole Wheat Bread)            0.176420   \n", "2                                (Organic Hass Avocado)            0.018668   \n", "3                              (100% Whole Wheat Bread)            0.064379   \n", "4                                 (Organic Raspberries)            0.018668   \n", "...                                                 ...                 ...   \n", "3863     (Bag of Organic Bananas, Organic Hass Avocado)            0.013074   \n", "3864  (Organic Hass Avocado, Organic Raspberries, Or...            0.142376   \n", "3865  (Bag of Organic Bananas, Organic Raspberries, ...            0.064379   \n", "3866  (Bag of Organic Bananas, Organic Hass Avocado,...            0.045594   \n", "3867  (Bag of Organic Bananas, Organic Hass Avocado,...            0.091668   \n", "\n", "      consequent support   support  confidence      lift  leverage  \\\n", "0               0.176420  0.004869    0.260845  1.478546  0.001576   \n", "1               0.018668  0.004869    0.027602  1.478546  0.001576   \n", "2               0.064379  0.001481    0.079338  1.232351  0.000279   \n", "3               0.018668  0.001481    0.023006  1.232351  0.000279   \n", "4               0.045594  0.001183    0.063356  1.389571  0.000332   \n", "...                  ...       ...         ...       ...       ...   \n", "3863            0.021449  0.001811    0.138549  6.459440  0.001531   \n", "3864            0.002866  0.001811    0.012723  4.438737  0.001403   \n", "3865            0.005306  0.001811    0.028136  5.302408  0.001470   \n", "3866            0.006063  0.001811    0.039729  6.552826  0.001535   \n", "3867            0.004454  0.001811    0.019761  4.436669  0.001403   \n", "\n", "      conviction  antecedent_len  \n", "0       1.114218               1  \n", "1       1.009187               1  \n", "2       1.016248               1  \n", "3       1.004440               1  \n", "4       1.018964               1  \n", "...          ...             ...  \n", "3863    1.135934               2  \n", "3864    1.009983               1  \n", "3865    1.023491               1  \n", "3866    1.035059               1  \n", "3867    1.015615               1  \n", "\n", "[3868 rows x 10 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# rules derived from the frequent itemsets only if the level of lift is above the 50 percent threshold\n", "rules = association_rules(frequent_items, metric='lift', min_threshold=1.1)\n", "rules[\"antecedent_len\"] = rules[\"antecedents\"].apply(lambda x: len(x))\n", "rules\n", "\n", "# left = 1 --> 3950 rule\n", "# left = 1.1 --> 3868 rule\n", "# left = .5 --> 4126 rule\n"]}, {"cell_type": "code", "execution_count": 23, "id": "5e891baa", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>antecedents</th>\n", "      <th>consequents</th>\n", "      <th>antecedent support</th>\n", "      <th>consequent support</th>\n", "      <th>support</th>\n", "      <th>confidence</th>\n", "      <th>lift</th>\n", "      <th>leverage</th>\n", "      <th>conviction</th>\n", "      <th>antecedent_len</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2132</th>\n", "      <td>(Apple Honeycrisp Organic, Organic Hass Avocado)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.002962</td>\n", "      <td>0.142376</td>\n", "      <td>0.001300</td>\n", "      <td>0.438849</td>\n", "      <td>3.082321</td>\n", "      <td>0.000878</td>\n", "      <td>1.528330</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2138</th>\n", "      <td>(Apple Honeycrisp Organic, Organic Strawberries)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.003559</td>\n", "      <td>0.142376</td>\n", "      <td>0.001598</td>\n", "      <td>0.449102</td>\n", "      <td>3.154333</td>\n", "      <td>0.001092</td>\n", "      <td>1.556774</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2139</th>\n", "      <td>(Apple Honeycrisp Organic, Bag of Organic Bana...</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.005147</td>\n", "      <td>0.091668</td>\n", "      <td>0.001598</td>\n", "      <td>0.310559</td>\n", "      <td>3.387884</td>\n", "      <td>0.001127</td>\n", "      <td>1.317491</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2199</th>\n", "      <td>(Bag of Organic Bananas, Fresh Cauliflower)</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.004166</td>\n", "      <td>0.091668</td>\n", "      <td>0.001289</td>\n", "      <td>0.309463</td>\n", "      <td>3.375926</td>\n", "      <td>0.000907</td>\n", "      <td>1.315400</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2212</th>\n", "      <td>(Organic Strawberries, Large Alfresco Eggs)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.002557</td>\n", "      <td>0.142376</td>\n", "      <td>0.001108</td>\n", "      <td>0.433333</td>\n", "      <td>3.043581</td>\n", "      <td>0.000744</td>\n", "      <td>1.513454</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3841</th>\n", "      <td>(Bag of Organic Bananas, Organic Cucumber, Org...</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.003101</td>\n", "      <td>0.064379</td>\n", "      <td>0.001087</td>\n", "      <td>0.350515</td>\n", "      <td>5.444534</td>\n", "      <td>0.000887</td>\n", "      <td>1.440559</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3843</th>\n", "      <td>(Organic Cucumber, Organic Hass Avocado, Organ...</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.001811</td>\n", "      <td>0.142376</td>\n", "      <td>0.001087</td>\n", "      <td>0.600000</td>\n", "      <td>4.214189</td>\n", "      <td>0.000829</td>\n", "      <td>2.144060</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3854</th>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.004454</td>\n", "      <td>0.091668</td>\n", "      <td>0.001811</td>\n", "      <td>0.406699</td>\n", "      <td>4.436669</td>\n", "      <td>0.001403</td>\n", "      <td>1.530980</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3856</th>\n", "      <td>(Bag of Organic Bananas, Organic Raspberries, ...</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.005306</td>\n", "      <td>0.064379</td>\n", "      <td>0.001811</td>\n", "      <td>0.341365</td>\n", "      <td>5.302408</td>\n", "      <td>0.001470</td>\n", "      <td>1.420546</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3857</th>\n", "      <td>(Organic Hass Avocado, Organic Raspberries, Or...</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.002866</td>\n", "      <td>0.142376</td>\n", "      <td>0.001811</td>\n", "      <td>0.631970</td>\n", "      <td>4.438737</td>\n", "      <td>0.001403</td>\n", "      <td>2.330311</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>112 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                                            antecedents  \\\n", "2132   (Apple Honeycrisp Organic, Organic Hass Avocado)   \n", "2138   (Apple Honeycrisp Organic, Organic Strawberries)   \n", "2139  (Apple Honeycrisp Organic, Bag of Organic Bana...   \n", "2199        (Bag of Organic Bananas, <PERSON> Cauliflower)   \n", "2212        (Organic Strawberries, Large Alfresco Eggs)   \n", "...                                                 ...   \n", "3841  (Bag of Organic Bananas, Organic Cucumber, Org...   \n", "3843  (Organic Cucumber, Organic Hass Avocado, Organ...   \n", "3854  (Bag of Organic Bananas, Organic Hass Avocado,...   \n", "3856  (Bag of Organic Bananas, Organic Raspberries, ...   \n", "3857  (Organic Hass Avocado, Organic Raspberries, Or...   \n", "\n", "                   consequents  antecedent support  consequent support  \\\n", "2132  (Bag of Organic Bananas)            0.002962            0.142376   \n", "2138  (Bag of Organic Bananas)            0.003559            0.142376   \n", "2139    (Organic Strawberries)            0.005147            0.091668   \n", "2199    (Organic Strawberries)            0.004166            0.091668   \n", "2212  (Bag of Organic Bananas)            0.002557            0.142376   \n", "...                        ...                 ...                 ...   \n", "3841    (Organic Hass Avocado)            0.003101            0.064379   \n", "3843  (Bag of Organic Bananas)            0.001811            0.142376   \n", "3854    (Organic Strawberries)            0.004454            0.091668   \n", "3856    (Organic Hass Avocado)            0.005306            0.064379   \n", "3857  (Bag of Organic Bananas)            0.002866            0.142376   \n", "\n", "       support  confidence      lift  leverage  conviction  antecedent_len  \n", "2132  0.001300    0.438849  3.082321  0.000878    1.528330               2  \n", "2138  0.001598    0.449102  3.154333  0.001092    1.556774               2  \n", "2139  0.001598    0.310559  3.387884  0.001127    1.317491               2  \n", "2199  0.001289    0.309463  3.375926  0.000907    1.315400               2  \n", "2212  0.001108    0.433333  3.043581  0.000744    1.513454               2  \n", "...        ...         ...       ...       ...         ...             ...  \n", "3841  0.001087    0.350515  5.444534  0.000887    1.440559               3  \n", "3843  0.001087    0.600000  4.214189  0.000829    2.144060               3  \n", "3854  0.001811    0.406699  4.436669  0.001403    1.530980               3  \n", "3856  0.001811    0.341365  5.302408  0.001470    1.420546               3  \n", "3857  0.001811    0.631970  4.438737  0.001403    2.330311               3  \n", "\n", "[112 rows x 10 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# rules that satisfy the following criteria: at least 2 antecedents , a confidence > 0.75 & a lift score > 1.2\n", "rules[ (rules['antecedent_len'] >= 1) &\n", "       (rules['confidence'] >= .3) &\n", "       (rules['lift'] >= 3) ]"]}, {"cell_type": "markdown", "id": "6fc7ef60", "metadata": {}, "source": ["rules based on huge num of items --> support\n", "rules based on \n", "rules based on"]}, {"cell_type": "code", "execution_count": 25, "id": "c639f911", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}], "source": ["# sort rules ascending based on metric\n", "def sorted_rules_asc_based_on_metric(metric='support'):\n", "    return rules.sort_values(metric)"]}, {"cell_type": "code", "execution_count": 41, "id": "4e3f7aa9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>antecedents</th>\n", "      <th>consequents</th>\n", "      <th>antecedent support</th>\n", "      <th>consequent support</th>\n", "      <th>support</th>\n", "      <th>confidence</th>\n", "      <th>lift</th>\n", "      <th>leverage</th>\n", "      <th>conviction</th>\n", "      <th>antecedent_len</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>290</th>\n", "      <td>(Banana)</td>\n", "      <td>(Organic Baby Arugula)</td>\n", "      <td>0.176420</td>\n", "      <td>0.022067</td>\n", "      <td>0.004283</td>\n", "      <td>0.024280</td>\n", "      <td>1.100268</td>\n", "      <td>0.000390</td>\n", "      <td>1.002268</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>291</th>\n", "      <td>(Organic Baby Arugula)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.022067</td>\n", "      <td>0.176420</td>\n", "      <td>0.004283</td>\n", "      <td>0.194109</td>\n", "      <td>1.100268</td>\n", "      <td>0.000390</td>\n", "      <td>1.021950</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>292</th>\n", "      <td>(Banana)</td>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>0.176420</td>\n", "      <td>0.085828</td>\n", "      <td>0.016665</td>\n", "      <td>0.094462</td>\n", "      <td>1.100586</td>\n", "      <td>0.001523</td>\n", "      <td>1.009534</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>293</th>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.085828</td>\n", "      <td>0.176420</td>\n", "      <td>0.016665</td>\n", "      <td>0.194165</td>\n", "      <td>1.100586</td>\n", "      <td>0.001523</td>\n", "      <td>1.022021</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1536</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Organic Fuji Apple)</td>\n", "      <td>0.064379</td>\n", "      <td>0.025850</td>\n", "      <td>0.001833</td>\n", "      <td>0.028467</td>\n", "      <td>1.101263</td>\n", "      <td>0.000169</td>\n", "      <td>1.002694</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3452</th>\n", "      <td>(Sparkling Water Grapefruit)</td>\n", "      <td>(Sparkling Lemon Water, Lime Sparkling Water)</td>\n", "      <td>0.028364</td>\n", "      <td>0.002706</td>\n", "      <td>0.001279</td>\n", "      <td>0.045079</td>\n", "      <td>16.656117</td>\n", "      <td>0.001202</td>\n", "      <td>1.044373</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3451</th>\n", "      <td>(Sparkling Lemon Water)</td>\n", "      <td>(Lime Sparkling Water, Sparkling Water Grapefr...</td>\n", "      <td>0.011465</td>\n", "      <td>0.004092</td>\n", "      <td>0.001279</td>\n", "      <td>0.111524</td>\n", "      <td>27.256622</td>\n", "      <td>0.001232</td>\n", "      <td>1.120918</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3450</th>\n", "      <td>(Lime Sparkling Water, Sparkling Water Grapefr...</td>\n", "      <td>(Sparkling Lemon Water)</td>\n", "      <td>0.004092</td>\n", "      <td>0.011465</td>\n", "      <td>0.001279</td>\n", "      <td>0.312500</td>\n", "      <td>27.256622</td>\n", "      <td>0.001232</td>\n", "      <td>1.437869</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3453</th>\n", "      <td>(Lime Spa<PERSON>ling Water)</td>\n", "      <td>(Sparkling Lemon Water, Sparkling Water Grapef...</td>\n", "      <td>0.015727</td>\n", "      <td>0.002664</td>\n", "      <td>0.001279</td>\n", "      <td>0.081301</td>\n", "      <td>30.520325</td>\n", "      <td>0.001237</td>\n", "      <td>1.085596</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3448</th>\n", "      <td>(Sparkling Lemon Water, Sparkling Water Grapef...</td>\n", "      <td>(Lime Spa<PERSON>ling Water)</td>\n", "      <td>0.002664</td>\n", "      <td>0.015727</td>\n", "      <td>0.001279</td>\n", "      <td>0.480000</td>\n", "      <td>30.520325</td>\n", "      <td>0.001237</td>\n", "      <td>1.892832</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3868 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                                            antecedents  \\\n", "290                                            (<PERSON><PERSON>)   \n", "291                              (Organic Baby Arugula)   \n", "292                                            (<PERSON><PERSON>)   \n", "293                              (<PERSON> Baby Spinach)   \n", "1536                             (Organic Hass Avocado)   \n", "...                                                 ...   \n", "3452                       (Sparkling Water Grapefruit)   \n", "3451                            (Sparkling Lemon Water)   \n", "3450  (Lime Sparkling Water, Sparkling Water Grapefr...   \n", "3453                             (Lime Sparkling Water)   \n", "3448  (Sparkling Lemon Water, Sparkling Water Grapef...   \n", "\n", "                                            consequents  antecedent support  \\\n", "290                              (Organic Baby Arugula)            0.176420   \n", "291                                            (Banana)            0.022067   \n", "292                              (<PERSON> Baby Spinach)            0.176420   \n", "293                                            (Banana)            0.085828   \n", "1536                               (Organic Fuji Apple)            0.064379   \n", "...                                                 ...                 ...   \n", "3452      (Sparkling Lemon Water, Lime Sparkling Water)            0.028364   \n", "3451  (Lime Sparkling Water, Sparkling Water Grapefr...            0.011465   \n", "3450                            (Sparkling Lemon Water)            0.004092   \n", "3453  (Sparkling Lemon Water, Sparkling Water Grapef...            0.015727   \n", "3448                             (<PERSON>e <PERSON>ling <PERSON>)            0.002664   \n", "\n", "      consequent support   support  confidence       lift  leverage  \\\n", "290             0.022067  0.004283    0.024280   1.100268  0.000390   \n", "291             0.176420  0.004283    0.194109   1.100268  0.000390   \n", "292             0.085828  0.016665    0.094462   1.100586  0.001523   \n", "293             0.176420  0.016665    0.194165   1.100586  0.001523   \n", "1536            0.025850  0.001833    0.028467   1.101263  0.000169   \n", "...                  ...       ...         ...        ...       ...   \n", "3452            0.002706  0.001279    0.045079  16.656117  0.001202   \n", "3451            0.004092  0.001279    0.111524  27.256622  0.001232   \n", "3450            0.011465  0.001279    0.312500  27.256622  0.001232   \n", "3453            0.002664  0.001279    0.081301  30.520325  0.001237   \n", "3448            0.015727  0.001279    0.480000  30.520325  0.001237   \n", "\n", "      conviction  antecedent_len  \n", "290     1.002268               1  \n", "291     1.021950               1  \n", "292     1.009534               1  \n", "293     1.022021               1  \n", "1536    1.002694               1  \n", "...          ...             ...  \n", "3452    1.044373               1  \n", "3451    1.120918               1  \n", "3450    1.437869               2  \n", "3453    1.085596               1  \n", "3448    1.892832               2  \n", "\n", "[3868 rows x 10 columns]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["sorted_rules_asc_based_on_metric('lift')"]}, {"cell_type": "code", "execution_count": 123, "id": "ccddac47", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/plain": ["3"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["rules['leverage'].min()\n", "# rules['confidence'].min()\n", "# rules['lift'].min()\n", "# rules['leverage'].min()\n", "# rules['conviction'].min()\n", "# rules['antecedent_len'].max()"]}, {"cell_type": "code", "execution_count": 113, "id": "948dec13", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}], "source": ["# the fun. initialized with min. value for each metric\n", "def rules_with_specific_threshold(support=.001,confidence=.005,lift=1.1,leverage=9.5e-05,conviction=1):\n", "    return rules[ (rules['support'] >= support) &\n", "       (rules['confidence'] >= confidence) &\n", "       (rules['lift'] >= lift)&\n", "       (rules['leverage'] >= leverage) &\n", "       (rules['conviction'] >= conviction)  ]"]}, {"cell_type": "code", "execution_count": 125, "id": "6889a2a2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>antecedents</th>\n", "      <th>consequents</th>\n", "      <th>antecedent support</th>\n", "      <th>consequent support</th>\n", "      <th>support</th>\n", "      <th>confidence</th>\n", "      <th>lift</th>\n", "      <th>leverage</th>\n", "      <th>conviction</th>\n", "      <th>antecedent_len</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.142376</td>\n", "      <td>0.064379</td>\n", "      <td>0.021449</td>\n", "      <td>0.150651</td>\n", "      <td>2.340054</td>\n", "      <td>0.012283</td>\n", "      <td>1.101574</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.064379</td>\n", "      <td>0.142376</td>\n", "      <td>0.021449</td>\n", "      <td>0.333168</td>\n", "      <td>2.340054</td>\n", "      <td>0.012283</td>\n", "      <td>1.286117</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.142376</td>\n", "      <td>0.091668</td>\n", "      <td>0.025456</td>\n", "      <td>0.178791</td>\n", "      <td>1.950424</td>\n", "      <td>0.012404</td>\n", "      <td>1.106091</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.091668</td>\n", "      <td>0.142376</td>\n", "      <td>0.025456</td>\n", "      <td>0.277694</td>\n", "      <td>1.950424</td>\n", "      <td>0.012404</td>\n", "      <td>1.187341</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  antecedents               consequents  antecedent support  \\\n", "186  (Bag of Organic Bananas)    (Organic Hass Avocado)            0.142376   \n", "187    (Organic Hass Avocado)  (Bag of Organic Bananas)            0.064379   \n", "214  (Bag of Organic Bananas)    (Organic Strawberries)            0.142376   \n", "215    (Organic Strawberries)  (Bag of Organic Bananas)            0.091668   \n", "\n", "     consequent support   support  confidence      lift  leverage  conviction  \\\n", "186            0.064379  0.021449    0.150651  2.340054  0.012283    1.101574   \n", "187            0.142376  0.021449    0.333168  2.340054  0.012283    1.286117   \n", "214            0.091668  0.025456    0.178791  1.950424  0.012404    1.106091   \n", "215            0.142376  0.025456    0.277694  1.950424  0.012404    1.187341   \n", "\n", "     antecedent_len  \n", "186               1  \n", "187               1  \n", "214               1  \n", "215               1  "]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["# rules_with_specific_threshold(lift=3)\n", "rules_with_specific_threshold(support=.021,lift=1)\n", "\n"]}, {"cell_type": "code", "execution_count": 119, "id": "477b50db", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}], "source": ["def select_rules_with_antecedents_length(len):\n", "    return rules[ rules['antecedent_len'] == len]"]}, {"cell_type": "code", "execution_count": 126, "id": "ea3835eb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>antecedents</th>\n", "      <th>consequents</th>\n", "      <th>antecedent support</th>\n", "      <th>consequent support</th>\n", "      <th>support</th>\n", "      <th>confidence</th>\n", "      <th>lift</th>\n", "      <th>leverage</th>\n", "      <th>conviction</th>\n", "      <th>antecedent_len</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3826</th>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>0.006063</td>\n", "      <td>0.085828</td>\n", "      <td>0.001385</td>\n", "      <td>0.228471</td>\n", "      <td>2.661950</td>\n", "      <td>0.000865</td>\n", "      <td>1.184883</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3827</th>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.004188</td>\n", "      <td>0.091668</td>\n", "      <td>0.001385</td>\n", "      <td>0.330789</td>\n", "      <td>3.608570</td>\n", "      <td>0.001001</td>\n", "      <td>1.357318</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3828</th>\n", "      <td>(Bag of Organic Bananas, Organic Strawberries,...</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.004752</td>\n", "      <td>0.064379</td>\n", "      <td>0.001385</td>\n", "      <td>0.291480</td>\n", "      <td>4.527537</td>\n", "      <td>0.001079</td>\n", "      <td>1.320528</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3829</th>\n", "      <td>(Organic Hass Avocado, Organic Strawberries, O...</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.002515</td>\n", "      <td>0.142376</td>\n", "      <td>0.001385</td>\n", "      <td>0.550847</td>\n", "      <td>3.868959</td>\n", "      <td>0.001027</td>\n", "      <td>1.909427</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3840</th>\n", "      <td>(Bag of Organic Bananas, Organic Cucumber, Org...</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.002760</td>\n", "      <td>0.091668</td>\n", "      <td>0.001087</td>\n", "      <td>0.393822</td>\n", "      <td>4.296203</td>\n", "      <td>0.000834</td>\n", "      <td>1.498459</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3841</th>\n", "      <td>(Bag of Organic Bananas, Organic Cucumber, Org...</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.003101</td>\n", "      <td>0.064379</td>\n", "      <td>0.001087</td>\n", "      <td>0.350515</td>\n", "      <td>5.444534</td>\n", "      <td>0.000887</td>\n", "      <td>1.440559</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3842</th>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>(Organic Cucumber)</td>\n", "      <td>0.006063</td>\n", "      <td>0.032040</td>\n", "      <td>0.001087</td>\n", "      <td>0.179262</td>\n", "      <td>5.594854</td>\n", "      <td>0.000893</td>\n", "      <td>1.179377</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3843</th>\n", "      <td>(Organic Cucumber, Organic Hass Avocado, Organ...</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.001811</td>\n", "      <td>0.142376</td>\n", "      <td>0.001087</td>\n", "      <td>0.600000</td>\n", "      <td>4.214189</td>\n", "      <td>0.000829</td>\n", "      <td>2.144060</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3854</th>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.004454</td>\n", "      <td>0.091668</td>\n", "      <td>0.001811</td>\n", "      <td>0.406699</td>\n", "      <td>4.436669</td>\n", "      <td>0.001403</td>\n", "      <td>1.530980</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3855</th>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado,...</td>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>0.006063</td>\n", "      <td>0.045594</td>\n", "      <td>0.001811</td>\n", "      <td>0.298770</td>\n", "      <td>6.552826</td>\n", "      <td>0.001535</td>\n", "      <td>1.361045</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3856</th>\n", "      <td>(Bag of Organic Bananas, Organic Raspberries, ...</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.005306</td>\n", "      <td>0.064379</td>\n", "      <td>0.001811</td>\n", "      <td>0.341365</td>\n", "      <td>5.302408</td>\n", "      <td>0.001470</td>\n", "      <td>1.420546</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3857</th>\n", "      <td>(Organic Hass Avocado, Organic Raspberries, Or...</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.002866</td>\n", "      <td>0.142376</td>\n", "      <td>0.001811</td>\n", "      <td>0.631970</td>\n", "      <td>4.438737</td>\n", "      <td>0.001403</td>\n", "      <td>2.330311</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            antecedents  \\\n", "3826  (Bag of Organic Bananas, Organic Hass Avocado,...   \n", "3827  (Bag of Organic Bananas, Organic Hass Avocado,...   \n", "3828  (Bag of Organic Bananas, Organic Strawberries,...   \n", "3829  (Organic Hass Avocado, Organic Strawberries, O...   \n", "3840  (Bag of Organic Bananas, Organic Cucumber, Org...   \n", "3841  (Bag of Organic Bananas, Organic Cucumber, Org...   \n", "3842  (Bag of Organic Bananas, Organic Hass Avocado,...   \n", "3843  (Organic Cucumber, Organic Hass Avocado, Organ...   \n", "3854  (Bag of Organic Bananas, Organic Hass Avocado,...   \n", "3855  (Bag of Organic Bananas, Organic Hass Avocado,...   \n", "3856  (Bag of Organic Bananas, Organic Raspberries, ...   \n", "3857  (Organic Hass Avocado, Organic Raspberries, Or...   \n", "\n", "                   consequents  antecedent support  consequent support  \\\n", "3826    (<PERSON>)            0.006063            0.085828   \n", "3827    (Organic Strawberries)            0.004188            0.091668   \n", "3828    (Organic Hass Avocado)            0.004752            0.064379   \n", "3829  (Bag of Organic Bananas)            0.002515            0.142376   \n", "3840    (Organic Strawberries)            0.002760            0.091668   \n", "3841    (Organic Hass Avocado)            0.003101            0.064379   \n", "3842        (Organic Cucumber)            0.006063            0.032040   \n", "3843  (Bag of Organic Bananas)            0.001811            0.142376   \n", "3854    (Organic Strawberries)            0.004454            0.091668   \n", "3855     (Organic Raspberries)            0.006063            0.045594   \n", "3856    (Organic Hass Avocado)            0.005306            0.064379   \n", "3857  (Bag of Organic Bananas)            0.002866            0.142376   \n", "\n", "       support  confidence      lift  leverage  conviction  antecedent_len  \n", "3826  0.001385    0.228471  2.661950  0.000865    1.184883               3  \n", "3827  0.001385    0.330789  3.608570  0.001001    1.357318               3  \n", "3828  0.001385    0.291480  4.527537  0.001079    1.320528               3  \n", "3829  0.001385    0.550847  3.868959  0.001027    1.909427               3  \n", "3840  0.001087    0.393822  4.296203  0.000834    1.498459               3  \n", "3841  0.001087    0.350515  5.444534  0.000887    1.440559               3  \n", "3842  0.001087    0.179262  5.594854  0.000893    1.179377               3  \n", "3843  0.001087    0.600000  4.214189  0.000829    2.144060               3  \n", "3854  0.001811    0.406699  4.436669  0.001403    1.530980               3  \n", "3855  0.001811    0.298770  6.552826  0.001535    1.361045               3  \n", "3856  0.001811    0.341365  5.302408  0.001470    1.420546               3  \n", "3857  0.001811    0.631970  4.438737  0.001403    2.330311               3  "]}, "execution_count": 126, "metadata": {}, "output_type": "execute_result"}], "source": ["select_rules_with_antecedents_length(3)"]}, {"cell_type": "code", "execution_count": 94, "id": "cc874d8b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}], "source": ["# take set of antecedents names max names is 3\n", "def select_rules_with_antecedents_names(names=set()):\n", "    return rules[rules['antecedents'].apply(lambda x:  names in {x})]\n"]}, {"cell_type": "code", "execution_count": 101, "id": "8321cd1f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>antecedents</th>\n", "      <th>consequents</th>\n", "      <th>antecedent support</th>\n", "      <th>consequent support</th>\n", "      <th>support</th>\n", "      <th>confidence</th>\n", "      <th>lift</th>\n", "      <th>leverage</th>\n", "      <th>conviction</th>\n", "      <th>antecedent_len</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(100% Whole Wheat Bread)</td>\n", "      <td>0.064379</td>\n", "      <td>0.018668</td>\n", "      <td>0.001481</td>\n", "      <td>0.023006</td>\n", "      <td>1.232351</td>\n", "      <td>0.000279</td>\n", "      <td>1.004440</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Apple Honeycrisp Organic)</td>\n", "      <td>0.064379</td>\n", "      <td>0.017304</td>\n", "      <td>0.002962</td>\n", "      <td>0.046011</td>\n", "      <td>2.658963</td>\n", "      <td>0.001848</td>\n", "      <td>1.030092</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(<PERSON><PERSON><PERSON><PERSON>)</td>\n", "      <td>0.064379</td>\n", "      <td>0.026180</td>\n", "      <td>0.002920</td>\n", "      <td>0.045349</td>\n", "      <td>1.732204</td>\n", "      <td>0.001234</td>\n", "      <td>1.020080</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.064379</td>\n", "      <td>0.142376</td>\n", "      <td>0.021449</td>\n", "      <td>0.333168</td>\n", "      <td>2.340054</td>\n", "      <td>0.012283</td>\n", "      <td>1.286117</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>424</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Carrots)</td>\n", "      <td>0.064379</td>\n", "      <td>0.017507</td>\n", "      <td>0.001801</td>\n", "      <td>0.027971</td>\n", "      <td>1.597727</td>\n", "      <td>0.000674</td>\n", "      <td>1.010765</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3794</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Organic Yellow Onion, Organic Strawberries)</td>\n", "      <td>0.064379</td>\n", "      <td>0.005178</td>\n", "      <td>0.001513</td>\n", "      <td>0.023502</td>\n", "      <td>4.538430</td>\n", "      <td>0.001180</td>\n", "      <td>1.018765</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3799</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Organic Zucchini, Organic Strawberries)</td>\n", "      <td>0.064379</td>\n", "      <td>0.005519</td>\n", "      <td>0.001247</td>\n", "      <td>0.019364</td>\n", "      <td>3.508404</td>\n", "      <td>0.000891</td>\n", "      <td>1.014118</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3837</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Bag of Organic Bananas, Organic Strawberries,...</td>\n", "      <td>0.064379</td>\n", "      <td>0.004752</td>\n", "      <td>0.001385</td>\n", "      <td>0.021516</td>\n", "      <td>4.527537</td>\n", "      <td>0.001079</td>\n", "      <td>1.017132</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3852</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Bag of Organic Bananas, Organic Cucumber, Org...</td>\n", "      <td>0.064379</td>\n", "      <td>0.003101</td>\n", "      <td>0.001087</td>\n", "      <td>0.016882</td>\n", "      <td>5.444534</td>\n", "      <td>0.000887</td>\n", "      <td>1.014018</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3865</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Bag of Organic Bananas, Organic Raspberries, ...</td>\n", "      <td>0.064379</td>\n", "      <td>0.005306</td>\n", "      <td>0.001811</td>\n", "      <td>0.028136</td>\n", "      <td>5.302408</td>\n", "      <td>0.001470</td>\n", "      <td>1.023491</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>139 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                 antecedents  \\\n", "3     (Organic Hass Avocado)   \n", "33    (Organic Hass Avocado)   \n", "88    (Organic Hass Avocado)   \n", "187   (Organic Hass Avocado)   \n", "424   (Organic Hass Avocado)   \n", "...                      ...   \n", "3794  (Organic Hass Avocado)   \n", "3799  (Organic Hass Avocado)   \n", "3837  (Organic Hass Avocado)   \n", "3852  (Organic Hass Avocado)   \n", "3865  (Organic Hass Avocado)   \n", "\n", "                                            consequents  antecedent support  \\\n", "3                              (100% Whole Wheat Bread)            0.064379   \n", "33                           (Apple Honeycrisp Organic)            0.064379   \n", "88                                          (Asparagus)            0.064379   \n", "187                            (Bag of Organic Bananas)            0.064379   \n", "424                                           (Carrots)            0.064379   \n", "...                                                 ...                 ...   \n", "3794       (Organic Yellow Onion, Organic Strawberries)            0.064379   \n", "3799           (Organic Zucchini, Organic Strawberries)            0.064379   \n", "3837  (Bag of Organic Bananas, Organic Strawberries,...            0.064379   \n", "3852  (Bag of Organic Bananas, Organic Cucumber, Org...            0.064379   \n", "3865  (Bag of Organic Bananas, Organic Raspberries, ...            0.064379   \n", "\n", "      consequent support   support  confidence      lift  leverage  \\\n", "3               0.018668  0.001481    0.023006  1.232351  0.000279   \n", "33              0.017304  0.002962    0.046011  2.658963  0.001848   \n", "88              0.026180  0.002920    0.045349  1.732204  0.001234   \n", "187             0.142376  0.021449    0.333168  2.340054  0.012283   \n", "424             0.017507  0.001801    0.027971  1.597727  0.000674   \n", "...                  ...       ...         ...       ...       ...   \n", "3794            0.005178  0.001513    0.023502  4.538430  0.001180   \n", "3799            0.005519  0.001247    0.019364  3.508404  0.000891   \n", "3837            0.004752  0.001385    0.021516  4.527537  0.001079   \n", "3852            0.003101  0.001087    0.016882  5.444534  0.000887   \n", "3865            0.005306  0.001811    0.028136  5.302408  0.001470   \n", "\n", "      conviction  antecedent_len  \n", "3       1.004440               1  \n", "33      1.030092               1  \n", "88      1.020080               1  \n", "187     1.286117               1  \n", "424     1.010765               1  \n", "...          ...             ...  \n", "3794    1.018765               1  \n", "3799    1.014118               1  \n", "3837    1.017132               1  \n", "3852    1.014018               1  \n", "3865    1.023491               1  \n", "\n", "[139 rows x 10 columns]"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["# select_rules_with_antecedents_names({'Small Hass Avocado'})\n", "# select_rules_with_antecedents_names({'Broccoli Crown'})\n", "# select_rules_with_antecedents_names({'Bag of Organic Bananas','Organic Hass Avocado'})\n", "select_rules_with_antecedents_names({'Organic Hass Avocado'})"]}, {"cell_type": "code", "execution_count": 102, "id": "617b38ac", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}], "source": ["# take set of consequents names max names is 3\n", "def select_rules_with_consequents_names(names=set()):\n", "    return rules[rules['consequents'].apply(lambda x:  names in {x})]\n"]}, {"cell_type": "code", "execution_count": 104, "id": "bd5cf54e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\ipykernel\\ipkernel.py:287: DeprecationWarning: `should_run_async` will not call `transform_cell` automatically in the future. Please pass the result to `transformed_cell` argument and any exception that happen during thetransform in `preprocessing_exc_tuple` in IPython 7.17 and above.\n", "  and should_run_async(code)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>antecedents</th>\n", "      <th>consequents</th>\n", "      <th>antecedent support</th>\n", "      <th>consequent support</th>\n", "      <th>support</th>\n", "      <th>confidence</th>\n", "      <th>lift</th>\n", "      <th>leverage</th>\n", "      <th>conviction</th>\n", "      <th>antecedent_len</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2135</th>\n", "      <td>(Apple Honeycrisp Organic)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.017304</td>\n", "      <td>0.021449</td>\n", "      <td>0.001300</td>\n", "      <td>0.075123</td>\n", "      <td>3.502388</td>\n", "      <td>0.000929</td>\n", "      <td>1.058034</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2155</th>\n", "      <td>(<PERSON><PERSON><PERSON><PERSON>)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.026180</td>\n", "      <td>0.021449</td>\n", "      <td>0.001108</td>\n", "      <td>0.042328</td>\n", "      <td>1.973416</td>\n", "      <td>0.000547</td>\n", "      <td>1.021802</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2197</th>\n", "      <td>(<PERSON>)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.019382</td>\n", "      <td>0.021449</td>\n", "      <td>0.001012</td>\n", "      <td>0.052226</td>\n", "      <td>2.434902</td>\n", "      <td>0.000597</td>\n", "      <td>1.032473</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2234</th>\n", "      <td>(<PERSON> Lemon)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.063111</td>\n", "      <td>0.021449</td>\n", "      <td>0.001492</td>\n", "      <td>0.023637</td>\n", "      <td>1.101988</td>\n", "      <td>0.000138</td>\n", "      <td>1.002241</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2259</th>\n", "      <td>(<PERSON><PERSON>)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.045115</td>\n", "      <td>0.021449</td>\n", "      <td>0.001737</td>\n", "      <td>0.038498</td>\n", "      <td>1.794846</td>\n", "      <td>0.000769</td>\n", "      <td>1.017731</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2277</th>\n", "      <td>(Michigan Organic Kale)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.020714</td>\n", "      <td>0.021449</td>\n", "      <td>0.001044</td>\n", "      <td>0.050412</td>\n", "      <td>2.350284</td>\n", "      <td>0.000600</td>\n", "      <td>1.030500</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2301</th>\n", "      <td>(Organic Baby Arugula)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.022067</td>\n", "      <td>0.021449</td>\n", "      <td>0.001023</td>\n", "      <td>0.046354</td>\n", "      <td>2.161134</td>\n", "      <td>0.000550</td>\n", "      <td>1.026116</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2313</th>\n", "      <td>(Organic Baby Carrots)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.026414</td>\n", "      <td>0.021449</td>\n", "      <td>0.001343</td>\n", "      <td>0.050827</td>\n", "      <td>2.369652</td>\n", "      <td>0.000776</td>\n", "      <td>1.030951</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2373</th>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.085828</td>\n", "      <td>0.021449</td>\n", "      <td>0.004188</td>\n", "      <td>0.048790</td>\n", "      <td>2.274665</td>\n", "      <td>0.002347</td>\n", "      <td>1.028743</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2451</th>\n", "      <td>(Organic Blueberries)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.036058</td>\n", "      <td>0.021449</td>\n", "      <td>0.001449</td>\n", "      <td>0.040189</td>\n", "      <td>1.873696</td>\n", "      <td>0.000676</td>\n", "      <td>1.019525</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2469</th>\n", "      <td>(Organic Broccoli)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.012477</td>\n", "      <td>0.021449</td>\n", "      <td>0.001183</td>\n", "      <td>0.094791</td>\n", "      <td>4.419332</td>\n", "      <td>0.000915</td>\n", "      <td>1.081022</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2481</th>\n", "      <td>(Organic Carrot Bunch)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.011018</td>\n", "      <td>0.021449</td>\n", "      <td>0.001055</td>\n", "      <td>0.095745</td>\n", "      <td>4.463804</td>\n", "      <td>0.000819</td>\n", "      <td>1.082162</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2487</th>\n", "      <td>(Organic Cilantro)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.024592</td>\n", "      <td>0.021449</td>\n", "      <td>0.001215</td>\n", "      <td>0.049393</td>\n", "      <td>2.302818</td>\n", "      <td>0.000687</td>\n", "      <td>1.029396</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2504</th>\n", "      <td>(Organic Cucumber)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.032040</td>\n", "      <td>0.021449</td>\n", "      <td>0.002760</td>\n", "      <td>0.086132</td>\n", "      <td>4.015659</td>\n", "      <td>0.002072</td>\n", "      <td>1.070780</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2547</th>\n", "      <td>(Organic D'Anjou Pears)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.013937</td>\n", "      <td>0.021449</td>\n", "      <td>0.001300</td>\n", "      <td>0.093272</td>\n", "      <td>4.348531</td>\n", "      <td>0.001001</td>\n", "      <td>1.079211</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2557</th>\n", "      <td>(Organic Gala Apples)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.022376</td>\n", "      <td>0.021449</td>\n", "      <td>0.001502</td>\n", "      <td>0.067143</td>\n", "      <td>3.130331</td>\n", "      <td>0.001022</td>\n", "      <td>1.048983</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2570</th>\n", "      <td>(Organic Garlic)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.032403</td>\n", "      <td>0.021449</td>\n", "      <td>0.001822</td>\n", "      <td>0.056232</td>\n", "      <td>2.621623</td>\n", "      <td>0.001127</td>\n", "      <td>1.036855</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2600</th>\n", "      <td>(Organic Garnet Sweet Potato (Yam))</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.017890</td>\n", "      <td>0.021449</td>\n", "      <td>0.001257</td>\n", "      <td>0.070280</td>\n", "      <td>3.276588</td>\n", "      <td>0.000874</td>\n", "      <td>1.052522</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2619</th>\n", "      <td>(Organic Granny <PERSON>)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.018380</td>\n", "      <td>0.021449</td>\n", "      <td>0.001151</td>\n", "      <td>0.062609</td>\n", "      <td>2.918940</td>\n", "      <td>0.000757</td>\n", "      <td>1.043909</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2637</th>\n", "      <td>(Organic Grape Tomatoes)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.029057</td>\n", "      <td>0.021449</td>\n", "      <td>0.001534</td>\n", "      <td>0.052805</td>\n", "      <td>2.461886</td>\n", "      <td>0.000911</td>\n", "      <td>1.033104</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2655</th>\n", "      <td>(Organic Kiwi)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.014822</td>\n", "      <td>0.021449</td>\n", "      <td>0.001566</td>\n", "      <td>0.105679</td>\n", "      <td>4.926979</td>\n", "      <td>0.001248</td>\n", "      <td>1.094183</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2661</th>\n", "      <td>(Organic Large Extra Fancy Fuji Apple)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.024379</td>\n", "      <td>0.021449</td>\n", "      <td>0.002142</td>\n", "      <td>0.087850</td>\n", "      <td>4.095723</td>\n", "      <td>0.001619</td>\n", "      <td>1.072796</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2667</th>\n", "      <td>(Organic Lemon)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.026745</td>\n", "      <td>0.021449</td>\n", "      <td>0.002930</td>\n", "      <td>0.109562</td>\n", "      <td>5.107983</td>\n", "      <td>0.002357</td>\n", "      <td>1.098954</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2673</th>\n", "      <td>(Organic Navel Orange)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.013724</td>\n", "      <td>0.021449</td>\n", "      <td>0.001460</td>\n", "      <td>0.106366</td>\n", "      <td>4.959013</td>\n", "      <td>0.001165</td>\n", "      <td>1.095025</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2679</th>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.045594</td>\n", "      <td>0.021449</td>\n", "      <td>0.004454</td>\n", "      <td>0.097686</td>\n", "      <td>4.554330</td>\n", "      <td>0.003476</td>\n", "      <td>1.084491</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2685</th>\n", "      <td>(Organic Red Bell Pepper)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.017038</td>\n", "      <td>0.021449</td>\n", "      <td>0.001406</td>\n", "      <td>0.082552</td>\n", "      <td>3.848717</td>\n", "      <td>0.001041</td>\n", "      <td>1.066600</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2691</th>\n", "      <td>(Organic Red Onion)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.025871</td>\n", "      <td>0.021449</td>\n", "      <td>0.001396</td>\n", "      <td>0.053954</td>\n", "      <td>2.515435</td>\n", "      <td>0.000841</td>\n", "      <td>1.034359</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2697</th>\n", "      <td>(Organic Small Bunch Celery)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.017581</td>\n", "      <td>0.021449</td>\n", "      <td>0.001129</td>\n", "      <td>0.064242</td>\n", "      <td>2.995108</td>\n", "      <td>0.000752</td>\n", "      <td>1.045731</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2703</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.091668</td>\n", "      <td>0.021449</td>\n", "      <td>0.006063</td>\n", "      <td>0.066140</td>\n", "      <td>3.083563</td>\n", "      <td>0.004097</td>\n", "      <td>1.047856</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2709</th>\n", "      <td>(Organic Tomato Cluster)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.018348</td>\n", "      <td>0.021449</td>\n", "      <td>0.001684</td>\n", "      <td>0.091754</td>\n", "      <td>4.277741</td>\n", "      <td>0.001290</td>\n", "      <td>1.077407</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2715</th>\n", "      <td>(Organic Unsweetened Almond Milk)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.017933</td>\n", "      <td>0.021449</td>\n", "      <td>0.001321</td>\n", "      <td>0.073678</td>\n", "      <td>3.435011</td>\n", "      <td>0.000937</td>\n", "      <td>1.056383</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2720</th>\n", "      <td>(Organic Whole Milk)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.044475</td>\n", "      <td>0.021449</td>\n", "      <td>0.001524</td>\n", "      <td>0.034260</td>\n", "      <td>1.597254</td>\n", "      <td>0.000570</td>\n", "      <td>1.013265</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2727</th>\n", "      <td>(Organic Whole String Cheese)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.016676</td>\n", "      <td>0.021449</td>\n", "      <td>0.001279</td>\n", "      <td>0.076677</td>\n", "      <td>3.574847</td>\n", "      <td>0.000921</td>\n", "      <td>1.059815</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2733</th>\n", "      <td>(Organic Yellow Onion)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.032968</td>\n", "      <td>0.021449</td>\n", "      <td>0.002440</td>\n", "      <td>0.074014</td>\n", "      <td>3.450688</td>\n", "      <td>0.001733</td>\n", "      <td>1.056767</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2739</th>\n", "      <td>(Organic Zucchini)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.034800</td>\n", "      <td>0.021449</td>\n", "      <td>0.002046</td>\n", "      <td>0.058788</td>\n", "      <td>2.740789</td>\n", "      <td>0.001299</td>\n", "      <td>1.039671</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2745</th>\n", "      <td>(Original Hummus)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.023655</td>\n", "      <td>0.021449</td>\n", "      <td>0.001066</td>\n", "      <td>0.045045</td>\n", "      <td>2.100088</td>\n", "      <td>0.000558</td>\n", "      <td>1.024709</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2751</th>\n", "      <td>(Seedless Red Grapes)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.030570</td>\n", "      <td>0.021449</td>\n", "      <td>0.001119</td>\n", "      <td>0.036598</td>\n", "      <td>1.706276</td>\n", "      <td>0.000463</td>\n", "      <td>1.015724</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3835</th>\n", "      <td>(Organic Strawberries, Organic Baby Spinach)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.012765</td>\n", "      <td>0.021449</td>\n", "      <td>0.001385</td>\n", "      <td>0.108514</td>\n", "      <td>5.059144</td>\n", "      <td>0.001111</td>\n", "      <td>1.097663</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3848</th>\n", "      <td>(Organic Cucumber, Organic Strawberries)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.006873</td>\n", "      <td>0.021449</td>\n", "      <td>0.001087</td>\n", "      <td>0.158140</td>\n", "      <td>7.372775</td>\n", "      <td>0.000939</td>\n", "      <td>1.162367</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3863</th>\n", "      <td>(Organic Raspberries, Organic Strawberries)</td>\n", "      <td>(Bag of Organic Bananas, Organic Hass Avocado)</td>\n", "      <td>0.013074</td>\n", "      <td>0.021449</td>\n", "      <td>0.001811</td>\n", "      <td>0.138549</td>\n", "      <td>6.459440</td>\n", "      <td>0.001531</td>\n", "      <td>1.135934</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                       antecedents  \\\n", "2135                    (Apple Honeycrisp Organic)   \n", "2155                                   (<PERSON><PERSON><PERSON><PERSON>)   \n", "2197                           (<PERSON>)   \n", "2234                                 (<PERSON> Lemon)   \n", "2259                                       (<PERSON><PERSON>)   \n", "2277                       (Michigan Organic Kale)   \n", "2301                        (Organic Baby Arugula)   \n", "2313                        (Organic Baby Carrots)   \n", "2373                        (<PERSON> Baby <PERSON>)   \n", "2451                         (Organic Blueberries)   \n", "2469                            (Organic Broccoli)   \n", "2481                        (Organic Carrot Bunch)   \n", "2487                            (Organic Cilantro)   \n", "2504                            (Organic Cucumber)   \n", "2547                       (Organic D'Anjou Pears)   \n", "2557                         (Organic Gala Apples)   \n", "2570                              (Organic Garlic)   \n", "2600           (Organic Garnet Sweet Potato (Yam))   \n", "2619                  (<PERSON> Granny <PERSON>)   \n", "2637                      (Organic Grape Tomatoes)   \n", "2655                                (Organic Kiwi)   \n", "2661        (Organic Large Extra Fancy Fuji Apple)   \n", "2667                               (Organic Lemon)   \n", "2673                        (Organic Navel Orange)   \n", "2679                         (Organic Raspberries)   \n", "2685                     (Organic Red Bell Pepper)   \n", "2691                           (Organic Red Onion)   \n", "2697                  (Organic Small Bunch Celery)   \n", "2703                        (Organic Strawberries)   \n", "2709                      (Organic Tomato Cluster)   \n", "2715             (Organic Unsweetened Almond Milk)   \n", "2720                          (Organic Whole Milk)   \n", "2727                 (Organic Whole String Cheese)   \n", "2733                        (Organic Yellow Onion)   \n", "2739                            (<PERSON>)   \n", "2745                             (<PERSON> Hummus)   \n", "2751                         (<PERSON><PERSON><PERSON> Red Grapes)   \n", "3835  (Organic Strawberries, Organic Baby Spinach)   \n", "3848      (Organic Cucumber, Organic Strawberries)   \n", "3863   (Organic Raspberries, Organic Strawberries)   \n", "\n", "                                         consequents  antecedent support  \\\n", "2135  (Bag of Organic Bananas, Organic Hass Avocado)            0.017304   \n", "2155  (Bag of Organic Bananas, Organic Hass Avocado)            0.026180   \n", "2197  (Bag of Organic Bananas, Organic Hass Avocado)            0.019382   \n", "2234  (Bag of Organic Bananas, Organic Hass Avocado)            0.063111   \n", "2259  (Bag of Organic Bananas, Organic Hass Avocado)            0.045115   \n", "2277  (Bag of Organic Bananas, Organic Hass Avocado)            0.020714   \n", "2301  (Bag of Organic Bananas, Organic Hass Avocado)            0.022067   \n", "2313  (Bag of Organic Bananas, Organic Hass Avocado)            0.026414   \n", "2373  (Bag of Organic Bananas, Organic Hass Avocado)            0.085828   \n", "2451  (Bag of Organic Bananas, Organic Hass Avocado)            0.036058   \n", "2469  (Bag of Organic Bananas, Organic Hass Avocado)            0.012477   \n", "2481  (Bag of Organic Bananas, Organic Hass Avocado)            0.011018   \n", "2487  (Bag of Organic Bananas, Organic Hass Avocado)            0.024592   \n", "2504  (Bag of Organic Bananas, Organic Hass Avocado)            0.032040   \n", "2547  (Bag of Organic Bananas, Organic Hass Avocado)            0.013937   \n", "2557  (Bag of Organic Bananas, Organic Hass Avocado)            0.022376   \n", "2570  (Bag of Organic Bananas, Organic Hass Avocado)            0.032403   \n", "2600  (Bag of Organic Bananas, Organic Hass Avocado)            0.017890   \n", "2619  (Bag of Organic Bananas, Organic Hass Avocado)            0.018380   \n", "2637  (Bag of Organic Bananas, Organic Hass Avocado)            0.029057   \n", "2655  (Bag of Organic Bananas, Organic Hass Avocado)            0.014822   \n", "2661  (Bag of Organic Bananas, Organic Hass Avocado)            0.024379   \n", "2667  (Bag of Organic Bananas, Organic Hass Avocado)            0.026745   \n", "2673  (Bag of Organic Bananas, Organic Hass Avocado)            0.013724   \n", "2679  (Bag of Organic Bananas, Organic Hass Avocado)            0.045594   \n", "2685  (Bag of Organic Bananas, Organic Hass Avocado)            0.017038   \n", "2691  (Bag of Organic Bananas, Organic Hass Avocado)            0.025871   \n", "2697  (Bag of Organic Bananas, Organic Hass Avocado)            0.017581   \n", "2703  (Bag of Organic Bananas, Organic Hass Avocado)            0.091668   \n", "2709  (Bag of Organic Bananas, Organic Hass Avocado)            0.018348   \n", "2715  (Bag of Organic Bananas, Organic Hass Avocado)            0.017933   \n", "2720  (Bag of Organic Bananas, Organic Hass Avocado)            0.044475   \n", "2727  (Bag of Organic Bananas, Organic Hass Avocado)            0.016676   \n", "2733  (Bag of Organic Bananas, Organic Hass Avocado)            0.032968   \n", "2739  (Bag of Organic Bananas, Organic Hass Avocado)            0.034800   \n", "2745  (Bag of Organic Bananas, Organic Hass Avocado)            0.023655   \n", "2751  (Bag of Organic Bananas, Organic Hass Avocado)            0.030570   \n", "3835  (Bag of Organic Bananas, Organic Hass Avocado)            0.012765   \n", "3848  (Bag of Organic Bananas, Organic Hass Avocado)            0.006873   \n", "3863  (Bag of Organic Bananas, Organic Hass Avocado)            0.013074   \n", "\n", "      consequent support   support  confidence      lift  leverage  \\\n", "2135            0.021449  0.001300    0.075123  3.502388  0.000929   \n", "2155            0.021449  0.001108    0.042328  1.973416  0.000547   \n", "2197            0.021449  0.001012    0.052226  2.434902  0.000597   \n", "2234            0.021449  0.001492    0.023637  1.101988  0.000138   \n", "2259            0.021449  0.001737    0.038498  1.794846  0.000769   \n", "2277            0.021449  0.001044    0.050412  2.350284  0.000600   \n", "2301            0.021449  0.001023    0.046354  2.161134  0.000550   \n", "2313            0.021449  0.001343    0.050827  2.369652  0.000776   \n", "2373            0.021449  0.004188    0.048790  2.274665  0.002347   \n", "2451            0.021449  0.001449    0.040189  1.873696  0.000676   \n", "2469            0.021449  0.001183    0.094791  4.419332  0.000915   \n", "2481            0.021449  0.001055    0.095745  4.463804  0.000819   \n", "2487            0.021449  0.001215    0.049393  2.302818  0.000687   \n", "2504            0.021449  0.002760    0.086132  4.015659  0.002072   \n", "2547            0.021449  0.001300    0.093272  4.348531  0.001001   \n", "2557            0.021449  0.001502    0.067143  3.130331  0.001022   \n", "2570            0.021449  0.001822    0.056232  2.621623  0.001127   \n", "2600            0.021449  0.001257    0.070280  3.276588  0.000874   \n", "2619            0.021449  0.001151    0.062609  2.918940  0.000757   \n", "2637            0.021449  0.001534    0.052805  2.461886  0.000911   \n", "2655            0.021449  0.001566    0.105679  4.926979  0.001248   \n", "2661            0.021449  0.002142    0.087850  4.095723  0.001619   \n", "2667            0.021449  0.002930    0.109562  5.107983  0.002357   \n", "2673            0.021449  0.001460    0.106366  4.959013  0.001165   \n", "2679            0.021449  0.004454    0.097686  4.554330  0.003476   \n", "2685            0.021449  0.001406    0.082552  3.848717  0.001041   \n", "2691            0.021449  0.001396    0.053954  2.515435  0.000841   \n", "2697            0.021449  0.001129    0.064242  2.995108  0.000752   \n", "2703            0.021449  0.006063    0.066140  3.083563  0.004097   \n", "2709            0.021449  0.001684    0.091754  4.277741  0.001290   \n", "2715            0.021449  0.001321    0.073678  3.435011  0.000937   \n", "2720            0.021449  0.001524    0.034260  1.597254  0.000570   \n", "2727            0.021449  0.001279    0.076677  3.574847  0.000921   \n", "2733            0.021449  0.002440    0.074014  3.450688  0.001733   \n", "2739            0.021449  0.002046    0.058788  2.740789  0.001299   \n", "2745            0.021449  0.001066    0.045045  2.100088  0.000558   \n", "2751            0.021449  0.001119    0.036598  1.706276  0.000463   \n", "3835            0.021449  0.001385    0.108514  5.059144  0.001111   \n", "3848            0.021449  0.001087    0.158140  7.372775  0.000939   \n", "3863            0.021449  0.001811    0.138549  6.459440  0.001531   \n", "\n", "      conviction  antecedent_len  \n", "2135    1.058034               1  \n", "2155    1.021802               1  \n", "2197    1.032473               1  \n", "2234    1.002241               1  \n", "2259    1.017731               1  \n", "2277    1.030500               1  \n", "2301    1.026116               1  \n", "2313    1.030951               1  \n", "2373    1.028743               1  \n", "2451    1.019525               1  \n", "2469    1.081022               1  \n", "2481    1.082162               1  \n", "2487    1.029396               1  \n", "2504    1.070780               1  \n", "2547    1.079211               1  \n", "2557    1.048983               1  \n", "2570    1.036855               1  \n", "2600    1.052522               1  \n", "2619    1.043909               1  \n", "2637    1.033104               1  \n", "2655    1.094183               1  \n", "2661    1.072796               1  \n", "2667    1.098954               1  \n", "2673    1.095025               1  \n", "2679    1.084491               1  \n", "2685    1.066600               1  \n", "2691    1.034359               1  \n", "2697    1.045731               1  \n", "2703    1.047856               1  \n", "2709    1.077407               1  \n", "2715    1.056383               1  \n", "2720    1.013265               1  \n", "2727    1.059815               1  \n", "2733    1.056767               1  \n", "2739    1.039671               1  \n", "2745    1.024709               1  \n", "2751    1.015724               1  \n", "3835    1.097663               2  \n", "3848    1.162367               2  \n", "3863    1.135934               2  "]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["# select_rules_with_consequents_names({'Small Hass Avocado'})\n", "# select_rules_with_consequents_names({'Broccoli Crown'})\n", "# select_rules_with_consequents_names({'Bag of Organic Bananas','Organic Hass Avocado'})\n", "select_rules_with_consequents_names({'Organic Hass Avocado'})"]}], "metadata": {"interpreter": {"hash": "26de051ba29f2982a8de78e945f0abaf191376122a1563185a90213a26c5da77"}, "kernelspec": {"display_name": "Python 3.10.0 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}