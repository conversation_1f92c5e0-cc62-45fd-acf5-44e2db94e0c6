const mongoose = require('mongoose');
const XGBoostForecastingService = require('../services/xgboostForecastingService');

async function testForecastData() {
  try {
    console.log('🔍 Testing forecast data preparation...');
    
    // Connect to database
    require('dotenv').config();
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/inventory_management';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
    
    // Initialize forecasting service
    const forecastingService = new XGBoostForecastingService();
    
    // Test data preparation
    console.log('📊 Preparing sales data...');
    const featureData = await forecastingService.prepareXGBoostFeatures();
    
    console.log(`✅ Prepared ${featureData.length} feature records for XGBoost`);
    
    if (salesData.length > 0) {
      console.log('📋 Sample data:');
      console.log(salesData.slice(0, 3));
      
      // Test CSV generation
      console.log('💾 Testing CSV generation...');
      const csvPath = await forecastingService.saveSalesDataToCsv(salesData.slice(0, 100));
      console.log(`✅ CSV saved to: ${csvPath}`);
    }
    
    await mongoose.disconnect();
    console.log('✅ Test completed successfully');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

testForecastData();
