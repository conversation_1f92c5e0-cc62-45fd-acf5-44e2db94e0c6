#!/usr/bin/env python3
"""
Combined EDA Notebook Execution Script
Running the Combined_EDA.ipynb step by step
"""

import numpy as np
import pandas as pd
import os
import zipfile
import glob
import seaborn as sns
import matplotlib as mpl
import matplotlib.pyplot as plt
import datetime
import random
import warnings
warnings.filterwarnings('ignore')

# Set pandas options
pd.set_option('display.max_columns', 150)
pd.set_option('display.max_rows', 100)

# Define Seaborn color palette and matplotlib settings
colors = sns.color_palette('crest', 8)
cmap_colors = sns.cubehelix_palette(start=.5, rot=-.5, as_cmap=True)
sns.set_style('darkgrid')

# Set figure parameters
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['figure.dpi'] = 120

# Data directory path
data_directory_path = 'instacartMarketBasket_Consolidated/01_Data/InstarcartMarketBasketAnalysisDataset/'

# Converting the days and hours from numbers to their interpretable form
days_of_week = {0: 'Saturday', 1: 'Sunday', 2: 'Monday', 3: 'Tuesday', 
                4: 'Wednesday', 5: 'Thursday', 6: 'Friday'}
hour_nums = list(range(24))
hours_of_day = {hour_num: datetime.time(hour_num).strftime('%I:00 %p') for hour_num in hour_nums}

print('✅ Step 1: Libraries imported successfully!')
print(f'✅ Pandas version: {pd.__version__}')
print(f'✅ NumPy version: {np.__version__}')

def reduce_mem_usage(train_data):
    """
    Iterate through all the columns of a dataframe and modify the data type
    to reduce memory usage.
    """
    start_mem = train_data.memory_usage().sum() / 1024**2
    print('Memory usage of dataframe is {:.2f} MB'.format(start_mem))

    for col in train_data.columns:
        col_type = train_data[col].dtype
        
        if col_type not in [object, 'category']:
            c_min = train_data[col].min()
            c_max = train_data[col].max()
            if str(col_type)[:3] == 'int':
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    train_data[col] = train_data[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    train_data[col] = train_data[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    train_data[col] = train_data[col].astype(np.int32)
                elif c_min > np.iinfo(np.int64).min and c_max < np.iinfo(np.int64).max:
                    train_data[col] = train_data[col].astype(np.int64)  
            else:
                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                    train_data[col] = train_data[col].astype(np.float16)
                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                    train_data[col] = train_data[col].astype(np.float32)
                else:
                    train_data[col] = train_data[col].astype(np.float64)
        else:
            train_data[col] = train_data[col].astype('category')
    end_mem = train_data.memory_usage().sum() / 1024**2
    print('Memory usage after optimization is: {:.2f} MB'.format(end_mem))
    print('Decreased by {:.1f}%'.format(100 * (start_mem - end_mem) / start_mem))

    return train_data

def annotate_text(ax, append_to_text='%'):
    """Annotate text on graph"""
    for p in ax.patches:
        txt = str(p.get_height().round(2)) + append_to_text
        txt_x = p.get_x() + p.get_width()/2.
        txt_y = 0.92*p.get_height()
        ax.text(txt_x, txt_y, txt, fontsize=12, color='#004235', ha='center', va='bottom')

print('✅ Step 2: Utility functions defined successfully!')

# Step 3: Data Loading and Preparation
print('\n' + '='*60)
print('STEP 3: DATA LOADING AND PREPARATION')
print('='*60)

print('Loading datasets...')

# Check if data directory exists
if not os.path.exists(data_directory_path):
    print(f'❌ Error: Data directory not found: {data_directory_path}')
    exit(1)

print(f'✅ Data directory found: {data_directory_path}')
print(f'✅ Available files: {os.listdir(data_directory_path)}')

# Load aisles
print('\n📊 Loading aisles.csv...')
aisles = pd.read_csv(data_directory_path + 'aisles.csv')
print(f'Original shape: {aisles.shape}')
aisles = reduce_mem_usage(aisles)

# Load departments
print('\n📊 Loading departments.csv...')
departments = pd.read_csv(data_directory_path + 'departments.csv')
print(f'Original shape: {departments.shape}')
departments = reduce_mem_usage(departments)

# Load order_products_prior with optimized dtypes
print('\n📊 Loading order_products__prior.csv...')
# Define optimal data types to reduce memory usage
prior_dtypes = {
    'order_id': 'int32',
    'product_id': 'int16',
    'add_to_cart_order': 'int8',
    'reordered': 'int8'
}
try:
    order_products_prior = pd.read_csv(data_directory_path + 'order_products__prior.csv',
                                       dtype=prior_dtypes)
    print(f'Original shape: {order_products_prior.shape}')
    print(f'Memory usage: {order_products_prior.memory_usage().sum() / 1024**2:.2f} MB')
except Exception as e:
    print(f'❌ Error loading order_products__prior.csv: {e}')
    print('Trying to load in chunks...')
    # Load in smaller chunks if memory error occurs
    chunk_list = []
    chunk_size = 100000
    for chunk in pd.read_csv(data_directory_path + 'order_products__prior.csv',
                            dtype=prior_dtypes, chunksize=chunk_size):
        chunk_list.append(chunk)
        print(f'Loaded chunk with {len(chunk)} rows')
        if len(chunk_list) >= 5:  # Limit to first 5 chunks for demo
            break
    order_products_prior = pd.concat(chunk_list, ignore_index=True)
    print(f'Final shape: {order_products_prior.shape}')
    print(f'Memory usage: {order_products_prior.memory_usage().sum() / 1024**2:.2f} MB')

# Load order_products_train with optimized dtypes
print('\n📊 Loading order_products__train.csv...')
train_dtypes = {
    'order_id': 'int32',
    'product_id': 'int16',
    'add_to_cart_order': 'int8',
    'reordered': 'int8'
}
order_products_train = pd.read_csv(data_directory_path + 'order_products__train.csv',
                                   dtype=train_dtypes)
print(f'Original shape: {order_products_train.shape}')
print(f'Memory usage: {order_products_train.memory_usage().sum() / 1024**2:.2f} MB')

# Load orders with optimized dtypes
print('\n📊 Loading orders.csv...')
orders_dtypes = {
    'order_id': 'int32',
    'user_id': 'int32',
    'eval_set': 'category',
    'order_number': 'int16',
    'order_dow': 'int8',
    'order_hour_of_day': 'int8',
    'days_since_prior_order': 'float32'
}
orders = pd.read_csv(data_directory_path + 'orders.csv', dtype=orders_dtypes)
print(f'Original shape: {orders.shape}')
print(f'Memory usage: {orders.memory_usage().sum() / 1024**2:.2f} MB')

# Process orders data
print('Processing orders data...')
# Replacing numbers with their corresponding hour representation
orders['order_hour_of_day'] = orders['order_hour_of_day'].replace(to_replace=hours_of_day, value=None)
orders['order_hour_of_day'] = pd.Categorical(orders['order_hour_of_day'], 
                                             ordered=True, 
                                             categories=list(hours_of_day.values()))
# Replacing numbers with their corresponding day of week
orders['order_dow'] = orders['order_dow'].replace(to_replace=days_of_week, value=None)
orders['order_dow'] = pd.Categorical(orders['order_dow'], 
                                     ordered=True, 
                                     categories=list(days_of_week.values()))
orders = reduce_mem_usage(orders)

# Load products
print('\n📊 Loading products.csv...')
products = pd.read_csv(data_directory_path + 'products.csv')
print(f'Original shape: {products.shape}')

# Add organic flag
print('Adding organic flag to products...')
organic = products['product_name'].str.contains('Organic', na=False)
products['is_organic'] = organic
products = reduce_mem_usage(products)

print('\n✅ All datasets loaded successfully!')

# Step 4: Data Overview
print('\n' + '='*60)
print('STEP 4: DATA OVERVIEW AND UNDERSTANDING')
print('='*60)

print('\n📋 DATASET INFORMATION:')
print('-' * 40)

datasets = {
    'aisles': aisles,
    'departments': departments,
    'orders': orders,
    'order_products_prior': order_products_prior,
    'order_products_train': order_products_train,
    'products': products
}

for name, df in datasets.items():
    print(f'\n{name.upper()}:')
    print(f'  Shape: {df.shape}')
    print(f'  Columns: {list(df.columns)}')
    print(f'  Memory usage: {df.memory_usage().sum() / 1024**2:.2f} MB')

print('\n📊 SAMPLE DATA:')
print('-' * 40)

print('\nAISLES (first 5 rows):')
print(aisles.head())

print('\nDEPARTMENTS (first 5 rows):')
print(departments.head())

print('\nPRODUCTS (first 5 rows):')
print(products.head())

print('\nORDERS (first 5 rows):')
print(orders.head())

print('\nORDER_PRODUCTS_PRIOR (first 5 rows):')
print(order_products_prior.head())

print('\nORDER_PRODUCTS_TRAIN (first 5 rows):')
print(order_products_train.head())

print('\n✅ Step 4 completed: Data overview finished!')

if __name__ == "__main__":
    print('\n' + '='*60)
    print('COMBINED EDA EXECUTION COMPLETED')
    print('='*60)
    print('✅ All steps executed successfully!')
    print('📊 Data is ready for analysis!')
