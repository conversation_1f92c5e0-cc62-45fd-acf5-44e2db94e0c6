const ChatbotService = require('./services/chatbotService');

const chatbot = new ChatbotService();

console.log('Testing preprocessQuery:');
console.log('Original: "Check inventory"');
console.log('Processed:', JSON.stringify(chatbot.preprocessQuery('Check inventory')));
console.log('Original: "inventory"');
console.log('Processed:', JSON.stringify(chatbot.preprocessQuery('inventory')));

// Test intent detection
console.log('\nTesting intent detection:');
console.log('Intent for "Check inventory":', chatbot.detectIntent('Check inventory'));
console.log('Intent for "inventory":', chatbot.detectIntent('inventory'));
