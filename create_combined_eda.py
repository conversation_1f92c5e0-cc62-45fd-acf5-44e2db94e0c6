#!/usr/bin/env python3
"""
Script to create a combined EDA notebook with the best features from both notebooks.
"""

import json
import os

def create_combined_eda():
    """Create a combined EDA notebook."""
    
    # Load both notebooks
    with open('instacartMarketBasket_Consolidated/02_EDA/Exploratory Data Analysis.ipynb', 'r', encoding='utf-8') as f:
        notebook1 = json.load(f)
    
    with open('instacartMarketBasket_Consolidated/02_EDA/eda-on-instacart-data.ipynb', 'r', encoding='utf-8') as f:
        notebook2 = json.load(f)
    
    # Create new combined notebook structure
    combined_notebook = {
        "cells": [],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "name": "python",
                "version": "3.8.0",
                "mimetype": "text/x-python",
                "codemirror_mode": {"name": "ipython", "version": 3},
                "pygments_lexer": "ipython3",
                "nbconvert_exporter": "python",
                "file_extension": ".py"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    # Add title cell
    title_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "# Comprehensive Exploratory Data Analysis\n",
            "## Instacart Market Basket Analysis\n",
            "\n",
            "This notebook combines the best features from multiple EDA approaches to provide comprehensive insights into the Instacart dataset.\n",
            "\n",
            "### Key Features:\n",
            "- Memory optimization techniques\n",
            "- Comprehensive data visualization\n",
            "- Business insights and recommendations\n",
            "- Statistical analysis of customer behavior\n",
            "\n",
            "> **Business Questions and Solutions**: This analysis provides actionable insights for inventory management and customer behavior understanding."
        ]
    }
    combined_notebook["cells"].append(title_cell)
    
    # Add imports section (taking the more comprehensive one from notebook2)
    imports_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## Importing Required Libraries"]
    }
    combined_notebook["cells"].append(imports_cell)
    
    # Enhanced imports from notebook2 with additions
    imports_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "import numpy as np\n",
            "import pandas as pd\n",
            "import os\n",
            "import zipfile\n",
            "import glob\n",
            "import seaborn as sns\n",
            "import matplotlib as mpl\n",
            "import matplotlib.pyplot as plt\n",
            "%matplotlib inline\n",
            "import datetime\n",
            "from IPython.display import display\n",
            "import random\n",
            "import warnings\n",
            "warnings.filterwarnings('ignore')\n",
            "\n",
            "# Set pandas options\n",
            "pd.set_option('display.max_columns', 150)\n",
            "pd.set_option('display.max_rows', 100)\n",
            "\n",
            "# Define Seaborn color palette and matplotlib settings\n",
            "colors = sns.color_palette(\"crest\", 8)\n",
            "cmap_colors = sns.cubehelix_palette(start=.5, rot=-.5, as_cmap=True)\n",
            "sns.set_style('darkgrid')\n",
            "\n",
            "# Set figure parameters\n",
            "plt.rcParams[\"figure.figsize\"] = (12, 8)\n",
            "plt.rcParams['figure.dpi'] = 120\n",
            "\n",
            "# Data directory path - update this to your local path\n",
            "data_directory_path = '../01_Data/InstarcartMarketBasketAnalysisDataset/'\n",
            "\n",
            "# Converting the days and hours from numbers to their interpretable form\n",
            "days_of_week = {0: 'Saturday', 1: 'Sunday', 2: 'Monday', 3: 'Tuesday', \n",
            "                4: 'Wednesday', 5: 'Thursday', 6: 'Friday'}\n",
            "hour_nums = list(range(24))\n",
            "hours_of_day = {hour_num: datetime.time(hour_num).strftime(\"%I:00 %p\") for hour_num in hour_nums}"
        ]
    }
    combined_notebook["cells"].append(imports_code)
    
    # Add utility functions from notebook2
    utils_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## Utility Functions"]
    }
    combined_notebook["cells"].append(utils_cell)

    # Memory optimization function from notebook2
    memory_opt_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def reduce_mem_usage(train_data):\n",
            "    \"\"\"\n",
            "    Iterate through all the columns of a dataframe and modify the data type\n",
            "    to reduce memory usage.\n",
            "    \"\"\"\n",
            "    start_mem = train_data.memory_usage().sum() / 1024**2\n",
            "    print('Memory usage of dataframe is {:.2f} MB'.format(start_mem))\n",
            "\n",
            "    for col in train_data.columns:\n",
            "        col_type = train_data[col].dtype\n",
            "        \n",
            "        if col_type not in [object, 'category']:\n",
            "            c_min = train_data[col].min()\n",
            "            c_max = train_data[col].max()\n",
            "            if str(col_type)[:3] == 'int':\n",
            "                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:\n",
            "                    train_data[col] = train_data[col].astype(np.int8)\n",
            "                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:\n",
            "                    train_data[col] = train_data[col].astype(np.int16)\n",
            "                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:\n",
            "                    train_data[col] = train_data[col].astype(np.int32)\n",
            "                elif c_min > np.iinfo(np.int64).min and c_max < np.iinfo(np.int64).max:\n",
            "                    train_data[col] = train_data[col].astype(np.int64)  \n",
            "            else:\n",
            "                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:\n",
            "                    train_data[col] = train_data[col].astype(np.float16)\n",
            "                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:\n",
            "                    train_data[col] = train_data[col].astype(np.float32)\n",
            "                else:\n",
            "                    train_data[col] = train_data[col].astype(np.float64)\n",
            "        else:\n",
            "            train_data[col] = train_data[col].astype('category')\n",
            "    end_mem = train_data.memory_usage().sum() / 1024**2\n",
            "    print('Memory usage after optimization is: {:.2f} MB'.format(end_mem))\n",
            "    print('Decreased by {:.1f}%'.format(100 * (start_mem - end_mem) / start_mem))\n",
            "\n",
            "    return train_data\n",
            "\n",
            "def annotate_text(ax, append_to_text='%'):\n",
            "    \"\"\"Annotate text on graph\"\"\"\n",
            "    for p in ax.patches:\n",
            "        txt = str(p.get_height().round(2)) + append_to_text\n",
            "        txt_x = p.get_x() + p.get_width()/2.\n",
            "        txt_y = 0.92*p.get_height()\n",
            "        ax.text(txt_x, txt_y, txt, fontsize=12, color='#004235', ha='center', va='bottom')"
        ]
    }
    combined_notebook["cells"].append(memory_opt_code)

    # Data loading section
    data_loading_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Data Loading and Preparation\n",
            "\n",
            "Loading the Instacart dataset with memory optimization techniques."
        ]
    }
    combined_notebook["cells"].append(data_loading_cell)

    # Data loading code
    data_loading_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Reading the csv files into corresponding dataframes\n",
            "# Then reduce their size to consume less memory\n",
            "print('Loading datasets...')\n",
            "\n",
            "aisles = pd.read_csv(data_directory_path + 'aisles.csv')\n",
            "aisles = reduce_mem_usage(aisles)\n",
            "\n",
            "departments = pd.read_csv(data_directory_path + 'departments.csv')\n",
            "departments = reduce_mem_usage(departments)\n",
            "\n",
            "order_products_prior = pd.read_csv(data_directory_path + 'order_products__prior.csv')\n",
            "order_products_prior = reduce_mem_usage(order_products_prior)\n",
            "\n",
            "order_products_train = pd.read_csv(data_directory_path + 'order_products__train.csv')\n",
            "order_products_train = reduce_mem_usage(order_products_train)\n",
            "\n",
            "orders = pd.read_csv(data_directory_path + 'orders.csv')\n",
            "# Replacing numbers with their corresponding hour representation\n",
            "orders['order_hour_of_day'] = orders['order_hour_of_day'].replace(to_replace=hours_of_day, value=None)\n",
            "orders['order_hour_of_day'] = pd.Categorical(orders['order_hour_of_day'], \n",
            "                                             ordered=True, \n",
            "                                             categories=list(hours_of_day.values()))\n",
            "# Replacing numbers with their corresponding day of week\n",
            "orders['order_dow'] = orders['order_dow'].replace(to_replace=days_of_week, value=None)\n",
            "orders['order_dow'] = pd.Categorical(orders['order_dow'], \n",
            "                                     ordered=True, \n",
            "                                     categories=list(days_of_week.values()))\n",
            "orders = reduce_mem_usage(orders)\n",
            "\n",
            "products = pd.read_csv(data_directory_path + 'products.csv')\n",
            "# Add organic flag\n",
            "organic = products['product_name'].str.contains('Organic')\n",
            "products['is_organic'] = organic\n",
            "products = reduce_mem_usage(products)\n",
            "\n",
            "print('\\nDatasets loaded successfully!')"
        ]
    }
    combined_notebook["cells"].append(data_loading_code)

    # Save the updated structure
    with open('instacartMarketBasket_Consolidated/02_EDA/Combined_EDA.ipynb', 'w', encoding='utf-8') as f:
        json.dump(combined_notebook, f, indent=2)

    print("Created comprehensive combined EDA notebook")
    print("File saved as: instacartMarketBasket_Consolidated/02_EDA/Combined_EDA.ipynb")

if __name__ == "__main__":
    create_combined_eda()
