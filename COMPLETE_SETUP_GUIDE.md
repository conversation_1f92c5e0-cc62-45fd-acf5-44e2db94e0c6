# 🚀 Complete Setup Guide - Inventory Management System

## 📋 Overview
This comprehensive guide will help you set up and run the complete Inventory Management System with all features including:
- **Demand Forecasting** (AI-powered with Prophet)
- **Amazon Fine Food Reviews Analysis**
- **Instacart Market Basket Analysis** 
- **AI Chatbot** (powered by Gemini AI)
- **Real-time Dashboard**

---

## 🛠️ Prerequisites

### Required Software
1. **Node.js** (v16 or higher) - [Download](https://nodejs.org)
2. **Python** (v3.8 or higher) - [Download](https://python.org)
3. **Git** - [Download](https://git-scm.com)

### Database
- **MongoDB Atlas** (already configured in the project)
- Connection string: `mongodb+srv://dangquybui88:<EMAIL>/inventory_management`

---

## 🚀 Quick Start (Automated)

### Option 1: Complete Automated Setup
```bash
# Run the complete setup script
start-system.bat
```
This script will:
- Install all dependencies
- Set up Python environment
- Start both servers
- Run diagnostics
- Set up sample data

### Option 2: Step-by-Step Setup

#### Step 1: Install Dependencies
```bash
# Frontend dependencies
npm install

# Backend dependencies
cd backend
npm install
cd ..
```

#### Step 2: Set up Python Environment
```bash
# Run Python setup
setup-python-env.bat
```

#### Step 3: Initialize Database
```bash
cd backend
npm run create-admin
npm run seed
cd ..
```

#### Step 4: Start the System
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend  
npm start
```

---

## 🔧 Manual Setup Instructions

### 1. Python Environment Setup
```bash
# Install core dependencies
pip install numpy pandas matplotlib seaborn
pip install prophet scikit-learn plotly
pip install python-dateutil pytz requests scipy statsmodels
```

### 2. Database Setup
The system uses MongoDB Atlas with the following collections:
- `products` - Inventory items
- `orders` - Sales orders
- `suppliers` - Supplier information
- `instacartproducts` - Instacart dataset
- `amazonreviews` - Amazon Fine Food Reviews
- `demandforecasts` - AI predictions

### 3. Environment Configuration
Backend `.env` file is already configured with:
- MongoDB Atlas connection
- Gemini AI API key
- JWT secrets
- CORS settings

---

## 🌐 Accessing the System

### Login Credentials
- **Email:** `<EMAIL>`
- **Password:** `admin123`

### URLs
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:5000
- **Health Check:** http://localhost:5000/api/health

---

## 📊 Features Overview

### 1. Dashboard
- Real-time inventory statistics
- Low stock alerts
- Sales trends
- Predictive insights

### 2. Inventory Management
- Product catalog
- Stock levels
- Supplier management
- Order tracking

### 3. Demand Forecasting 🔮
- **Location:** Navigation → "Demand Forecasting"
- AI-powered predictions using Facebook Prophet
- 30-day demand forecasts
- Low stock alerts with urgency levels
- Seasonal analysis
- Supplier recommendations

### 4. Amazon Fine Food Reviews ⭐
- **Location:** Navigation → "Amazon Reviews Analysis"
- Sentiment analysis
- Product insights
- Review analytics
- Rating distributions

### 5. Instacart Market Basket Analysis 🛒
- **Location:** Navigation → "Market Basket Analysis"
- Customer behavior analysis
- Product associations
- Purchase patterns

### 6. AI Chatbot 🤖
- **Location:** Navigation → "AI Chatbot"
- Powered by Google Gemini AI
- Natural language queries
- Database integration
- Inventory insights

---

## 🔍 Troubleshooting

### Demand Forecast Not Working?
```bash
# Run diagnostics
node test-demand-forecast.js

# Common fixes:
1. Ensure Python is installed: python --version
2. Install Prophet: pip install prophet
3. Check backend logs for errors
4. Verify database has products: npm run seed
```

### Amazon Reviews Not Showing Data?
```bash
# Set up Amazon reviews
node setup-amazon-reviews.js

# Manual import:
1. Go to Amazon Reviews Analysis page
2. Click "Import Reviews"
3. Enter: sample_reviews.csv
4. Wait for processing to complete
```

### Chatbot Not Responding?
1. Check Gemini API key in backend/.env
2. Verify internet connection
3. Check browser console for errors

### General Issues
```bash
# Test all APIs
node test-all-apis.js

# Reset database
cd backend
npm run seed

# Check server logs
cd backend
npm run dev
```

---

## 📱 Using the Features

### Demand Forecasting
1. Navigate to "Demand Forecasting"
2. Click "Generate Comprehensive Analysis"
3. Wait for AI processing (30-60 seconds)
4. Review:
   - Low stock alerts
   - High demand products
   - Seasonal recommendations
   - Supplier analysis

### Amazon Reviews Analysis
1. Navigate to "Amazon Reviews Analysis"
2. View dashboard with:
   - Total reviews and ratings
   - Sentiment distribution
   - Product insights
   - Recent reviews

### AI Chatbot
1. Navigate to "AI Chatbot"
2. Ask questions like:
   - "Show me low stock items"
   - "What are our best selling products?"
   - "Analyze customer sentiment"
   - "Generate demand forecast"

---

## 🧪 Testing Scripts

### Available Test Scripts
- `test-demand-forecast.js` - Tests forecasting functionality
- `test-all-apis.js` - Tests all API endpoints
- `setup-amazon-reviews.js` - Sets up Amazon reviews data

### Running Tests
```bash
# Test demand forecasting
node test-demand-forecast.js

# Test all APIs
node test-all-apis.js

# Setup Amazon reviews
node setup-amazon-reviews.js
```

---

## 📈 Performance Tips

1. **Database Optimization**
   - Indexes are already configured
   - Regular data cleanup recommended

2. **Python Performance**
   - Prophet models are cached
   - Forecasts generated in background

3. **Frontend Performance**
   - Data is paginated
   - Charts use efficient rendering

---

## 🆘 Support

### Common Error Messages
- **"Prophet not found"** → Run `setup-python-env.bat`
- **"MongoDB connection failed"** → Check internet connection
- **"Token expired"** → Re-login to the system
- **"No forecasts available"** → Click "Generate Comprehensive Analysis"

### Getting Help
1. Check browser console for errors
2. Check backend server logs
3. Run diagnostic scripts
4. Verify all dependencies are installed

---

## 🎉 Success Indicators

✅ **System is working correctly when:**
- Dashboard loads with real data
- Demand forecasting generates predictions
- Amazon reviews show analytics
- Chatbot responds to queries
- All navigation links work
- No console errors

**Enjoy your AI-powered inventory management system!** 🚀
