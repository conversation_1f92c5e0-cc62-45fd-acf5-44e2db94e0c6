{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.7.12", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "markdown", "source": "# Prediciting your Future order!\n> This notebook is implementing a predictive analysis model, that predicts the products ordered in users' future order based on each purchasing history.", "metadata": {}}, {"cell_type": "markdown", "source": "## Model Usage\n> **How this model will make better customer shopping expireince?**\n\n> Customers tend to do things quick and in an easy way. Thus can integrate this model with instacart's shopping software to initially autofill user's basket once openned the application.", "metadata": {}}, {"cell_type": "markdown", "source": "## 1. Reading Data", "metadata": {}}, {"cell_type": "code", "source": "import numpy as np # linear algebra\nimport pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)\n\n# garbage collector to free up memory\nimport gc\ngc.enable()", "metadata": {"_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "execution": {"iopub.status.busy": "2022-05-28T08:46:56.022106Z", "iopub.execute_input": "2022-05-28T08:46:56.022493Z", "iopub.status.idle": "2022-05-28T08:46:56.026892Z", "shell.execute_reply.started": "2022-05-28T08:46:56.022455Z", "shell.execute_reply": "2022-05-28T08:46:56.025975Z"}, "trusted": true}, "execution_count": 26, "outputs": []}, {"cell_type": "code", "source": "orders = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/orders.csv' )\norder_products_train = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/order_products__train.csv')\norder_products_prior = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/order_products__prior.csv')\nproducts = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/products.csv')\naisles = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/aisles.csv')\ndepartments = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/departments.csv')", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:46:56.031281Z", "iopub.execute_input": "2022-05-28T08:46:56.031823Z", "iopub.status.idle": "2022-05-28T08:47:07.727013Z", "shell.execute_reply.started": "2022-05-28T08:46:56.031785Z", "shell.execute_reply": "2022-05-28T08:47:07.726200Z"}, "trusted": true}, "execution_count": 27, "outputs": []}, {"cell_type": "code", "source": "def reduce_mem_usage(train_data):\n    \n#  iterate through all the columns of a dataframe and modify the data type to reduce memory usage.\"\"\"\n    start_mem = train_data.memory_usage().sum() / 1024**2\n    print('Memory usage of dataframe is {:.2f} MB'.format(start_mem))\n\n    for col in train_data.columns:\n        col_type = train_data[col].dtype\n\n        if col_type != object:\n            c_min = train_data[col].min()\n            c_max = train_data[col].max()\n            if str(col_type)[:3] == 'int':\n                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:\n                    train_data[col] = train_data[col].astype(np.int8)\n                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:\n                    train_data[col] = train_data[col].astype(np.int16)\n                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:\n                    train_data[col] = train_data[col].astype(np.int32)\n                elif c_min > np.iinfo(np.int64).min and c_max < np.iinfo(np.int64).max:\n                    train_data[col] = train_data[col].astype(np.int64)  \n            else:\n                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:\n                    train_data[col] = train_data[col].astype(np.float16)\n                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:\n                    train_data[col] = train_data[col].astype(np.float32)\n                else:\n                    train_data[col] = train_data[col].astype(np.float64)\n        else:\n            train_data[col] = train_data[col].astype('category')\n        end_mem = train_data.memory_usage().sum() / 1024**2\n        print('Memory usage after optimization is: {:.2f} MB'.format(end_mem))\n        print('Decreased by {:.1f}%'.format(100 * (start_mem - end_mem) / start_mem))\n\n    return train_data", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:07.730636Z", "iopub.execute_input": "2022-05-28T08:47:07.730861Z", "iopub.status.idle": "2022-05-28T08:47:07.748154Z", "shell.execute_reply.started": "2022-05-28T08:47:07.730834Z", "shell.execute_reply": "2022-05-28T08:47:07.746591Z"}, "trusted": true}, "execution_count": 28, "outputs": []}, {"cell_type": "code", "source": "reduce_mem_usage(order_products_prior)\nreduce_mem_usage(order_products_train)\nreduce_mem_usage(products)\nreduce_mem_usage(orders)\nreduce_mem_usage(departments)\nreduce_mem_usage(aisles)", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:07.749364Z", "iopub.execute_input": "2022-05-28T08:47:07.749974Z", "iopub.status.idle": "2022-05-28T08:47:09.555646Z", "shell.execute_reply.started": "2022-05-28T08:47:07.749932Z", "shell.execute_reply": "2022-05-28T08:47:09.554828Z"}, "trusted": true}, "execution_count": 29, "outputs": [{"name": "stdout", "text": "Memory usage of dataframe is 989.82 MB\nMemory usage after optimization is: 866.09 MB\nDecreased by 12.5%\nMemory usage after optimization is: 742.37 MB\nDecreased by 25.0%\nMemory usage after optimization is: 556.78 MB\nDecreased by 43.7%\nMemory usage after optimization is: 340.25 MB\nDecreased by 65.6%\nMemory usage of dataframe is 42.26 MB\nMemory usage after optimization is: 36.97 MB\nDecreased by 12.5%\nMemory usage after optimization is: 31.69 MB\nDecreased by 25.0%\nMemory usage after optimization is: 22.45 MB\nDecreased by 46.9%\nMemory usage after optimization is: 13.20 MB\nDecreased by 68.7%\nMemory usage of dataframe is 1.52 MB\nMemory usage after optimization is: 1.33 MB\nDecreased by 12.5%\nMemory usage after optimization is: 2.52 MB\nDecreased by -66.5%\nMemory usage after optimization is: 2.24 MB\nDecreased by -47.7%\nMemory usage after optimization is: 1.91 MB\nDecreased by -25.8%\nMemory usage of dataframe is 182.71 MB\nMemory usage after optimization is: 169.66 MB\nDecreased by 7.1%\nMemory usage after optimization is: 156.60 MB\nDecreased by 14.3%\nMemory usage after optimization is: 133.77 MB\nDecreased by 26.8%\nMemory usage after optimization is: 110.93 MB\nDecreased by 39.3%\nMemory usage after optimization is: 88.09 MB\nDecreased by 51.8%\nMemory usage after optimization is: 65.25 MB\nDecreased by 64.3%\nMemory usage after optimization is: 45.68 MB\nDecreased by 75.0%\nMemory usage of dataframe is 0.00 MB\nMemory usage after optimization is: 0.00 MB\nDecreased by 31.7%\nMemory usage after optimization is: 0.00 MB\nDecreased by -92.7%\nMemory usage of dataframe is 0.00 MB\nMemory usage after optimization is: 0.00 MB\nDecreased by 35.4%\nMemory usage after optimization is: 0.01 MB\nDecreased by -159.9%\n", "output_type": "stream"}, {"execution_count": 29, "output_type": "execute_result", "data": {"text/plain": "     aisle_id                       aisle\n0           1       prepared soups salads\n1           2           specialty cheeses\n2           3         energy granola bars\n3           4               instant foods\n4           5  marinades meat preparation\n..        ...                         ...\n129       130    hot cereal pancake mixes\n130       131                   dry pasta\n131       132                      beauty\n132       133  muscles joints pain relief\n133       134  specialty wines champagnes\n\n[134 rows x 2 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>aisle_id</th>\n      <th>aisle</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>prepared soups salads</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2</td>\n      <td>specialty cheeses</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>3</td>\n      <td>energy granola bars</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>4</td>\n      <td>instant foods</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>marinades meat preparation</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>129</th>\n      <td>130</td>\n      <td>hot cereal pancake mixes</td>\n    </tr>\n    <tr>\n      <th>130</th>\n      <td>131</td>\n      <td>dry pasta</td>\n    </tr>\n    <tr>\n      <th>131</th>\n      <td>132</td>\n      <td>beauty</td>\n    </tr>\n    <tr>\n      <th>132</th>\n      <td>133</td>\n      <td>muscles joints pain relief</td>\n    </tr>\n    <tr>\n      <th>133</th>\n      <td>134</td>\n      <td>specialty wines champagnes</td>\n    </tr>\n  </tbody>\n</table>\n<p>134 rows × 2 columns</p>\n</div>"}, "metadata": {}}]}, {"cell_type": "code", "source": "# get shape of each df\nprint(f\" aisles : {aisles.shape} \\n depts : {departments.shape} \\n order_prod_prior : {order_products_prior.shape} \\n order_products_train : {order_products_train.shape} \\n orders : {orders.shape} \\n products : {products.shape}\")", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:09.557885Z", "iopub.execute_input": "2022-05-28T08:47:09.558155Z", "iopub.status.idle": "2022-05-28T08:47:09.563607Z", "shell.execute_reply.started": "2022-05-28T08:47:09.558120Z", "shell.execute_reply": "2022-05-28T08:47:09.562626Z"}, "trusted": true}, "execution_count": 30, "outputs": [{"name": "stdout", "text": " aisles : (134, 2) \n depts : (21, 2) \n order_prod_prior : (32434489, 4) \n order_products_train : (1384617, 4) \n orders : (3421083, 7) \n products : (49688, 4)\n", "output_type": "stream"}]}, {"cell_type": "code", "source": "# Helper functions to be able to correclty calculate the avergae of hours\n# The problem is that if we deal with hours and average them normally, the average of 1:00 and 23:00 will be 12 not 0:00\nimport datetime\nimport math\n\ndef datetime_to_radians(x):\n    # radians are calculated using a 24-hour circle, not 12-hour, starting at north and moving clockwise\n    seconds_from_midnight = 3600 * x\n    radians = float(seconds_from_midnight) / float(12 * 60 * 60) * 2.0 * math.pi\n    return radians\n\ndef average_angle(angles):\n    # angles measured in radians\n    x_sum = np.sum(np.sin(angles))\n    y_sum = np.sum(np.cos(angles))\n    x_mean = x_sum / float(len(angles))\n    y_mean = y_sum / float(len(angles))\n    return np.arctan2(x_mean, y_mean)\n\ndef radians_to_time_of_day(x):\n    # radians are measured clockwise from north and represent time in a 24-hour circle\n    seconds_from_midnight = int(float(x) / (2.0 * math.pi) * 12.0 * 60.0 * 60.0)\n    hour = seconds_from_midnight // 3600 % 24\n    minute = (seconds_from_midnight % 3600) // 60\n    second = seconds_from_midnight % 60\n    return datetime.time(hour, minute, second)\n    \ndef average_times_of_day(x):\n    # input datetime.datetime array and output datetime.time value\n    angles = [datetime_to_radians(y) for y in x]\n    avg_angle = average_angle(angles)\n    return radians_to_time_of_day(avg_angle)\n\ndef day_to_radians(day):\n    radians = float(day) / float(7) * 2.0 * math.pi\n    return radians\ndef radians_to_days(x):\n    day = int(float(x) / (2.0 * math.pi) * 7) % 7\n    return day\ndef average_days(x):\n    angles = [day_to_radians(y) for y in x]\n    avg_angle = average_angle(angles)\n    return radians_to_days(avg_angle)", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:09.565092Z", "iopub.execute_input": "2022-05-28T08:47:09.565446Z", "iopub.status.idle": "2022-05-28T08:47:09.579673Z", "shell.execute_reply.started": "2022-05-28T08:47:09.565407Z", "shell.execute_reply": "2022-05-28T08:47:09.578780Z"}, "trusted": true}, "execution_count": 31, "outputs": []}, {"cell_type": "markdown", "source": "## 2. Predictor Features", "metadata": {}}, {"cell_type": "markdown", "source": "### 2.1 User predictors\n", "metadata": {}}, {"cell_type": "code", "source": "# We keep only the prior orders\nusers = orders[orders['eval_set'] == 'prior']\nusers['days_since_prior_order'].dropna()\n\n# We group orders by user_id & calculate the variables based on different user_id\nusers = users.groupby('user_id').agg(\n    \n user_orders= ('order_number' , max),\n user_period=('days_since_prior_order', sum),\n user_mean_days_since_prior = ('days_since_prior_order','mean')\n    \n)\nusers.head()", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:09.581522Z", "iopub.execute_input": "2022-05-28T08:47:09.582299Z", "iopub.status.idle": "2022-05-28T08:47:09.878276Z", "shell.execute_reply.started": "2022-05-28T08:47:09.582200Z", "shell.execute_reply": "2022-05-28T08:47:09.877472Z"}, "trusted": true}, "execution_count": 32, "outputs": [{"execution_count": 32, "output_type": "execute_result", "data": {"text/plain": "         user_orders  user_period  user_mean_days_since_prior\nuser_id                                                      \n1                 10        176.0                   19.562500\n2                 14        198.0                   15.234375\n3                 12        133.0                   12.093750\n4                  5         55.0                   13.750000\n5                  4         40.0                   13.335938", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>user_orders</th>\n      <th>user_period</th>\n      <th>user_mean_days_since_prior</th>\n    </tr>\n    <tr>\n      <th>user_id</th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>1</th>\n      <td>10</td>\n      <td>176.0</td>\n      <td>19.562500</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>14</td>\n      <td>198.0</td>\n      <td>15.234375</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>12</td>\n      <td>133.0</td>\n      <td>12.093750</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>55.0</td>\n      <td>13.750000</td>\n    </tr>\n    <tr>\n      <th>5</th>\n      <td>4</td>\n      <td>40.0</td>\n      <td>13.335938</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}]}, {"cell_type": "markdown", "source": "#### calculate three more new variables:\n\nuser_total_products: Total numbers of basket items included in user's orders\n\nuser_reorder_ratio: Ratio a user orders reorordered products.\n\nuser_distinct_products: Total number of distinct products ordered by a user", "metadata": {}}, {"cell_type": "code", "source": "# We create a new table \"orders_products\" which contains the tables \"orders\" and \"order_products_prior\"\norders_products =pd.merge(orders , order_products_prior, on='order_id', how='inner')\n\n# Getting the number of products in each basket(order)\ngroupedorders_products = orders_products.groupby(['order_id']).agg(\n    basket_size = ('product_id', 'count')\n).reset_index()\norders_products = orders_products.merge(groupedorders_products, on='order_id', how='left')\norders_products.head()", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:09.879796Z", "iopub.execute_input": "2022-05-28T08:47:09.880063Z", "iopub.status.idle": "2022-05-28T08:47:23.754539Z", "shell.execute_reply.started": "2022-05-28T08:47:09.880029Z", "shell.execute_reply": "2022-05-28T08:47:23.753795Z"}, "trusted": true}, "execution_count": 33, "outputs": [{"execution_count": 33, "output_type": "execute_result", "data": {"text/plain": "   order_id  user_id eval_set  order_number  order_dow  order_hour_of_day  \\\n0   2539329        1    prior             1          2                  8   \n1   2539329        1    prior             1          2                  8   \n2   2539329        1    prior             1          2                  8   \n3   2539329        1    prior             1          2                  8   \n4   2539329        1    prior             1          2                  8   \n\n   days_since_prior_order  product_id  add_to_cart_order  reordered  \\\n0                     NaN         196                  1          0   \n1                     NaN       14084                  2          0   \n2                     NaN       12427                  3          0   \n3                     NaN       26088                  4          0   \n4                     NaN       26405                  5          0   \n\n   basket_size  \n0            5  \n1            5  \n2            5  \n3            5  \n4            5  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>order_id</th>\n      <th>user_id</th>\n      <th>eval_set</th>\n      <th>order_number</th>\n      <th>order_dow</th>\n      <th>order_hour_of_day</th>\n      <th>days_since_prior_order</th>\n      <th>product_id</th>\n      <th>add_to_cart_order</th>\n      <th>reordered</th>\n      <th>basket_size</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>2539329</td>\n      <td>1</td>\n      <td>prior</td>\n      <td>1</td>\n      <td>2</td>\n      <td>8</td>\n      <td>NaN</td>\n      <td>196</td>\n      <td>1</td>\n      <td>0</td>\n      <td>5</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2539329</td>\n      <td>1</td>\n      <td>prior</td>\n      <td>1</td>\n      <td>2</td>\n      <td>8</td>\n      <td>NaN</td>\n      <td>14084</td>\n      <td>2</td>\n      <td>0</td>\n      <td>5</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>2539329</td>\n      <td>1</td>\n      <td>prior</td>\n      <td>1</td>\n      <td>2</td>\n      <td>8</td>\n      <td>NaN</td>\n      <td>12427</td>\n      <td>3</td>\n      <td>0</td>\n      <td>5</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>2539329</td>\n      <td>1</td>\n      <td>prior</td>\n      <td>1</td>\n      <td>2</td>\n      <td>8</td>\n      <td>NaN</td>\n      <td>26088</td>\n      <td>4</td>\n      <td>0</td>\n      <td>5</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>2539329</td>\n      <td>1</td>\n      <td>prior</td>\n      <td>1</td>\n      <td>2</td>\n      <td>8</td>\n      <td>NaN</td>\n      <td>26405</td>\n      <td>5</td>\n      <td>0</td>\n      <td>5</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}]}, {"cell_type": "code", "source": "orders_products['p_reordered']= orders_products['reordered']==1\norders_products['non_first_order']= orders_products['order_number']>1\n\nus=orders_products\n\n# We group orders_products by user_id & calculate the variables based on different user_id\nus=orders_products.groupby('user_id').agg(\n    \n     user_total_products =('user_id','count') ,\n     p_reordered =('p_reordered', sum) ,\n     non_first_order =('non_first_order', sum),\n     user_distinct_products=('product_id','nunique')\n\n).reset_index()\n#    us['user_reorder_ratio'] = sum(reordered == 1) / sum(order_number > 1)\nus['user_reorder_ratio']=us['p_reordered']/us['non_first_order']\n\ndel us[\"p_reordered\"],us[\"non_first_order\"]\ndel orders_products['p_reordered' ],orders_products['non_first_order']\n\nus.head(20)", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:23.756001Z", "iopub.execute_input": "2022-05-28T08:47:23.756260Z", "iopub.status.idle": "2022-05-28T08:47:34.686385Z", "shell.execute_reply.started": "2022-05-28T08:47:23.756226Z", "shell.execute_reply": "2022-05-28T08:47:34.685708Z"}, "trusted": true}, "execution_count": 34, "outputs": [{"execution_count": 34, "output_type": "execute_result", "data": {"text/plain": "    user_id  user_total_products  user_distinct_products  user_reorder_ratio\n0         1                   59                      18            0.759259\n1         2                  195                     102            0.510989\n2         3                   88                      33            0.705128\n3         4                   18                      17            0.071429\n4         5                   37                      23            0.538462\n5         6                   14                      12            0.200000\n6         7                  206                      68            0.711340\n7         8                   49                      36            0.464286\n8         9                   76                      58            0.391304\n9        10                  143                      94            0.355072\n10       11                   94                      61            0.407407\n11       12                   74                      61            0.183099\n12       13                   81                      29            0.684211\n13       14                  210                     142            0.331707\n14       15                   72                      13            0.867647\n15       16                   70                      46            0.436364\n16       17                  294                      83            0.725086\n17       18                   39                      29            0.333333\n18       19                  204                     133            0.412791\n19       20                   22                       7            0.833333", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>user_id</th>\n      <th>user_total_products</th>\n      <th>user_distinct_products</th>\n      <th>user_reorder_ratio</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>59</td>\n      <td>18</td>\n      <td>0.759259</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2</td>\n      <td>195</td>\n      <td>102</td>\n      <td>0.510989</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>3</td>\n      <td>88</td>\n      <td>33</td>\n      <td>0.705128</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>4</td>\n      <td>18</td>\n      <td>17</td>\n      <td>0.071429</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>37</td>\n      <td>23</td>\n      <td>0.538462</td>\n    </tr>\n    <tr>\n      <th>5</th>\n      <td>6</td>\n      <td>14</td>\n      <td>12</td>\n      <td>0.200000</td>\n    </tr>\n    <tr>\n      <th>6</th>\n      <td>7</td>\n      <td>206</td>\n      <td>68</td>\n      <td>0.711340</td>\n    </tr>\n    <tr>\n      <th>7</th>\n      <td>8</td>\n      <td>49</td>\n      <td>36</td>\n      <td>0.464286</td>\n    </tr>\n    <tr>\n      <th>8</th>\n      <td>9</td>\n      <td>76</td>\n      <td>58</td>\n      <td>0.391304</td>\n    </tr>\n    <tr>\n      <th>9</th>\n      <td>10</td>\n      <td>143</td>\n      <td>94</td>\n      <td>0.355072</td>\n    </tr>\n    <tr>\n      <th>10</th>\n      <td>11</td>\n      <td>94</td>\n      <td>61</td>\n      <td>0.407407</td>\n    </tr>\n    <tr>\n      <th>11</th>\n      <td>12</td>\n      <td>74</td>\n      <td>61</td>\n      <td>0.183099</td>\n    </tr>\n    <tr>\n      <th>12</th>\n      <td>13</td>\n      <td>81</td>\n      <td>29</td>\n      <td>0.684211</td>\n    </tr>\n    <tr>\n      <th>13</th>\n      <td>14</td>\n      <td>210</td>\n      <td>142</td>\n      <td>0.331707</td>\n    </tr>\n    <tr>\n      <th>14</th>\n      <td>15</td>\n      <td>72</td>\n      <td>13</td>\n      <td>0.867647</td>\n    </tr>\n    <tr>\n      <th>15</th>\n      <td>16</td>\n      <td>70</td>\n      <td>46</td>\n      <td>0.436364</td>\n    </tr>\n    <tr>\n      <th>16</th>\n      <td>17</td>\n      <td>294</td>\n      <td>83</td>\n      <td>0.725086</td>\n    </tr>\n    <tr>\n      <th>17</th>\n      <td>18</td>\n      <td>39</td>\n      <td>29</td>\n      <td>0.333333</td>\n    </tr>\n    <tr>\n      <th>18</th>\n      <td>19</td>\n      <td>204</td>\n      <td>133</td>\n      <td>0.412791</td>\n    </tr>\n    <tr>\n      <th>19</th>\n      <td>20</td>\n      <td>22</td>\n      <td>7</td>\n      <td>0.833333</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}]}, {"cell_type": "markdown", "source": "#### Then we combine the users and us tables ussing inner_join() function and we calculate the final variable:\n\nuser_average_basket: Average number of basket items per order per user", "metadata": {}}, {"cell_type": "code", "source": "users =pd.merge(users,us ,on='user_id',  how='inner')\n\n# We calculate the user_average_basket variable\nusers['user_average_basket'] = users['user_total_products'] / users['user_orders']\nusers.head()", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:34.687744Z", "iopub.execute_input": "2022-05-28T08:47:34.688206Z", "iopub.status.idle": "2022-05-28T08:47:34.734847Z", "shell.execute_reply.started": "2022-05-28T08:47:34.688167Z", "shell.execute_reply": "2022-05-28T08:47:34.734059Z"}, "trusted": true}, "execution_count": 35, "outputs": [{"execution_count": 35, "output_type": "execute_result", "data": {"text/plain": "   user_id  user_orders  user_period  user_mean_days_since_prior  \\\n0        1           10        176.0                   19.562500   \n1        2           14        198.0                   15.234375   \n2        3           12        133.0                   12.093750   \n3        4            5         55.0                   13.750000   \n4        5            4         40.0                   13.335938   \n\n   user_total_products  user_distinct_products  user_reorder_ratio  \\\n0                   59                      18            0.759259   \n1                  195                     102            0.510989   \n2                   88                      33            0.705128   \n3                   18                      17            0.071429   \n4                   37                      23            0.538462   \n\n   user_average_basket  \n0             5.900000  \n1            13.928571  \n2             7.333333  \n3             3.600000  \n4             9.250000  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>user_id</th>\n      <th>user_orders</th>\n      <th>user_period</th>\n      <th>user_mean_days_since_prior</th>\n      <th>user_total_products</th>\n      <th>user_distinct_products</th>\n      <th>user_reorder_ratio</th>\n      <th>user_average_basket</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>10</td>\n      <td>176.0</td>\n      <td>19.562500</td>\n      <td>59</td>\n      <td>18</td>\n      <td>0.759259</td>\n      <td>5.900000</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2</td>\n      <td>14</td>\n      <td>198.0</td>\n      <td>15.234375</td>\n      <td>195</td>\n      <td>102</td>\n      <td>0.510989</td>\n      <td>13.928571</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>3</td>\n      <td>12</td>\n      <td>133.0</td>\n      <td>12.093750</td>\n      <td>88</td>\n      <td>33</td>\n      <td>0.705128</td>\n      <td>7.333333</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>4</td>\n      <td>5</td>\n      <td>55.0</td>\n      <td>13.750000</td>\n      <td>18</td>\n      <td>17</td>\n      <td>0.071429</td>\n      <td>3.600000</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>4</td>\n      <td>40.0</td>\n      <td>13.335938</td>\n      <td>37</td>\n      <td>23</td>\n      <td>0.538462</td>\n      <td>9.250000</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}]}, {"cell_type": "markdown", "source": "We now identify the future order per user and add them in the users table. The future orders are indicated as train and test in the eval_set variable. As a result, we will know what is the order_id of the future order per user, whether this order belongs in the train or test set, and the time in days since the last order.", "metadata": {}}, {"cell_type": "code", "source": "# we exclude prior orders and thus we keep only train and test orders\nus = orders[orders['eval_set'] != 'prior']\nus['time_since_last_order'] = us['days_since_prior_order']\nus['future_order_dow'] = us['order_dow']\nus['future_order_hour_of_day'] = us['order_hour_of_day']\n\nus = us[['user_id','order_id','eval_set','time_since_last_order', 'future_order_dow', 'future_order_hour_of_day']]\n\n# We combine users and us tables and store the results into the users table\nusers_features = pd.merge(users , us, on='user_id', how='inner') \n\n# We delete the us table\ndel us, users\n\nusers_features.head()", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:34.738126Z", "iopub.execute_input": "2022-05-28T08:47:34.738359Z", "iopub.status.idle": "2022-05-28T08:47:34.816229Z", "shell.execute_reply.started": "2022-05-28T08:47:34.738331Z", "shell.execute_reply": "2022-05-28T08:47:34.814548Z"}, "trusted": true}, "execution_count": 36, "outputs": [{"name": "stderr", "text": "/opt/conda/lib/python3.7/site-packages/ipykernel_launcher.py:3: SettingWithCopyWarning: \nA value is trying to be set on a copy of a slice from a DataFrame.\nTry using .loc[row_indexer,col_indexer] = value instead\n\nSee the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n  This is separate from the ipykernel package so we can avoid doing imports until\n/opt/conda/lib/python3.7/site-packages/ipykernel_launcher.py:4: SettingWithCopyWarning: \nA value is trying to be set on a copy of a slice from a DataFrame.\nTry using .loc[row_indexer,col_indexer] = value instead\n\nSee the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n  after removing the cwd from sys.path.\n/opt/conda/lib/python3.7/site-packages/ipykernel_launcher.py:5: SettingWithCopyWarning: \nA value is trying to be set on a copy of a slice from a DataFrame.\nTry using .loc[row_indexer,col_indexer] = value instead\n\nSee the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n  \"\"\"\n", "output_type": "stream"}, {"execution_count": 36, "output_type": "execute_result", "data": {"text/plain": "   user_id  user_orders  user_period  user_mean_days_since_prior  \\\n0        1           10        176.0                   19.562500   \n1        2           14        198.0                   15.234375   \n2        3           12        133.0                   12.093750   \n3        4            5         55.0                   13.750000   \n4        5            4         40.0                   13.335938   \n\n   user_total_products  user_distinct_products  user_reorder_ratio  \\\n0                   59                      18            0.759259   \n1                  195                     102            0.510989   \n2                   88                      33            0.705128   \n3                   18                      17            0.071429   \n4                   37                      23            0.538462   \n\n   user_average_basket  order_id eval_set  time_since_last_order  \\\n0             5.900000   1187899    train                   14.0   \n1            13.928571   1492625    train                   30.0   \n2             7.333333   2774568     test                   11.0   \n3             3.600000    329954     test                   30.0   \n4             9.250000   2196797    train                    6.0   \n\n   future_order_dow  future_order_hour_of_day  \n0                 4                         8  \n1                 1                        11  \n2                 5                        15  \n3                 3                        12  \n4                 0                        11  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>user_id</th>\n      <th>user_orders</th>\n      <th>user_period</th>\n      <th>user_mean_days_since_prior</th>\n      <th>user_total_products</th>\n      <th>user_distinct_products</th>\n      <th>user_reorder_ratio</th>\n      <th>user_average_basket</th>\n      <th>order_id</th>\n      <th>eval_set</th>\n      <th>time_since_last_order</th>\n      <th>future_order_dow</th>\n      <th>future_order_hour_of_day</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>10</td>\n      <td>176.0</td>\n      <td>19.562500</td>\n      <td>59</td>\n      <td>18</td>\n      <td>0.759259</td>\n      <td>5.900000</td>\n      <td>1187899</td>\n      <td>train</td>\n      <td>14.0</td>\n      <td>4</td>\n      <td>8</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2</td>\n      <td>14</td>\n      <td>198.0</td>\n      <td>15.234375</td>\n      <td>195</td>\n      <td>102</td>\n      <td>0.510989</td>\n      <td>13.928571</td>\n      <td>1492625</td>\n      <td>train</td>\n      <td>30.0</td>\n      <td>1</td>\n      <td>11</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>3</td>\n      <td>12</td>\n      <td>133.0</td>\n      <td>12.093750</td>\n      <td>88</td>\n      <td>33</td>\n      <td>0.705128</td>\n      <td>7.333333</td>\n      <td>2774568</td>\n      <td>test</td>\n      <td>11.0</td>\n      <td>5</td>\n      <td>15</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>4</td>\n      <td>5</td>\n      <td>55.0</td>\n      <td>13.750000</td>\n      <td>18</td>\n      <td>17</td>\n      <td>0.071429</td>\n      <td>3.600000</td>\n      <td>329954</td>\n      <td>test</td>\n      <td>30.0</td>\n      <td>3</td>\n      <td>12</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>4</td>\n      <td>40.0</td>\n      <td>13.335938</td>\n      <td>37</td>\n      <td>23</td>\n      <td>0.538462</td>\n      <td>9.250000</td>\n      <td>2196797</td>\n      <td>train</td>\n      <td>6.0</td>\n      <td>0</td>\n      <td>11</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}]}, {"cell_type": "markdown", "source": "### 2.2 Product dependent Features\n* Total number of orders per product\n* Avg position in cart for the product\n* The position in cart mostly repeated for the product\n* Avg of the number of items that co-occur with this product\n* The number of items that mostly co-occur with this product", "metadata": {}}, {"cell_type": "code", "source": "prod_features = orders_products.groupby(['product_id']).agg(\n    prod_freq = ('order_id', 'count'),\n    prod_avg_position = ('add_to_cart_order', 'mean')\n#     prod_avg_hour = ('order_hour_of_day', average_times_of_day),\n#     prod_avg_dow = ('order_dow', average_days)\n).reset_index()\n\nprod_features.head(20)", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:34.817332Z", "iopub.execute_input": "2022-05-28T08:47:34.817865Z", "iopub.status.idle": "2022-05-28T08:47:35.964492Z", "shell.execute_reply.started": "2022-05-28T08:47:34.817827Z", "shell.execute_reply": "2022-05-28T08:47:35.963630Z"}, "trusted": true}, "execution_count": 37, "outputs": [{"execution_count": 37, "output_type": "execute_result", "data": {"text/plain": "    product_id  prod_freq  prod_avg_position\n0            1       1852           5.801836\n1            2         90           9.888889\n2            3        277           6.415162\n3            4        329           9.507599\n4            5         15           6.466667\n5            6          8          14.125000\n6            7         30           7.966667\n7            8        165           8.418182\n8            9        156           7.608974\n9           10       2572           8.816874\n10          11        104           8.471154\n11          12        246           8.349593\n12          13          9           9.111111\n13          14         17           8.235294\n14          15          4           3.500000\n15          16         19          11.263158\n16          17         18           5.944444\n17          18        137           6.510949\n18          19          4          13.750000\n19          20          6          10.000000", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>product_id</th>\n      <th>prod_freq</th>\n      <th>prod_avg_position</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>1852</td>\n      <td>5.801836</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2</td>\n      <td>90</td>\n      <td>9.888889</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>3</td>\n      <td>277</td>\n      <td>6.415162</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>4</td>\n      <td>329</td>\n      <td>9.507599</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>15</td>\n      <td>6.466667</td>\n    </tr>\n    <tr>\n      <th>5</th>\n      <td>6</td>\n      <td>8</td>\n      <td>14.125000</td>\n    </tr>\n    <tr>\n      <th>6</th>\n      <td>7</td>\n      <td>30</td>\n      <td>7.966667</td>\n    </tr>\n    <tr>\n      <th>7</th>\n      <td>8</td>\n      <td>165</td>\n      <td>8.418182</td>\n    </tr>\n    <tr>\n      <th>8</th>\n      <td>9</td>\n      <td>156</td>\n      <td>7.608974</td>\n    </tr>\n    <tr>\n      <th>9</th>\n      <td>10</td>\n      <td>2572</td>\n      <td>8.816874</td>\n    </tr>\n    <tr>\n      <th>10</th>\n      <td>11</td>\n      <td>104</td>\n      <td>8.471154</td>\n    </tr>\n    <tr>\n      <th>11</th>\n      <td>12</td>\n      <td>246</td>\n      <td>8.349593</td>\n    </tr>\n    <tr>\n      <th>12</th>\n      <td>13</td>\n      <td>9</td>\n      <td>9.111111</td>\n    </tr>\n    <tr>\n      <th>13</th>\n      <td>14</td>\n      <td>17</td>\n      <td>8.235294</td>\n    </tr>\n    <tr>\n      <th>14</th>\n      <td>15</td>\n      <td>4</td>\n      <td>3.500000</td>\n    </tr>\n    <tr>\n      <th>15</th>\n      <td>16</td>\n      <td>19</td>\n      <td>11.263158</td>\n    </tr>\n    <tr>\n      <th>16</th>\n      <td>17</td>\n      <td>18</td>\n      <td>5.944444</td>\n    </tr>\n    <tr>\n      <th>17</th>\n      <td>18</td>\n      <td>137</td>\n      <td>6.510949</td>\n    </tr>\n    <tr>\n      <th>18</th>\n      <td>19</td>\n      <td>4</td>\n      <td>13.750000</td>\n    </tr>\n    <tr>\n      <th>19</th>\n      <td>20</td>\n      <td>6</td>\n      <td>10.000000</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}]}, {"cell_type": "markdown", "source": "* Probability a product is reordered after the first order → Fraction of people ordered at least one time.\n* In average how many times a product has been purchased by the users who purchased it at least once", "metadata": {}}, {"cell_type": "code", "source": "non_first_order = orders_products['order_number'] != 1\n\ngroupedorders_products = orders_products[non_first_order].groupby(['product_id']).agg(\n    prod_reorder_ratio = ('reordered', 'mean')\n).reset_index()\n\nprod_features = prod_features.merge(groupedorders_products, on='product_id', how='left')\n\n# Group by users who have bought it more than once\n# get the count of orders each user bought having the product. \ngroupedorders_products = orders_products[non_first_order].groupby(['product_id', 'user_id']).agg(\n    user_prod_freq = ('order_id', 'count')\n).reset_index()\n\n# get the avg # of orders the user will buy having that product after buying it for the first time\ngroupedorders_products = groupedorders_products.groupby(['product_id']).agg(\n    user_prod_avg_freq = ('user_prod_freq', 'mean')\n).reset_index()\n\nprod_features = prod_features.merge(groupedorders_products, on='product_id', how='left')\ndel groupedorders_products, non_first_order\n\nprod_features.head()", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:35.965982Z", "iopub.execute_input": "2022-05-28T08:47:35.966230Z", "iopub.status.idle": "2022-05-28T08:47:51.311150Z", "shell.execute_reply.started": "2022-05-28T08:47:35.966195Z", "shell.execute_reply": "2022-05-28T08:47:51.310470Z"}, "trusted": true}, "execution_count": 38, "outputs": [{"execution_count": 38, "output_type": "execute_result", "data": {"text/plain": "   product_id  prod_freq  prod_avg_position  prod_reorder_ratio  \\\n0           1       1852           5.801836            0.647662   \n1           2         90           9.888889            0.137931   \n2           3        277           6.415162            0.780769   \n3           4        329           9.507599            0.506897   \n4           5         15           6.466667            0.642857   \n\n   user_prod_avg_freq  \n0            2.641566  \n1            1.160000  \n2            3.714286  \n3            1.746988  \n4            2.333333  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>product_id</th>\n      <th>prod_freq</th>\n      <th>prod_avg_position</th>\n      <th>prod_reorder_ratio</th>\n      <th>user_prod_avg_freq</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>1852</td>\n      <td>5.801836</td>\n      <td>0.647662</td>\n      <td>2.641566</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2</td>\n      <td>90</td>\n      <td>9.888889</td>\n      <td>0.137931</td>\n      <td>1.160000</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>3</td>\n      <td>277</td>\n      <td>6.415162</td>\n      <td>0.780769</td>\n      <td>3.714286</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>4</td>\n      <td>329</td>\n      <td>9.507599</td>\n      <td>0.506897</td>\n      <td>1.746988</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>15</td>\n      <td>6.466667</td>\n      <td>0.642857</td>\n      <td>2.333333</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}]}, {"cell_type": "markdown", "source": "### 2.3 User x Product Predictors \n\n##### We now create predictors that indicate how a user behaves towards a specific product. We store these predictors in the data table, which is also the final table that we create. Towards this end, we use both prd and users tables. \n##### We start by calculating four new variables:\nup_orders:The total times a user ordered a product\n\nup_first_order: What was the first time a user purchased a product\n\nup_last_order: What was the last time a user purchased a product\n\nup_average_cart_position: The average position in a user's cart of a product", "metadata": {}}, {"cell_type": "code", "source": "# We create the data table starting from the orders_products table \ndata = orders_products\n\ndata = data.groupby(['user_id','product_id']).agg(\n\n up_orders= ('product_id', 'count'),\n up_first_order=('order_number', min),\n up_last_order = ('order_number',max),\n up_average_cart_position = ('add_to_cart_order','mean')\n#  up_avg_hour = ('order_hour_of_day', average_times_of_day),\n#  up_avg_dow = ('order_dow', average_days)\n).reset_index()\n \ndel orders_products\ndata.head(20)", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:47:51.312407Z", "iopub.execute_input": "2022-05-28T08:47:51.312637Z", "iopub.status.idle": "2022-05-28T08:48:03.629065Z", "shell.execute_reply.started": "2022-05-28T08:47:51.312604Z", "shell.execute_reply": "2022-05-28T08:48:03.628273Z"}, "trusted": true}, "execution_count": 39, "outputs": [{"execution_count": 39, "output_type": "execute_result", "data": {"text/plain": "    user_id  product_id  up_orders  up_first_order  up_last_order  \\\n0         1         196         10               1             10   \n1         1       10258          9               2             10   \n2         1       10326          1               5              5   \n3         1       12427         10               1             10   \n4         1       13032          3               2             10   \n5         1       13176          2               2              5   \n6         1       14084          1               1              1   \n7         1       17122          1               5              5   \n8         1       25133          8               3             10   \n9         1       26088          2               1              2   \n10        1       26405          2               1              4   \n11        1       30450          1               3              3   \n12        1       35951          1              10             10   \n13        1       38928          1              10             10   \n14        1       39657          1              10             10   \n15        1       41787          1               5              5   \n16        1       46149          3               8             10   \n17        1       49235          2               8              9   \n18        2          23          1               8              8   \n19        2          79          1              13             13   \n\n    up_average_cart_position  \n0                   1.400000  \n1                   3.333333  \n2                   5.000000  \n3                   3.300000  \n4                   6.333333  \n5                   6.000000  \n6                   2.000000  \n7                   6.000000  \n8                   4.000000  \n9                   4.500000  \n10                  5.000000  \n11                  5.000000  \n12                  7.000000  \n13                  4.000000  \n14                  3.000000  \n15                  7.000000  \n16                  3.000000  \n17                  3.500000  \n18                 12.000000  \n19                  3.000000  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>user_id</th>\n      <th>product_id</th>\n      <th>up_orders</th>\n      <th>up_first_order</th>\n      <th>up_last_order</th>\n      <th>up_average_cart_position</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>196</td>\n      <td>10</td>\n      <td>1</td>\n      <td>10</td>\n      <td>1.400000</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>1</td>\n      <td>10258</td>\n      <td>9</td>\n      <td>2</td>\n      <td>10</td>\n      <td>3.333333</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>1</td>\n      <td>10326</td>\n      <td>1</td>\n      <td>5</td>\n      <td>5</td>\n      <td>5.000000</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>1</td>\n      <td>12427</td>\n      <td>10</td>\n      <td>1</td>\n      <td>10</td>\n      <td>3.300000</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>1</td>\n      <td>13032</td>\n      <td>3</td>\n      <td>2</td>\n      <td>10</td>\n      <td>6.333333</td>\n    </tr>\n    <tr>\n      <th>5</th>\n      <td>1</td>\n      <td>13176</td>\n      <td>2</td>\n      <td>2</td>\n      <td>5</td>\n      <td>6.000000</td>\n    </tr>\n    <tr>\n      <th>6</th>\n      <td>1</td>\n      <td>14084</td>\n      <td>1</td>\n      <td>1</td>\n      <td>1</td>\n      <td>2.000000</td>\n    </tr>\n    <tr>\n      <th>7</th>\n      <td>1</td>\n      <td>17122</td>\n      <td>1</td>\n      <td>5</td>\n      <td>5</td>\n      <td>6.000000</td>\n    </tr>\n    <tr>\n      <th>8</th>\n      <td>1</td>\n      <td>25133</td>\n      <td>8</td>\n      <td>3</td>\n      <td>10</td>\n      <td>4.000000</td>\n    </tr>\n    <tr>\n      <th>9</th>\n      <td>1</td>\n      <td>26088</td>\n      <td>2</td>\n      <td>1</td>\n      <td>2</td>\n      <td>4.500000</td>\n    </tr>\n    <tr>\n      <th>10</th>\n      <td>1</td>\n      <td>26405</td>\n      <td>2</td>\n      <td>1</td>\n      <td>4</td>\n      <td>5.000000</td>\n    </tr>\n    <tr>\n      <th>11</th>\n      <td>1</td>\n      <td>30450</td>\n      <td>1</td>\n      <td>3</td>\n      <td>3</td>\n      <td>5.000000</td>\n    </tr>\n    <tr>\n      <th>12</th>\n      <td>1</td>\n      <td>35951</td>\n      <td>1</td>\n      <td>10</td>\n      <td>10</td>\n      <td>7.000000</td>\n    </tr>\n    <tr>\n      <th>13</th>\n      <td>1</td>\n      <td>38928</td>\n      <td>1</td>\n      <td>10</td>\n      <td>10</td>\n      <td>4.000000</td>\n    </tr>\n    <tr>\n      <th>14</th>\n      <td>1</td>\n      <td>39657</td>\n      <td>1</td>\n      <td>10</td>\n      <td>10</td>\n      <td>3.000000</td>\n    </tr>\n    <tr>\n      <th>15</th>\n      <td>1</td>\n      <td>41787</td>\n      <td>1</td>\n      <td>5</td>\n      <td>5</td>\n      <td>7.000000</td>\n    </tr>\n    <tr>\n      <th>16</th>\n      <td>1</td>\n      <td>46149</td>\n      <td>3</td>\n      <td>8</td>\n      <td>10</td>\n      <td>3.000000</td>\n    </tr>\n    <tr>\n      <th>17</th>\n      <td>1</td>\n      <td>49235</td>\n      <td>2</td>\n      <td>8</td>\n      <td>9</td>\n      <td>3.500000</td>\n    </tr>\n    <tr>\n      <th>18</th>\n      <td>2</td>\n      <td>23</td>\n      <td>1</td>\n      <td>8</td>\n      <td>8</td>\n      <td>12.000000</td>\n    </tr>\n    <tr>\n      <th>19</th>\n      <td>2</td>\n      <td>79</td>\n      <td>1</td>\n      <td>13</td>\n      <td>13</td>\n      <td>3.000000</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}]}, {"cell_type": "markdown", "source": "#### Then We compine the data table with the prd and users tables and we calculate the final three variables.\n\nup_order_rate: Percentage of user’s orders that include a specific product\n\nup_orders_since_last_order: Number of orders since user’s last order of a product\n\nup_order_rate_since_first_order: Pecentage of orders since first order of a product in which a user purchased this product", "metadata": {}}, {"cell_type": "code", "source": "\n# up_order_rate = up_orders / user_total_products\ndata = data.merge(users_features[['user_id','user_orders']], on='user_id' , how='left')\ndata['up_order_rate'] = data['up_orders']/data['user_orders']\n\n# up_orders_since_last_order = user_last_order - user_last_ordered_that_product\ndata['up_orders_since_last_order'] = data['user_orders'] - data['up_last_order']\n\n# From the moment the user know about the product, how frequent he then bought it in his next orders?\n# up_order_rate_since_first_order = up_orders / (user_orders - up_first_order + 1)\n# The + 1 is added since order_numbering starts from 1 not 0\ndata['up_order_rate_since_first_order'] = data['up_orders']/(data['user_orders'] - data['up_first_order'] + 1)\ndel data['user_orders']\n\ndata.head()", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:03.630401Z", "iopub.execute_input": "2022-05-28T08:48:03.630785Z", "iopub.status.idle": "2022-05-28T08:48:05.833647Z", "shell.execute_reply.started": "2022-05-28T08:48:03.630749Z", "shell.execute_reply": "2022-05-28T08:48:05.832881Z"}, "trusted": true}, "execution_count": 40, "outputs": [{"execution_count": 40, "output_type": "execute_result", "data": {"text/plain": "   user_id  product_id  up_orders  up_first_order  up_last_order  \\\n0        1         196         10               1             10   \n1        1       10258          9               2             10   \n2        1       10326          1               5              5   \n3        1       12427         10               1             10   \n4        1       13032          3               2             10   \n\n   up_average_cart_position  up_order_rate  up_orders_since_last_order  \\\n0                  1.400000            1.0                           0   \n1                  3.333333            0.9                           0   \n2                  5.000000            0.1                           5   \n3                  3.300000            1.0                           0   \n4                  6.333333            0.3                           0   \n\n   up_order_rate_since_first_order  \n0                         1.000000  \n1                         1.000000  \n2                         0.166667  \n3                         1.000000  \n4                         0.333333  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>user_id</th>\n      <th>product_id</th>\n      <th>up_orders</th>\n      <th>up_first_order</th>\n      <th>up_last_order</th>\n      <th>up_average_cart_position</th>\n      <th>up_order_rate</th>\n      <th>up_orders_since_last_order</th>\n      <th>up_order_rate_since_first_order</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>196</td>\n      <td>10</td>\n      <td>1</td>\n      <td>10</td>\n      <td>1.400000</td>\n      <td>1.0</td>\n      <td>0</td>\n      <td>1.000000</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>1</td>\n      <td>10258</td>\n      <td>9</td>\n      <td>2</td>\n      <td>10</td>\n      <td>3.333333</td>\n      <td>0.9</td>\n      <td>0</td>\n      <td>1.000000</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>1</td>\n      <td>10326</td>\n      <td>1</td>\n      <td>5</td>\n      <td>5</td>\n      <td>5.000000</td>\n      <td>0.1</td>\n      <td>5</td>\n      <td>0.166667</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>1</td>\n      <td>12427</td>\n      <td>10</td>\n      <td>1</td>\n      <td>10</td>\n      <td>3.300000</td>\n      <td>1.0</td>\n      <td>0</td>\n      <td>1.000000</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>1</td>\n      <td>13032</td>\n      <td>3</td>\n      <td>2</td>\n      <td>10</td>\n      <td>6.333333</td>\n      <td>0.3</td>\n      <td>0</td>\n      <td>0.333333</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}]}, {"cell_type": "markdown", "source": "* It seems that user_id = 1 is always ordering product_id = 196 in all his orders", "metadata": {}}, {"cell_type": "markdown", "source": "#### Merging product, user, product-user features", "metadata": {}}, {"cell_type": "code", "source": "# Merging user and product features with the final features dataframe\ndata = data.merge(users_features, on='user_id', how='left').merge(prod_features, on='product_id', how='left')\ndel users_features, prod_features\n\norder_products_future = order_products_train.merge(orders, on='order_id', how='left')\norder_products_future = order_products_future[['user_id', 'product_id', 'reordered']]\ndata = data.merge(order_products_future, on=['user_id', 'product_id'], how='left')\n\n# Set 0 to Product who didn't exists in the future order so model can predict them as Not in future order.\ndata['reordered'].fillna(0, inplace = True)", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:05.835232Z", "iopub.execute_input": "2022-05-28T08:48:05.835501Z", "iopub.status.idle": "2022-05-28T08:48:22.468448Z", "shell.execute_reply.started": "2022-05-28T08:48:05.835465Z", "shell.execute_reply": "2022-05-28T08:48:22.462959Z"}, "trusted": true}, "execution_count": 41, "outputs": []}, {"cell_type": "markdown", "source": "### 2.4 Time-dependent Predictors\n#### 2.4.1 User-dependent\n- On avg the hour the user buys this product at - The future order hour.\n- On avg which day of week the user buys this product at - The future order's day of week.\n#### 2.4.2 product-dependent\n- On avg the hour the product is most bought at - The future order hour.\n- Frequency of buying this product at the future's order hour of day. ??\n- Frequency of buying this product on the future's order day of week. ??", "metadata": {}}, {"cell_type": "code", "source": "'''\nCalculates the difference between 2 values from a looping sequence\ndist(X, Y) = min { X-Y, N-(X-Y) }\n'''\ndef diff_bet_time(arr1, arr2, max_value=23):\n    arr1 = pd.to_datetime(arr1, format='%H')\n    arr2 = pd.to_datetime(arr2, format='%H:%M:%S')\n    arr_diff = np.abs(arr1.dt.hour-arr2.dt.hour)\n    return np.minimum(arr_diff, max_value- (arr_diff-1))\n\n'''\nCalculates the difference between 2 values from a looping sequence\ndist(X, Y) = min { X-Y, N-(X-Y) }\n'''\ndef diff_bet_dow(arr1, arr2, max_value=6):\n    arr_diff = np.abs(arr1-arr2)\n    return np.minimum(arr_diff, max_value- (arr_diff-1))", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:22.472160Z", "iopub.execute_input": "2022-05-28T08:48:22.472518Z", "iopub.status.idle": "2022-05-28T08:48:22.481253Z", "shell.execute_reply.started": "2022-05-28T08:48:22.472480Z", "shell.execute_reply": "2022-05-28T08:48:22.478988Z"}, "trusted": true}, "execution_count": 42, "outputs": []}, {"cell_type": "code", "source": "# data['up_hour_diff'] = diff_bet_time(data['future_order_hour_of_day'], data['up_avg_hour'])\n# data['up_dow_diff'] = diff_bet_dow(data['future_order_dow'], data['up_avg_dow'])\n\n# data['prod_hour_diff'] = diff_bet_time(data['future_order_hour_of_day'], data['prod_avg_hour'], )\n# data['prod_dow_diff'] = diff_bet_dow(data['prod_avg_dow'], data['future_order_dow'])\n\n# del data['prod_avg_dow'], data['prod_avg_hour'], data['future_order_hour_of_day'], data['up_avg_hour'], data['future_order_dow'], data['up_avg_dow']\ndel data['future_order_hour_of_day'], data['future_order_dow']", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:22.482613Z", "iopub.execute_input": "2022-05-28T08:48:22.482860Z", "iopub.status.idle": "2022-05-28T08:48:22.493379Z", "shell.execute_reply.started": "2022-05-28T08:48:22.482827Z", "shell.execute_reply": "2022-05-28T08:48:22.492650Z"}, "trusted": true}, "execution_count": 43, "outputs": []}, {"cell_type": "code", "source": "# # Saving features in a csv file\n# data.to_csv('./features.csv', index=False)", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:22.494646Z", "iopub.execute_input": "2022-05-28T08:48:22.494883Z", "iopub.status.idle": "2022-05-28T08:48:22.501935Z", "shell.execute_reply.started": "2022-05-28T08:48:22.494851Z", "shell.execute_reply": "2022-05-28T08:48:22.501156Z"}, "trusted": true}, "execution_count": 44, "outputs": []}, {"cell_type": "markdown", "source": "## Creating the X, y", "metadata": {}}, {"cell_type": "code", "source": "# To saveup memory, delete any dataframe we won't use next\ndel order_products_prior, order_products_train, products, orders, departments, aisles", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:22.502849Z", "iopub.execute_input": "2022-05-28T08:48:22.505058Z", "iopub.status.idle": "2022-05-28T08:48:22.514488Z", "shell.execute_reply.started": "2022-05-28T08:48:22.505016Z", "shell.execute_reply": "2022-05-28T08:48:22.513826Z"}, "trusted": true}, "execution_count": 45, "outputs": []}, {"cell_type": "markdown", "source": "#### Splitting data to train, valid and test\n- Note that we don't have y_test, since in the data given we don't know what products will be in the test future orders.\n- To have an direction on how our model performs, will create a validation set.\n- Train on train set, observe performance and tune parameters on valid set.\n", "metadata": {}}, {"cell_type": "code", "source": "# Splitting data to train and test\nX_train = data[data['eval_set'] == 'train']\ny_train = X_train['reordered']\nX_test = data[data['eval_set'] == 'test']\ndel data", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:22.516710Z", "iopub.execute_input": "2022-05-28T08:48:22.516967Z", "iopub.status.idle": "2022-05-28T08:48:25.905503Z", "shell.execute_reply.started": "2022-05-28T08:48:22.516934Z", "shell.execute_reply": "2022-05-28T08:48:25.904677Z"}, "trusted": true}, "execution_count": 46, "outputs": []}, {"cell_type": "code", "source": "from sklearn.model_selection import train_test_split\n\nprint('Class distribution before splitting')\npos_count = np.sum(X_train['reordered']==1)\nneg_count = np.sum(X_train['reordered']==0)\nprint('positive ratio: ', pos_count)\nprint('negative count: ', neg_count)\nprint('positive ratio: ', pos_count/(pos_count+neg_count))\n\nX_train, X_val, y_train, y_val = train_test_split(X_train, y_train, test_size=0.3)\nprint('Class distribution of Train set')\ntrain_pos_count = np.sum(X_train['reordered']==1)\ntrain_neg_count = np.sum(X_train['reordered']==0)\nprint('positive count: ', train_pos_count)\nprint('negative count: ', train_neg_count)\nprint('positive ratio: ', train_pos_count/(train_pos_count+train_neg_count))\n\nprint('Class distribution of Validation set')\nval_pos_count = np.sum(X_val['reordered']==1)\nval_neg_count = np.sum(X_val['reordered']==0)\nprint('positive count: ', val_pos_count)\nprint('negative count: ', val_neg_count)\nprint('positive ratio: ', val_pos_count/(val_pos_count+val_neg_count))\n\n# Removing eval_set and the target variable from features\nX_train_non_pred_vars = X_train[['product_id', 'order_id', 'user_id']]\nX_train.drop(['reordered', 'eval_set', 'product_id', 'order_id', 'user_id'], axis=1, inplace=True)\n\nX_val_non_pred_vars = X_val[['product_id', 'order_id', 'user_id']]\nX_val.drop(['reordered', 'eval_set', 'product_id', 'order_id', 'user_id'], axis=1, inplace=True)\n\nX_test_non_pred_vars = X_test[['product_id', 'order_id', 'user_id']]\nX_test.drop(['reordered', 'eval_set', 'product_id', 'order_id', 'user_id'], axis=1, inplace=True)\n\n# Drop features I suspect redundant or of no significant importance as the feature importance graph says\nX_train.drop(['up_orders', 'up_last_order', 'user_total_products', 'user_distinct_products'], axis=1, inplace=True)\nX_test.drop(['up_orders', 'up_last_order', 'user_total_products', 'user_distinct_products'], axis=1, inplace=True)\nX_val.drop(['up_orders', 'up_last_order', 'user_total_products', 'user_distinct_products'], axis=1, inplace=True)\n\n# Dropping the time dependent features 'up_dow_diff','prod_dow_diff','up_hour_diff','prod_hour_diff'\n# We had a strong intuition that these time features will be significant in the prediction,\n# However, they were the least feature's importance used by the model.\n# This is why We commented them across the code.", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:25.908676Z", "iopub.execute_input": "2022-05-28T08:48:25.908881Z", "iopub.status.idle": "2022-05-28T08:48:30.400658Z", "shell.execute_reply.started": "2022-05-28T08:48:25.908857Z", "shell.execute_reply": "2022-05-28T08:48:30.399887Z"}, "trusted": true}, "execution_count": 47, "outputs": [{"name": "stdout", "text": "Class distribution before splitting\npositive ratio:  828824\nnegative count:  7645837\npositive ratio:  0.09780025419305857\nClass distribution of Train set\npositive count:  580103\nnegative count:  5352159\npositive ratio:  0.09778782528485762\nClass distribution of Validation set\npositive count:  248721\nnegative count:  2293678\npositive ratio:  0.09782925496745397\n", "output_type": "stream"}]}, {"cell_type": "code", "source": "print(X_train.shape, y_train.shape)\nprint(X_val.shape, y_val.shape)\nprint(X_test.shape)", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:30.402133Z", "iopub.execute_input": "2022-05-28T08:48:30.402402Z", "iopub.status.idle": "2022-05-28T08:48:30.409360Z", "shell.execute_reply.started": "2022-05-28T08:48:30.402364Z", "shell.execute_reply": "2022-05-28T08:48:30.407408Z"}, "trusted": true}, "execution_count": 48, "outputs": [{"name": "stdout", "text": "(5932262, 15) (5932262,)\n(2542399, 15) (2542399,)\n(4833292, 15)\n", "output_type": "stream"}]}, {"cell_type": "code", "source": "X_train.columns", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:30.410553Z", "iopub.execute_input": "2022-05-28T08:48:30.410953Z", "iopub.status.idle": "2022-05-28T08:48:30.420382Z", "shell.execute_reply.started": "2022-05-28T08:48:30.410903Z", "shell.execute_reply": "2022-05-28T08:48:30.419566Z"}, "trusted": true}, "execution_count": 49, "outputs": [{"execution_count": 49, "output_type": "execute_result", "data": {"text/plain": "Index(['up_first_order', 'up_average_cart_position', 'up_order_rate',\n       'up_orders_since_last_order', 'up_order_rate_since_first_order',\n       'user_orders', 'user_period', 'user_mean_days_since_prior',\n       'user_reorder_ratio', 'user_average_basket', 'time_since_last_order',\n       'prod_freq', 'prod_avg_position', 'prod_reorder_ratio',\n       'user_prod_avg_freq'],\n      dtype='object')"}, "metadata": {}}]}, {"cell_type": "markdown", "source": "## Training the Model", "metadata": {}}, {"cell_type": "markdown", "source": "Notes from xgb documentation\n> * learning_rate: step size shrinkage used to prevent overfitting. Range is [0,1]\n> * max_depth: determines how deeply each tree is allowed to grow during any boosting round.\n> * subsample: percentage of samples used per tree. Low value can lead to underfitting.\n> * colsample_bytree: percentage of features used per tree. High value can lead to overfitting.\n> * n_estimators: number of trees you want to build.\n> * objective: determines the loss function to be used like reg:linear for regression problems, reg:logistic for classification problems with only decision, binary:logistic for classification problems with probability.\nXGBoost also supports regularization parameters to penalize models as they become more complex and reduce them to simple (parsimonious) models.\n> * gamma: controls whether a given node will split based on the expected reduction in loss after the split. A higher value leads to fewer splits. Supported only for tree-based learners.\n> * alpha: L1 regularization on leaf weights. A large value leads to more regularization.\n> * lambda: L2 regularization on leaf weights and is smoother than L1 regularization.", "metadata": {}}, {"cell_type": "code", "source": "import xgboost as xgb\nfrom sklearn import metrics\n\n# Training the model with features except the product_id, user_id, order_id columns\nclf = xgb.XGBClassifier(objective='binary:logistic', colsample_bytree = 0.4, learning_rate = 0.1,\n                max_depth = 5, reg_lambda = 5.0, n_estimators = 100)\nclf.fit(X_train,y_train)", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:48:30.421691Z", "iopub.execute_input": "2022-05-28T08:48:30.422009Z", "iopub.status.idle": "2022-05-28T08:59:51.002111Z", "shell.execute_reply.started": "2022-05-28T08:48:30.421975Z", "shell.execute_reply": "2022-05-28T08:59:51.001355Z"}, "trusted": true}, "execution_count": 50, "outputs": [{"execution_count": 50, "output_type": "execute_result", "data": {"text/plain": "XGBClassifier(base_score=0.5, booster='gbtree', callbacks=None,\n              colsample_bylevel=1, colsample_bynode=1, colsample_bytree=0.4,\n              early_stopping_rounds=None, enable_categorical=False,\n              eval_metric=None, gamma=0, gpu_id=-1, grow_policy='depthwise',\n              importance_type=None, interaction_constraints='',\n              learning_rate=0.1, max_bin=256, max_cat_to_onehot=4,\n              max_delta_step=0, max_depth=5, max_leaves=0, min_child_weight=1,\n              missing=nan, monotone_constraints='()', n_estimators=100,\n              n_jobs=0, num_parallel_tree=1, predictor='auto', random_state=0,\n              reg_alpha=0, reg_lambda=5.0, ...)"}, "metadata": {}}]}, {"cell_type": "code", "source": "import matplotlib.pyplot as plt\nfrom sklearn.feature_selection import SelectFromModel\n# Visualizing the Feature importance \nprint(clf.feature_importances_)\n\nxgb.plot_importance(clf)\nplt.show()", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:59:51.003309Z", "iopub.execute_input": "2022-05-28T08:59:51.003548Z", "iopub.status.idle": "2022-05-28T08:59:51.313048Z", "shell.execute_reply.started": "2022-05-28T08:59:51.003513Z", "shell.execute_reply": "2022-05-28T08:59:51.311320Z"}, "trusted": true}, "execution_count": 51, "outputs": [{"name": "stdout", "text": "[0.04249701 0.00872558 0.20411843 0.26609853 0.20724022 0.02846541\n 0.01143723 0.00768705 0.04054408 0.00784754 0.02597937 0.01363135\n 0.02697914 0.05464772 0.05410136]\n", "output_type": "stream"}, {"output_type": "display_data", "data": {"text/plain": "<Figure size 432x288 with 1 Axes>", "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": "## Analyzing PR-Curve ", "metadata": {}}, {"cell_type": "code", "source": "# keep probabilities for the positive outcome only\ny_test_prob = clf.predict_proba(X_test)[:, 1]\ny_val_prob = clf.predict_proba(X_val)[:, 1]\ny_train_prob = clf.predict_proba(X_train)[:, 1]", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T08:59:51.314357Z", "iopub.execute_input": "2022-05-28T08:59:51.314606Z", "iopub.status.idle": "2022-05-28T09:00:10.606664Z", "shell.execute_reply.started": "2022-05-28T08:59:51.314571Z", "shell.execute_reply": "2022-05-28T09:00:10.605936Z"}, "trusted": true}, "execution_count": 52, "outputs": []}, {"cell_type": "code", "source": "'''\nThis function maximizes a metric, while keeping another metric above a given threshold.\n'''\ndef maximize_metric_keep_metric(metric1_list, metric2_list, metric2_thresh=0.3):\n    for idx in range(len(metric1_list)):\n        if(metric2_list[idx] > metric2_thresh):\n            return idx\n    return -1", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T09:00:10.611126Z", "iopub.execute_input": "2022-05-28T09:00:10.611397Z", "iopub.status.idle": "2022-05-28T09:00:10.619823Z", "shell.execute_reply.started": "2022-05-28T09:00:10.611363Z", "shell.execute_reply": "2022-05-28T09:00:10.618911Z"}, "trusted": true}, "execution_count": 53, "outputs": []}, {"cell_type": "code", "source": "from sklearn.metrics import precision_recall_curve\n\n# Choosing Threshold that maximizes the f1_score\nprecision, recall, thresholds = precision_recall_curve(y_val, y_val_prob)\nf1_scores = 2*recall*precision/(recall+precision)\nopt_indx = np.argmax(f1_scores)\nprint(\"Maximuim f1_score for the positive class: \", f1_scores[opt_indx])\nprint(\"Correspoding precision: \", precision[opt_indx])\nprint(\"Correspoding recall: \", recall[opt_indx])\nprint(\"Correspoding Threshold: \", thresholds[opt_indx])\nbest_thresh = thresholds[opt_indx]", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T09:05:44.442359Z", "iopub.execute_input": "2022-05-28T09:05:44.442938Z", "iopub.status.idle": "2022-05-28T09:05:45.094411Z", "shell.execute_reply.started": "2022-05-28T09:05:44.442885Z", "shell.execute_reply": "2022-05-28T09:05:45.093607Z"}, "trusted": true}, "execution_count": 55, "outputs": [{"name": "stdout", "text": "<PERSON><PERSON>m f1_score for the positive class:  0.4400228151792239\nCorrespoding precision:  0.395227162841929\nCorrespoding recall:  0.496270922037142\nCorrespoding Threshold:  0.21708147\n", "output_type": "stream"}]}, {"cell_type": "code", "source": "# Choosing Threshold that maximizes recall, while keeping precision above 0.3\nopt_indx = maximize_metric_keep_metric(metric1_list=recall, metric2_list=precision, metric2_thresh=0.3)\nprint(\"Max recall for the positive class: \", recall[opt_indx])\nprint(\"Correspoding precision: \", precision[opt_indx])\nprint(\"Correspoding f1_score: \", f1_scores[opt_indx])\nprint(\"Correspoding Threshold: \", thresholds[opt_indx])\nbest_thresh = thresholds[opt_indx]", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T09:06:00.518826Z", "iopub.execute_input": "2022-05-28T09:06:00.519415Z", "iopub.status.idle": "2022-05-28T09:06:01.071591Z", "shell.execute_reply.started": "2022-05-28T09:06:00.519376Z", "shell.execute_reply": "2022-05-28T09:06:01.070850Z"}, "trusted": true}, "execution_count": 56, "outputs": [{"name": "stdout", "text": "Max recall for the positive class:  0.6547175349085924\nCorrespoding precision:  0.30000036845576505\nCorrespoding f1_score:  0.4114629065085588\nCorrespoding Threshold:  0.13529855\n", "output_type": "stream"}]}, {"cell_type": "code", "source": "# plot the precision-recall curves\nno_skill = len(y_val[y_val==1]) / len(y_val)\nplt.plot([0, 1], [no_skill, no_skill], linestyle='--', label='No Skill')\nplt.plot(recall, precision, marker='.', label='Logistic')\nplt.plot(recall[opt_indx], precision[opt_indx], marker='o', color='k', label='optimum threshold')\n# axis labels\nplt.xlabel('Recall')\nplt.ylabel('Precision')\nplt.title('Precision-Recall Curve')\n# show the legend\nplt.legend()\n# show the plot\nplt.show()", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T09:07:02.192609Z", "iopub.execute_input": "2022-05-28T09:07:02.193229Z", "iopub.status.idle": "2022-05-28T09:07:05.277131Z", "shell.execute_reply.started": "2022-05-28T09:07:02.193183Z", "shell.execute_reply": "2022-05-28T09:07:05.276445Z"}, "trusted": true}, "execution_count": 58, "outputs": [{"output_type": "display_data", "data": {"text/plain": "<Figure size 432x288 with 1 Axes>", "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "code", "source": "# Changing probabilities to crisp predicted values, useing the threshold obtained from the ROC-curve\ny_test_preds = y_test_prob>best_thresh\ny_val_preds = y_val_prob>best_thresh\ny_train_preds = y_train_prob>best_thresh", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T09:07:12.891480Z", "iopub.execute_input": "2022-05-28T09:07:12.892044Z", "iopub.status.idle": "2022-05-28T09:07:12.903213Z", "shell.execute_reply.started": "2022-05-28T09:07:12.892004Z", "shell.execute_reply": "2022-05-28T09:07:12.902423Z"}, "trusted": true}, "execution_count": 59, "outputs": []}, {"cell_type": "markdown", "source": "## Classification Report", "metadata": {}}, {"cell_type": "code", "source": "from sklearn.metrics import confusion_matrix, classification_report\n\nprint('-----------------CLASSIFICATION REPORT--------------------')\nprint(\"Train positive class count: \", y_train.sum())\nprint(\"Train negative class count: \", y_train.shape[0] - y_train.sum())\nprint(\"Train Set tn, fp, fn, tp:\",confusion_matrix(y_train, y_train_preds).ravel())\nprint(\"Train Set report:\",classification_report(y_train, y_train_preds))\n\nprint(\"Validation positive class count: \", y_val.sum())\nprint(\"Validation negative class count: \", y_val.shape[0] - y_val.sum())\nprint(\"Validation Set tn, fp, fn, tp:\",confusion_matrix(y_val, y_val_preds).ravel())\nprint(\"Validation Set report:\",classification_report(y_val, y_val_preds))", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T09:07:15.041476Z", "iopub.execute_input": "2022-05-28T09:07:15.041746Z", "iopub.status.idle": "2022-05-28T09:08:03.954313Z", "shell.execute_reply.started": "2022-05-28T09:07:15.041710Z", "shell.execute_reply": "2022-05-28T09:08:03.953466Z"}, "trusted": true}, "execution_count": 60, "outputs": [{"name": "stdout", "text": "-----------------CLASSIFICATION REPORT--------------------\nTrain positive class count:  580103.0\nTrain negative class count:  5352159.0\nTrain Set tn, fp, fn, tp: [4465892  886267  199556  380547]\nTrain Set report:               precision    recall  f1-score   support\n\n         0.0       0.96      0.83      0.89   5352159\n         1.0       0.30      0.66      0.41    580103\n\n    accuracy                           0.82   5932262\n   macro avg       0.63      0.75      0.65   5932262\nweighted avg       0.89      0.82      0.84   5932262\n\nValidation positive class count:  248721.0\nValidation negative class count:  2293678.0\nValidation Set tn, fp, fn, tp: [1913715  379963   85879  162842]\nValidation Set report:               precision    recall  f1-score   support\n\n         0.0       0.96      0.83      0.89   2293678\n         1.0       0.30      0.65      0.41    248721\n\n    accuracy                           0.82   2542399\n   macro avg       0.63      0.74      0.65   2542399\nweighted avg       0.89      0.82      0.84   2542399\n\n", "output_type": "stream"}]}, {"cell_type": "markdown", "source": "> * By forming the X, and y in this way, data is sparse. It's very skewed to the negative class. Thus accuracy is not a good measure for how the model is performing.\n> * First, we've found that there's alot of false negatives, so we considered to decrease it, or increase the recall of the positive class.\n> * This means, we want to reduce the number of products the model say user won't predict in the future while he/she will actually does. On the other side, it's okay to allow some false positives, when the model recommends a products the user won't buy in the next order.\n> * Our aim is to change the threshold to maximize the recall, while keeping the precision above a certain threshold. \n> * Since skewed class distribution we will consider PR-curve not ROC-curve.", "metadata": {}}, {"cell_type": "markdown", "source": "#### Preparing the sumission file", "metadata": {}}, {"cell_type": "code", "source": "import csv\n\n# Append prediction to test_order details\ntest_orders = X_test_non_pred_vars[['order_id','product_id']]\ntest_orders['reordered'] = y_test_preds\n\n# Extracting orders who have no predicted products\nempty_orders = test_orders.groupby(['order_id']).agg(\n    count_reorders = ('reordered', 'sum')\n).reset_index()\nempty_orders = empty_orders[empty_orders['count_reorders'] == 0]\n\n# For orders who have predicted products \n# Extract the products predicted to be in the future order\ntest_orders = test_orders[test_orders['reordered'] == 1]\n# For each order group its predicted products together into a list \ntest_orders = test_orders.groupby('order_id')['product_id'].apply(list).reset_index(name='products')\n\n\ntest_orders.head()", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T09:00:10.696033Z", "iopub.status.idle": "2022-05-28T09:00:10.696444Z", "shell.execute_reply.started": "2022-05-28T09:00:10.696213Z", "shell.execute_reply": "2022-05-28T09:00:10.696243Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "# csv header\nheaderNames = ['order_id', 'products']\nrows = []\n\nfor index, row in test_orders.iterrows():\n    products = ' '.join(str(product_id) for product_id in row['products']) \n    rows.append( \n        {'order_id': str(row['order_id']),\n        'products': products})\n\nfor index, row in empty_orders.iterrows():\n    rows.append( \n        {'order_id': str(row['order_id']),\n        'products': 'None'})\n    \nwith open('./submissions.csv', 'w', encoding='UTF-8', newline='') as f:\n    writer = csv.DictWriter(f, fieldnames=headerNames)\n    writer.writeheader()\n    writer.writerows(rows)", "metadata": {"execution": {"iopub.status.busy": "2022-05-28T09:00:10.697613Z", "iopub.status.idle": "2022-05-28T09:00:10.698470Z", "shell.execute_reply.started": "2022-05-28T09:00:10.698242Z", "shell.execute_reply": "2022-05-28T09:00:10.698266Z"}, "trusted": true}, "execution_count": null, "outputs": []}]}