const mongoose = require('mongoose');
const DemandForecast = require('../models/DemandForecast');
const Product = require('../models/Product');

async function createSampleForecasts() {
  try {
    console.log('🔍 Creating sample forecasts for testing...');
    
    // Connect to database
    require('dotenv').config();
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/inventory_management';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
    
    // Get some products
    const products = await Product.find().limit(10);
    console.log(`📦 Found ${products.length} products`);
    
    if (products.length === 0) {
      console.log('❌ No products found. Please add some products first.');
      process.exit(1);
    }
    
    // Clear existing forecasts
    await DemandForecast.deleteMany({});
    console.log('🧹 Cleared existing forecasts');
    
    // Create sample forecasts
    const forecasts = [];
    const baseDate = new Date();
    
    for (const product of products) {
      // Generate 30 days of forecast data
      const forecastData = [];
      for (let i = 1; i <= 30; i++) {
        const forecastDate = new Date(baseDate);
        forecastDate.setDate(baseDate.getDate() + i);
        
        // Generate realistic demand values
        const baseDemand = Math.floor(Math.random() * 20) + 5; // 5-25 units
        const seasonalFactor = 1 + 0.3 * Math.sin((i / 30) * 2 * Math.PI); // Seasonal variation
        const demand = Math.max(1, Math.floor(baseDemand * seasonalFactor));
        
        forecastData.push({
          date: forecastDate,
          predicted_demand: demand,
          lower_bound: Math.max(1, demand - Math.floor(demand * 0.2)),
          upper_bound: demand + Math.floor(demand * 0.3),
          confidence_interval: 0.95
        });
      }
      
      const forecast = new DemandForecast({
        product_id: product._id,
        product_name: product.name,
        sku: product.sku,
        category: 'Sample Category',
        forecast_horizon_days: 30,
        forecast_data: forecastData,
        model_type: 'prophet',
        model_version: '1.0.0',
        training_data_start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        training_data_end: new Date(),
        model_accuracy: 0.75 + Math.random() * 0.2, // 75-95% accuracy
        status: 'active',
        forecast_generated_at: new Date(),
        summary: {
          avg_daily_demand: forecastData.reduce((sum, d) => sum + d.predicted_demand, 0) / forecastData.length,
          total_predicted_demand: forecastData.reduce((sum, d) => sum + d.predicted_demand, 0),
          peak_demand_date: forecastData.reduce((max, d) => d.predicted_demand > max.predicted_demand ? d : max).date,
          low_demand_periods: forecastData.filter(d => d.predicted_demand < 5).length
        }
      });
      
      forecasts.push(forecast);
    }
    
    // Save all forecasts
    await DemandForecast.insertMany(forecasts);
    console.log(`✅ Created ${forecasts.length} sample forecasts`);
    
    // Display summary
    console.log('\n📊 Sample forecast summary:');
    for (const forecast of forecasts.slice(0, 3)) {
      console.log(`  ${forecast.product_name}: ${forecast.summary.avg_daily_demand.toFixed(1)} avg daily demand`);
    }
    
    await mongoose.disconnect();
    console.log('✅ Sample forecasts created successfully');
    
  } catch (error) {
    console.error('❌ Error creating sample forecasts:', error.message);
    process.exit(1);
  }
}

createSampleForecasts();
