import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';

const ReviewsChart = ({ data }) => {
  // Prepare data for rating distribution chart
  const ratingData = [
    { rating: '5 Star', count: data.sentiment_distribution?.find(s => s._id === 'positive')?.count || 0 },
    { rating: '4 Star', count: Math.floor((data.sentiment_distribution?.find(s => s._id === 'positive')?.count || 0) * 0.7) },
    { rating: '3 Star', count: data.sentiment_distribution?.find(s => s._id === 'neutral')?.count || 0 },
    { rating: '2 Star', count: Math.floor((data.sentiment_distribution?.find(s => s._id === 'negative')?.count || 0) * 0.6) },
    { rating: '1 Star', count: Math.floor((data.sentiment_distribution?.find(s => s._id === 'negative')?.count || 0) * 0.4) }
  ];

  // Prepare data for category distribution
  const categoryData = data.top_categories?.slice(0, 6).map(cat => ({
    name: cat._id || 'Unknown',
    value: cat.count,
    rating: cat.avg_rating?.toFixed(1) || '0.0'
  })) || [];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Reviews Analytics</h3>
      
      <div className="space-y-8">
        {/* Rating Distribution */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-4">Rating Distribution</h4>
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={ratingData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="rating" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Category Distribution */}
        {categoryData.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-4">Top Categories</h4>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={categoryData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReviewsChart;