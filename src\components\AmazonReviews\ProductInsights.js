import React, { useState, useEffect } from 'react';
import { Search, Star, TrendingUp, TrendingDown, Eye, Filter } from 'lucide-react';
import { amazonReviewsAPI } from '../../services/api';
import toast from 'react-hot-toast';

const ProductInsights = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [filters, setFilters] = useState({
    sortBy: 'recommendation_score',
    sortOrder: 'desc',
    min_rating: '',
    category: ''
  });

  useEffect(() => {
    fetchProducts();
  }, [filters]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await amazonReviewsAPI.getProducts({
        ...filters,
        limit: 20
      });
      setProducts(response.data.products);
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      fetchProducts();
      return;
    }

    try {
      setLoading(true);
      const response = await amazonReviewsAPI.searchProducts(searchTerm);
      setProducts(response.data);
    } catch (error) {
      console.error('Error searching products:', error);
      toast.error('Failed to search products');
    } finally {
      setLoading(false);
    }
  };

  const viewProductDetails = async (productId) => {
    try {
      const response = await amazonReviewsAPI.getProduct(productId);
      setSelectedProduct(response.data);
    } catch (error) {
      console.error('Error fetching product details:', error);
      toast.error('Failed to load product details');
    }
  };

  const ProductCard = ({ product }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {product.title || `Product ${product.product_id}`}
          </h3>
          {product.brand && (
            <p className="text-sm text-gray-600 mb-1">Brand: {product.brand}</p>
          )}
          {product.category && (
            <p className="text-sm text-gray-600 mb-2">Category: {product.category}</p>
          )}
        </div>
        <button
          onClick={() => viewProductDetails(product.product_id)}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <Eye className="h-5 w-5" />
        </button>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Star className="h-4 w-4 text-yellow-500 mr-1" />
            <span className="text-lg font-bold text-gray-900">
              {product.average_rating?.toFixed(1) || '0.0'}
            </span>
          </div>
          <p className="text-xs text-gray-600">Average Rating</p>
        </div>
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <p className="text-lg font-bold text-gray-900">
            {product.total_reviews?.toLocaleString() || 0}
          </p>
          <p className="text-xs text-gray-600">Total Reviews</p>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Recommendation Score</span>
          <span className="text-sm font-medium text-gray-900">
            {Math.round((product.recommendation_score || 0) * 100)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full"
            style={{ width: `${(product.recommendation_score || 0) * 100}%` }}
          ></div>
        </div>
      </div>

      {product.sentiment_distribution && (
        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">Sentiment Distribution</p>
          <div className="flex space-x-2">
            <div className="flex-1 text-center">
              <div className="text-sm font-medium text-green-600">
                {product.sentiment_distribution.positive || 0}
              </div>
              <div className="text-xs text-gray-500">Positive</div>
            </div>
            <div className="flex-1 text-center">
              <div className="text-sm font-medium text-gray-600">
                {product.sentiment_distribution.neutral || 0}
              </div>
              <div className="text-xs text-gray-500">Neutral</div>
            </div>
            <div className="flex-1 text-center">
              <div className="text-sm font-medium text-red-600">
                {product.sentiment_distribution.negative || 0}
              </div>
              <div className="text-xs text-gray-500">Negative</div>
            </div>
          </div>
        </div>
      )}

      {product.strengths && product.strengths.length > 0 && (
        <div className="mb-2">
          <p className="text-sm text-gray-600 mb-1">Top Strengths:</p>
          <div className="flex flex-wrap gap-1">
            {product.strengths.slice(0, 3).map((strength, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
              >
                {strength}
              </span>
            ))}
          </div>
        </div>
      )}

      {product.weaknesses && product.weaknesses.length > 0 && (
        <div>
          <p className="text-sm text-gray-600 mb-1">Areas for Improvement:</p>
          <div className="flex flex-wrap gap-1">
            {product.weaknesses.slice(0, 3).map((weakness, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full"
              >
                {weakness}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const ProductDetailModal = ({ product, onClose }) => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {product.title || `Product ${product.product_id}`}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              ×
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Product Info */}
            <div>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Product Information</h3>
                  <div className="space-y-2 text-sm">
                    <p><strong>Product ID:</strong> {product.product_id}</p>
                    {product.brand && <p><strong>Brand:</strong> {product.brand}</p>}
                    {product.category && <p><strong>Category:</strong> {product.category}</p>}
                    {product.description && <p><strong>Description:</strong> {product.description}</p>}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Review Statistics</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">
                        {product.average_rating?.toFixed(1) || '0.0'}
                      </div>
                      <div className="text-sm text-gray-600">Average Rating</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">
                        {product.total_reviews?.toLocaleString() || 0}
                      </div>
                      <div className="text-sm text-gray-600">Total Reviews</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Insights */}
            <div>
              <div className="space-y-4">
                {product.top_keywords && product.top_keywords.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Top Keywords</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.top_keywords.slice(0, 10).map((keyword, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                        >
                          {keyword.word} ({keyword.frequency})
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {product.common_themes && product.common_themes.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Common Themes</h3>
                    <div className="space-y-2">
                      {product.common_themes.slice(0, 5).map((theme, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">{theme.theme}</span>
                          <div className="flex items-center">
                            <span className="text-sm text-gray-500 mr-2">
                              {theme.frequency} mentions
                            </span>
                            {theme.sentiment_score > 0 ? (
                              <TrendingUp className="h-4 w-4 text-green-500" />
                            ) : theme.sentiment_score < 0 ? (
                              <TrendingDown className="h-4 w-4 text-red-500" />
                            ) : (
                              <div className="h-4 w-4" />
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {product.strengths && product.strengths.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Strengths</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.strengths.map((strength, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full"
                        >
                          {strength}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {product.weaknesses && product.weaknesses.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Areas for Improvement</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.weaknesses.map((weakness, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-red-100 text-red-800 text-sm rounded-full"
                        >
                          {weakness}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <select
              value={filters.sortBy}
              onChange={(e) => setFilters({ ...filters, sortBy: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="recommendation_score">Recommendation Score</option>
              <option value="average_rating">Average Rating</option>
              <option value="total_reviews">Total Reviews</option>
            </select>
            
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Search
            </button>
          </div>
        </div>
      </div>

      {/* Products Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3 mb-4"></div>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="h-16 bg-gray-200 rounded"></div>
                <div className="h-16 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product) => (
            <ProductCard key={product.product_id} product={product} />
          ))}
        </div>
      )}

      {/* Product Detail Modal */}
      {selectedProduct && (
        <ProductDetailModal
          product={selectedProduct}
          onClose={() => setSelectedProduct(null)}
        />
      )}

      {/* Empty State */}
      {!loading && products.length === 0 && (
        <div className="text-center py-12">
          <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
          <p className="text-gray-500">Try adjusting your search terms or filters.</p>
        </div>
      )}
    </div>
  );
};

export default ProductInsights;