const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const AmazonProduct = require('./models/AmazonProduct');
const AmazonReview = require('./models/AmazonReview');
const InstacartProduct = require('./models/InstacartProduct');
const InstacartOrderProduct = require('./models/InstacartOrderProduct');
const InstacartAisle = require('./models/InstacartAisle');
const InstacartDepartment = require('./models/InstacartDepartment');
const Product = require('./models/Product');
const DemandForecast = require('./models/DemandForecast');

async function testDatabaseConnectivity() {
  console.log('🔍 Testing Database Connectivity and Data Verification\n');
  
  try {
    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB connected successfully\n');

    // Test Amazon Fine Food Reviews Data
    console.log('🍯 Testing Amazon Fine Food Reviews Data:');
    console.log('=' .repeat(50));
    
    const amazonProductCount = await AmazonProduct.countDocuments();
    console.log(`📊 Amazon Products: ${amazonProductCount}`);
    
    const amazonReviewCount = await AmazonReview.countDocuments();
    console.log(`📝 Amazon Reviews: ${amazonReviewCount}`);
    
    if (amazonProductCount > 0) {
      const sampleAmazonProduct = await AmazonProduct.findOne().limit(1);
      console.log(`📦 Sample Amazon Product: ${sampleAmazonProduct?.title || 'N/A'}`);
      console.log(`⭐ Rating: ${sampleAmazonProduct?.average_rating || 'N/A'}`);
      console.log(`📊 Total Reviews: ${sampleAmazonProduct?.total_reviews || 'N/A'}`);
    }
    
    if (amazonReviewCount > 0) {
      const sampleAmazonReview = await AmazonReview.findOne().limit(1);
      console.log(`💬 Sample Review Score: ${sampleAmazonReview?.score || 'N/A'}`);
      console.log(`📅 Review Date: ${sampleAmazonReview?.time || 'N/A'}`);
    }
    
    console.log();

    // Test Instacart Market Basket Data
    console.log('🛒 Testing Instacart Market Basket Data:');
    console.log('=' .repeat(50));
    
    const instacartProductCount = await InstacartProduct.countDocuments();
    console.log(`📊 Instacart Products: ${instacartProductCount}`);
    
    const instacartOrderProductCount = await InstacartOrderProduct.countDocuments();
    console.log(`📦 Instacart Order Products: ${instacartOrderProductCount}`);
    
    const instacartAisleCount = await InstacartAisle.countDocuments();
    console.log(`🏪 Instacart Aisles: ${instacartAisleCount}`);
    
    const instacartDepartmentCount = await InstacartDepartment.countDocuments();
    console.log(`🏢 Instacart Departments: ${instacartDepartmentCount}`);
    
    if (instacartProductCount > 0) {
      const sampleInstacartProduct = await InstacartProduct.findOne().limit(1);
      console.log(`📦 Sample Instacart Product: ${sampleInstacartProduct?.product_name || 'N/A'}`);
      console.log(`🏪 Aisle ID: ${sampleInstacartProduct?.aisle_id || 'N/A'}`);
      console.log(`🏢 Department ID: ${sampleInstacartProduct?.department_id || 'N/A'}`);
    }
    
    console.log();

    // Test Inventory Management Data
    console.log('📋 Testing Inventory Management Data:');
    console.log('=' .repeat(50));
    
    const inventoryProductCount = await Product.countDocuments();
    console.log(`📊 Inventory Products: ${inventoryProductCount}`);
    
    const demandForecastCount = await DemandForecast.countDocuments();
    console.log(`📈 Demand Forecasts: ${demandForecastCount}`);
    
    if (inventoryProductCount > 0) {
      const sampleInventoryProduct = await Product.findOne().limit(1);
      console.log(`📦 Sample Inventory Product: ${sampleInventoryProduct?.name || 'N/A'}`);
      console.log(`📊 Stock Quantity: ${sampleInventoryProduct?.quantity || 'N/A'}`);
      console.log(`💰 Price: $${sampleInventoryProduct?.price || 'N/A'}`);
    }
    
    console.log();

    // Test Data Relationships
    console.log('🔗 Testing Data Relationships:');
    console.log('=' .repeat(50));
    
    // Test if Amazon products have reviews
    if (amazonProductCount > 0 && amazonReviewCount > 0) {
      const productWithReviews = await AmazonProduct.findOne({ total_reviews: { $gt: 0 } });
      if (productWithReviews) {
        const reviewsForProduct = await AmazonReview.countDocuments({ 
          product_id: productWithReviews.product_id 
        });
        console.log(`✅ Amazon Product "${productWithReviews.title}" has ${reviewsForProduct} reviews`);
      }
    }
    
    // Test if Instacart products have order data
    if (instacartProductCount > 0 && instacartOrderProductCount > 0) {
      const productWithOrders = await InstacartProduct.findOne();
      if (productWithOrders) {
        const ordersForProduct = await InstacartOrderProduct.countDocuments({ 
          product_id: productWithOrders.product_id 
        });
        console.log(`✅ Instacart Product "${productWithOrders.product_name}" appears in ${ordersForProduct} orders`);
      }
    }
    
    console.log();

    // Summary
    console.log('📋 Database Verification Summary:');
    console.log('=' .repeat(50));
    console.log(`✅ MongoDB Connection: Working`);
    console.log(`📊 Amazon Products: ${amazonProductCount > 0 ? '✅ Available' : '❌ Missing'}`);
    console.log(`📝 Amazon Reviews: ${amazonReviewCount > 0 ? '✅ Available' : '❌ Missing'}`);
    console.log(`🛒 Instacart Products: ${instacartProductCount > 0 ? '✅ Available' : '❌ Missing'}`);
    console.log(`📦 Instacart Orders: ${instacartOrderProductCount > 0 ? '✅ Available' : '❌ Missing'}`);
    console.log(`📋 Inventory Products: ${inventoryProductCount > 0 ? '✅ Available' : '❌ Missing'}`);
    console.log(`📈 Demand Forecasts: ${demandForecastCount > 0 ? '✅ Available' : '❌ Missing'}`);
    
    const totalDataPoints = amazonProductCount + amazonReviewCount + instacartProductCount + 
                           instacartOrderProductCount + inventoryProductCount + demandForecastCount;
    console.log(`\n📊 Total Data Points: ${totalDataPoints.toLocaleString()}`);
    
    if (totalDataPoints > 0) {
      console.log('\n🎉 Database verification completed successfully!');
      console.log('✅ Ready for chatbot testing and application launch.');
    } else {
      console.log('\n⚠️  Warning: No data found in database!');
      console.log('❌ Data import may be required before testing.');
    }

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n📡 Database connection closed.');
  }
}

// Run the test
testDatabaseConnectivity();
