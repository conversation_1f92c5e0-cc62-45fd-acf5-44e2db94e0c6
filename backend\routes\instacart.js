const express = require('express');
const router = express.Router();
const InstacartAisle = require('../models/InstacartAisle');
const InstacartDepartment = require('../models/InstacartDepartment');
const InstacartProduct = require('../models/InstacartProduct');
const InstacartOrder = require('../models/InstacartOrder');
const InstacartOrderProduct = require('../models/InstacartOrderProduct');

// Get all aisles
router.get('/aisles', async (req, res) => {
  try {
    const aisles = await InstacartAisle.find().sort({ aisle_id: 1 });
    res.json(aisles);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get all departments
router.get('/departments', async (req, res) => {
  try {
    const departments = await InstacartDepartment.find().sort({ department_id: 1 });
    res.json(departments);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get products with pagination and filtering
router.get('/products', async (req, res) => {
  try {
    const { page = 1, limit = 50, search, department_id, aisle_id } = req.query;
    const query = {};
    
    if (search) {
      query.$text = { $search: search };
    }
    if (department_id) {
      query.department_id = parseInt(department_id);
    }
    if (aisle_id) {
      query.aisle_id = parseInt(aisle_id);
    }
    
    const products = await InstacartProduct.find(query)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ product_id: 1 });
    
    const total = await InstacartProduct.countDocuments(query);
    
    res.json({
      products,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get orders with pagination
router.get('/orders', async (req, res) => {
  try {
    const { page = 1, limit = 50, user_id } = req.query;
    const query = {};
    
    if (user_id) {
      query.user_id = parseInt(user_id);
    }
    
    const orders = await InstacartOrder.find(query)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ order_id: 1 });
    
    const total = await InstacartOrder.countDocuments(query);
    
    res.json({
      orders,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get order details with products
router.get('/orders/:orderId', async (req, res) => {
  try {
    const orderId = parseInt(req.params.orderId);
    
    const order = await InstacartOrder.findOne({ order_id: orderId });
    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }
    
    const orderProducts = await InstacartOrderProduct.aggregate([
      { $match: { order_id: orderId } },
      {
        $lookup: {
          from: 'instacart_products',
          localField: 'product_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'instacart_aisles',
          localField: 'product.aisle_id',
          foreignField: 'aisle_id',
          as: 'aisle'
        }
      },
      { $unwind: '$aisle' },
      {
        $lookup: {
          from: 'instacart_departments',
          localField: 'product.department_id',
          foreignField: 'department_id',
          as: 'department'
        }
      },
      { $unwind: '$department' }
    ]);
    
    res.json({
      order,
      products: orderProducts
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Top products by order frequency
router.get('/analytics/top-products', async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    
    const topProducts = await InstacartOrderProduct.aggregate([
      {
        $group: {
          _id: '$product_id',
          order_count: { $sum: 1 },
          reorder_count: { $sum: '$reordered' }
        }
      },
      {
        $lookup: {
          from: 'instacart_products',
          localField: '_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'instacart_aisles',
          localField: 'product.aisle_id',
          foreignField: 'aisle_id',
          as: 'aisle'
        }
      },
      { $unwind: '$aisle' },
      {
        $lookup: {
          from: 'instacart_departments',
          localField: 'product.department_id',
          foreignField: 'department_id',
          as: 'department'
        }
      },
      { $unwind: '$department' },
      {
        $project: {
          product_id: '$_id',
          product_name: '$product.product_name',
          aisle: '$aisle.aisle',
          department: '$department.department',
          order_count: 1,
          reorder_count: 1,
          reorder_rate: { $divide: ['$reorder_count', '$order_count'] }
        }
      },
      { $sort: { order_count: -1 } },
      { $limit: parseInt(limit) }
    ]);
    
    res.json(topProducts);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Orders by hour of day
router.get('/analytics/orders-by-hour', async (req, res) => {
  try {
    const ordersByHour = await InstacartOrder.aggregate([
      {
        $group: {
          _id: '$order_hour_of_day',
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);
    
    res.json(ordersByHour);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Orders by day of week
router.get('/analytics/orders-by-dow', async (req, res) => {
  try {
    const ordersByDow = await InstacartOrder.aggregate([
      {
        $group: {
          _id: '$order_dow',
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);
    
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const result = ordersByDow.map(item => ({
      day_of_week: item._id,
      day_name: dayNames[item._id],
      count: item.count
    }));
    
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Department popularity
router.get('/analytics/department-stats', async (req, res) => {
  try {
    const departmentStats = await InstacartOrderProduct.aggregate([
      {
        $lookup: {
          from: 'instacart_products',
          localField: 'product_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'instacart_departments',
          localField: 'product.department_id',
          foreignField: 'department_id',
          as: 'department'
        }
      },
      { $unwind: '$department' },
      {
        $group: {
          _id: '$department.department_id',
          department_name: { $first: '$department.department' },
          total_orders: { $sum: 1 },
          unique_products: { $addToSet: '$product_id' },
          reorder_count: { $sum: '$reordered' }
        }
      },
      {
        $project: {
          department_id: '$_id',
          department_name: 1,
          total_orders: 1,
          unique_products_count: { $size: '$unique_products' },
          reorder_count: 1,
          reorder_rate: { $divide: ['$reorder_count', '$total_orders'] }
        }
      },
      { $sort: { total_orders: -1 } }
    ]);
    
    res.json(departmentStats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Aisle popularity
router.get('/analytics/aisle-stats', async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    
    const aisleStats = await InstacartOrderProduct.aggregate([
      {
        $lookup: {
          from: 'instacart_products',
          localField: 'product_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'instacart_aisles',
          localField: 'product.aisle_id',
          foreignField: 'aisle_id',
          as: 'aisle'
        }
      },
      { $unwind: '$aisle' },
      {
        $group: {
          _id: '$aisle.aisle_id',
          aisle_name: { $first: '$aisle.aisle' },
          total_orders: { $sum: 1 },
          unique_products: { $addToSet: '$product_id' },
          reorder_count: { $sum: '$reordered' }
        }
      },
      {
        $project: {
          aisle_id: '$_id',
          aisle_name: 1,
          total_orders: 1,
          unique_products_count: { $size: '$unique_products' },
          reorder_count: 1,
          reorder_rate: { $divide: ['$reorder_count', '$total_orders'] }
        }
      },
      { $sort: { total_orders: -1 } },
      { $limit: parseInt(limit) }
    ]);
    
    res.json(aisleStats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Dashboard summary
router.get('/analytics/summary', async (req, res) => {
  try {
    const [
      totalProducts,
      totalOrders,
      totalAisles,
      totalDepartments,
      totalOrderProducts
    ] = await Promise.all([
      InstacartProduct.countDocuments(),
      InstacartOrder.countDocuments(),
      InstacartAisle.countDocuments(),
      InstacartDepartment.countDocuments(),
      InstacartOrderProduct.countDocuments()
    ]);

    res.json({
      totalProducts,
      totalOrders,
      totalAisles,
      totalDepartments,
      totalOrderProducts
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Business Insights - Comprehensive analysis
router.get('/analytics/business-insights', async (req, res) => {
  try {
    // 1. Temporal Shopping Patterns
    const [peakDayData, peakHourData, avgDaysBetween] = await Promise.all([
      InstacartOrder.aggregate([
        { $group: { _id: '$order_dow', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 1 }
      ]),
      InstacartOrder.aggregate([
        { $group: { _id: '$order_hour_of_day', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 1 }
      ]),
      InstacartOrder.aggregate([
        { $match: { days_since_prior_order: { $ne: null } } },
        { $group: { _id: null, avgDays: { $avg: '$days_since_prior_order' } } }
      ])
    ]);

    // 2. Product Performance
    const [topProduct, topDepartment, overallReorderRate, organicProductsPct] = await Promise.all([
      InstacartOrderProduct.aggregate([
        {
          $lookup: {
            from: 'instacart_products',
            localField: 'product_id',
            foreignField: 'product_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        { $group: { _id: '$product.product_name', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 1 }
      ]),
      InstacartOrderProduct.aggregate([
        {
          $lookup: {
            from: 'instacart_products',
            localField: 'product_id',
            foreignField: 'product_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        {
          $lookup: {
            from: 'instacart_departments',
            localField: 'product.department_id',
            foreignField: 'department_id',
            as: 'department'
          }
        },
        { $unwind: '$department' },
        { $group: { _id: '$department.department', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 1 }
      ]),
      InstacartOrderProduct.aggregate([
        { $group: { _id: null, reorderRate: { $avg: '$reordered' } } }
      ]),
      InstacartProduct.aggregate([
        { $group: { _id: null, organicPct: { $avg: { $cond: ['$is_organic', 1, 0] } } } }
      ])
    ]);

    // 3. Customer Behavior
    const [avgOrdersPerCustomer, highLoyaltyCustomers, avgOrderSize] = await Promise.all([
      InstacartOrder.aggregate([
        { $group: { _id: '$user_id', orderCount: { $sum: 1 } } },
        { $group: { _id: null, avgOrders: { $avg: '$orderCount' } } }
      ]),
      InstacartOrder.aggregate([
        { $group: { _id: '$user_id', orderCount: { $sum: 1 } } },
        { $match: { orderCount: { $gt: 15 } } },
        { $count: 'highLoyaltyCount' }
      ]),
      InstacartOrderProduct.aggregate([
        { $group: { _id: '$order_id', orderSize: { $sum: 1 } } },
        { $group: { _id: null, avgSize: { $avg: '$orderSize' } } }
      ])
    ]);

    const totalCustomers = await InstacartOrder.distinct('user_id').then(users => users.length);

    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    const insights = {
      temporal_patterns: {
        peak_day: peakDayData[0] ? dayNames[peakDayData[0]._id] : 'N/A',
        peak_hour: peakHourData[0] ? peakHourData[0]._id : 'N/A',
        avg_reorder_cycle: avgDaysBetween[0] ? Math.round(avgDaysBetween[0].avgDays * 10) / 10 : 'N/A',
        recommendations: [
          'Schedule inventory restocking before peak days',
          'Increase staffing during peak hours',
          'Plan promotional campaigns around reorder cycles'
        ]
      },
      product_performance: {
        top_product: topProduct[0] ? topProduct[0]._id : 'N/A',
        top_department: topDepartment[0] ? topDepartment[0]._id : 'N/A',
        overall_reorder_rate: overallReorderRate[0] ? Math.round(overallReorderRate[0].reorderRate * 1000) / 10 : 'N/A',
        organic_products_pct: organicProductsPct[0] ? Math.round(organicProductsPct[0].organicPct * 1000) / 10 : 'N/A',
        recommendations: [
          'Ensure high stock levels for top products',
          'Focus on departments with high reorder rates',
          'Expand organic product offerings'
        ]
      },
      customer_behavior: {
        avg_orders_per_customer: avgOrdersPerCustomer[0] ? Math.round(avgOrdersPerCustomer[0].avgOrders * 10) / 10 : 'N/A',
        high_loyalty_customers: highLoyaltyCustomers[0] ? highLoyaltyCustomers[0].highLoyaltyCount : 0,
        high_loyalty_percentage: totalCustomers > 0 ? Math.round((highLoyaltyCustomers[0]?.highLoyaltyCount || 0) / totalCustomers * 1000) / 10 : 0,
        avg_order_size: avgOrderSize[0] ? Math.round(avgOrderSize[0].avgSize * 10) / 10 : 'N/A',
        total_customers: totalCustomers,
        recommendations: [
          'Implement loyalty programs for repeat customers',
          'Create targeted promotions for high-value customers',
          'Optimize inventory for average order sizes'
        ]
      }
    };

    res.json(insights);
  } catch (error) {
    console.error('Error fetching business insights:', error);
    res.status(500).json({ error: error.message });
  }
});

// Analytics: High Priority Products (Top reorder rates)
router.get('/analytics/high-priority-products', async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const highPriorityProducts = await InstacartOrderProduct.aggregate([
      {
        $lookup: {
          from: 'instacart_products',
          localField: 'product_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $group: {
          _id: '$product.product_name',
          product_id: { $first: '$product_id' },
          total_orders: { $sum: 1 },
          reorder_count: { $sum: '$reordered' }
        }
      },
      {
        $match: { total_orders: { $gte: 100 } } // Minimum order threshold
      },
      {
        $project: {
          product_name: '$_id',
          product_id: 1,
          total_orders: 1,
          reorder_count: 1,
          reorder_rate: { $divide: ['$reorder_count', '$total_orders'] }
        }
      },
      { $sort: { reorder_rate: -1 } },
      { $limit: parseInt(limit) }
    ]);

    res.json(highPriorityProducts);
  } catch (error) {
    console.error('Error fetching high priority products:', error);
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Customer Segmentation
router.get('/analytics/customer-segmentation', async (req, res) => {
  try {
    const customerSegments = await InstacartOrder.aggregate([
      {
        $group: {
          _id: '$user_id',
          total_orders: { $sum: 1 },
          avg_days_between_orders: { $avg: '$days_since_prior_order' }
        }
      },
      {
        $bucket: {
          groupBy: '$total_orders',
          boundaries: [1, 5, 10, 20, 50, 100],
          default: '100+',
          output: {
            customer_count: { $sum: 1 },
            avg_orders: { $avg: '$total_orders' },
            avg_days_between: { $avg: '$avg_days_between_orders' }
          }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Add segment labels
    const segmentLabels = {
      1: 'New Customers (1-4 orders)',
      5: 'Regular Customers (5-9 orders)',
      10: 'Loyal Customers (10-19 orders)',
      20: 'Very Loyal (20-49 orders)',
      50: 'Super Loyal (50-99 orders)',
      '100+': 'VIP Customers (100+ orders)'
    };

    const labeledSegments = customerSegments.map(segment => ({
      ...segment,
      segment_label: segmentLabels[segment._id] || `${segment._id}+ orders`,
      order_range: segment._id
    }));

    res.json(labeledSegments);
  } catch (error) {
    console.error('Error fetching customer segmentation:', error);
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Reorder Analysis by Product Category
router.get('/analytics/reorder-analysis', async (req, res) => {
  try {
    const reorderAnalysis = await InstacartOrderProduct.aggregate([
      {
        $lookup: {
          from: 'instacart_products',
          localField: 'product_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'instacart_departments',
          localField: 'product.department_id',
          foreignField: 'department_id',
          as: 'department'
        }
      },
      { $unwind: '$department' },
      {
        $group: {
          _id: '$department.department',
          department_id: { $first: '$department.department_id' },
          total_orders: { $sum: 1 },
          reorder_count: { $sum: '$reordered' },
          unique_products: { $addToSet: '$product_id' }
        }
      },
      {
        $project: {
          department_name: '$_id',
          department_id: 1,
          total_orders: 1,
          reorder_count: 1,
          unique_products_count: { $size: '$unique_products' },
          reorder_rate: { $divide: ['$reorder_count', '$total_orders'] }
        }
      },
      { $sort: { reorder_rate: -1 } }
    ]);

    res.json(reorderAnalysis);
  } catch (error) {
    console.error('Error fetching reorder analysis:', error);
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Temporal Patterns - Detailed
router.get('/analytics/temporal-patterns', async (req, res) => {
  try {
    const [hourlyPatterns, dailyPatterns, reorderCycles] = await Promise.all([
      // Hourly patterns with more detail
      InstacartOrder.aggregate([
        {
          $group: {
            _id: '$order_hour_of_day',
            order_count: { $sum: 1 },
            unique_customers: { $addToSet: '$user_id' }
          }
        },
        {
          $project: {
            hour: '$_id',
            order_count: 1,
            unique_customers_count: { $size: '$unique_customers' },
            avg_orders_per_customer: { $divide: ['$order_count', { $size: '$unique_customers' }] }
          }
        },
        { $sort: { hour: 1 } }
      ]),

      // Daily patterns with customer behavior
      InstacartOrder.aggregate([
        {
          $group: {
            _id: '$order_dow',
            order_count: { $sum: 1 },
            unique_customers: { $addToSet: '$user_id' }
          }
        },
        {
          $project: {
            day_of_week: '$_id',
            order_count: 1,
            unique_customers_count: { $size: '$unique_customers' },
            avg_orders_per_customer: { $divide: ['$order_count', { $size: '$unique_customers' }] }
          }
        },
        { $sort: { day_of_week: 1 } }
      ]),

      // Reorder cycle analysis
      InstacartOrder.aggregate([
        { $match: { days_since_prior_order: { $ne: null, $lte: 60 } } },
        {
          $bucket: {
            groupBy: '$days_since_prior_order',
            boundaries: [0, 7, 14, 21, 30, 45, 60],
            default: '60+',
            output: {
              order_count: { $sum: 1 },
              unique_customers: { $addToSet: '$user_id' }
            }
          }
        },
        {
          $project: {
            days_range: '$_id',
            order_count: 1,
            unique_customers_count: { $size: '$unique_customers' }
          }
        }
      ])
    ]);

    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const enhancedDailyPatterns = dailyPatterns.map(day => ({
      ...day,
      day_name: dayNames[day.day_of_week]
    }));

    res.json({
      hourly_patterns: hourlyPatterns,
      daily_patterns: enhancedDailyPatterns,
      reorder_cycles: reorderCycles
    });
  } catch (error) {
    console.error('Error fetching temporal patterns:', error);
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Association Rules Analysis
router.get('/analytics/association-rules', async (req, res) => {
  try {
    const { min_support = 0.01, min_confidence = 0.3, limit = 20 } = req.query;

    // Get frequent product pairs
    const frequentPairs = await InstacartOrderProduct.aggregate([
      {
        $lookup: {
          from: 'instacartorders',
          localField: 'order_id',
          foreignField: 'order_id',
          as: 'order'
        }
      },
      { $unwind: '$order' },
      {
        $lookup: {
          from: 'instacartproducts',
          localField: 'product_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $group: {
          _id: '$order_id',
          products: {
            $push: {
              product_id: '$product_id',
              product_name: '$product.product_name'
            }
          }
        }
      },
      {
        $project: {
          order_id: '$_id',
          product_pairs: {
            $reduce: {
              input: { $range: [0, { $size: '$products' }] },
              initialValue: [],
              in: {
                $concatArrays: [
                  '$$value',
                  {
                    $map: {
                      input: { $range: [{ $add: ['$$this', 1] }, { $size: '$products' }] },
                      as: 'j',
                      in: {
                        antecedent: { $arrayElemAt: ['$products', '$$this'] },
                        consequent: { $arrayElemAt: ['$products', '$$j'] }
                      }
                    }
                  }
                ]
              }
            }
          }
        }
      },
      { $unwind: '$product_pairs' },
      {
        $group: {
          _id: {
            antecedent: '$product_pairs.antecedent.product_name',
            consequent: '$product_pairs.consequent.product_name'
          },
          support_count: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'instacartorders',
          pipeline: [{ $group: { _id: null, total_orders: { $sum: 1 } } }],
          as: 'total_orders'
        }
      },
      {
        $project: {
          antecedent: '$_id.antecedent',
          consequent: '$_id.consequent',
          support_count: 1,
          support: {
            $divide: ['$support_count', { $arrayElemAt: ['$total_orders.total_orders', 0] }]
          }
        }
      },
      { $match: { support: { $gte: parseFloat(min_support) } } },
      { $sort: { support: -1 } },
      { $limit: parseInt(limit) }
    ]);

    res.json(frequentPairs);
  } catch (error) {
    console.error('Error fetching association rules:', error);
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Market Basket Insights
router.get('/analytics/market-basket-insights', async (req, res) => {
  try {
    const [
      basketSizeStats,
      topProductCombinations,
      categoryAssociations,
      reorderPatterns
    ] = await Promise.all([
      // Basket size analysis
      InstacartOrderProduct.aggregate([
        {
          $group: {
            _id: '$order_id',
            basket_size: { $sum: 1 }
          }
        },
        {
          $group: {
            _id: null,
            avg_basket_size: { $avg: '$basket_size' },
            min_basket_size: { $min: '$basket_size' },
            max_basket_size: { $max: '$basket_size' },
            total_baskets: { $sum: 1 }
          }
        }
      ]),

      // Top product combinations (2-item baskets)
      InstacartOrderProduct.aggregate([
        {
          $lookup: {
            from: 'instacartproducts',
            localField: 'product_id',
            foreignField: 'product_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        {
          $group: {
            _id: '$order_id',
            products: {
              $push: {
                product_id: '$product_id',
                product_name: '$product.product_name'
              }
            }
          }
        },
        { $match: { 'products.1': { $exists: true } } },
        {
          $project: {
            combinations: {
              $map: {
                input: { $range: [0, { $subtract: [{ $size: '$products' }, 1] }] },
                as: 'i',
                in: {
                  $map: {
                    input: { $range: [{ $add: ['$$i', 1] }, { $size: '$products' }] },
                    as: 'j',
                    in: {
                      product1: { $arrayElemAt: ['$products.product_name', '$$i'] },
                      product2: { $arrayElemAt: ['$products.product_name', '$$j'] }
                    }
                  }
                }
              }
            }
          }
        },
        { $unwind: '$combinations' },
        { $unwind: '$combinations' },
        {
          $group: {
            _id: {
              product1: '$combinations.product1',
              product2: '$combinations.product2'
            },
            frequency: { $sum: 1 }
          }
        },
        { $sort: { frequency: -1 } },
        { $limit: 15 }
      ]),

      // Category associations
      InstacartOrderProduct.aggregate([
        {
          $lookup: {
            from: 'instacartproducts',
            localField: 'product_id',
            foreignField: 'product_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        {
          $lookup: {
            from: 'instacartdepartments',
            localField: 'product.department_id',
            foreignField: 'department_id',
            as: 'department'
          }
        },
        { $unwind: '$department' },
        {
          $group: {
            _id: '$order_id',
            departments: { $addToSet: '$department.department' }
          }
        },
        {
          $project: {
            department_pairs: {
              $reduce: {
                input: { $range: [0, { $size: '$departments' }] },
                initialValue: [],
                in: {
                  $concatArrays: [
                    '$$value',
                    {
                      $map: {
                        input: { $range: [{ $add: ['$$this', 1] }, { $size: '$departments' }] },
                        as: 'j',
                        in: {
                          dept1: { $arrayElemAt: ['$departments', '$$this'] },
                          dept2: { $arrayElemAt: ['$departments', '$$j'] }
                        }
                      }
                    }
                  ]
                }
              }
            }
          }
        },
        { $unwind: '$department_pairs' },
        {
          $group: {
            _id: {
              dept1: '$department_pairs.dept1',
              dept2: '$department_pairs.dept2'
            },
            frequency: { $sum: 1 }
          }
        },
        { $sort: { frequency: -1 } },
        { $limit: 10 }
      ]),

      // Reorder patterns in baskets
      InstacartOrderProduct.aggregate([
        {
          $group: {
            _id: '$order_id',
            total_items: { $sum: 1 },
            reordered_items: { $sum: { $cond: [{ $eq: ['$reordered', 1] }, 1, 0] } }
          }
        },
        {
          $project: {
            total_items: 1,
            reordered_items: 1,
            reorder_ratio: { $divide: ['$reordered_items', '$total_items'] }
          }
        },
        {
          $bucket: {
            groupBy: '$reorder_ratio',
            boundaries: [0, 0.25, 0.5, 0.75, 1.0],
            default: 'other',
            output: {
              order_count: { $sum: 1 },
              avg_basket_size: { $avg: '$total_items' }
            }
          }
        }
      ])
    ]);

    res.json({
      basket_size_stats: basketSizeStats[0] || {},
      top_product_combinations: topProductCombinations,
      category_associations: categoryAssociations,
      reorder_patterns: reorderPatterns
    });
  } catch (error) {
    console.error('Error fetching market basket insights:', error);
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Advanced Customer Behavior Analysis
router.get('/analytics/customer-behavior-advanced', async (req, res) => {
  try {
    const [
      customerLifecycle,
      loyaltySegments,
      purchasePatterns,
      customerValue
    ] = await Promise.all([
      // Customer lifecycle analysis
      InstacartOrder.aggregate([
        {
          $group: {
            _id: '$user_id',
            first_order: { $min: '$order_number' },
            last_order: { $max: '$order_number' },
            total_orders: { $sum: 1 },
            avg_days_between: { $avg: '$days_since_prior_order' }
          }
        },
        {
          $project: {
            customer_lifetime: { $subtract: ['$last_order', '$first_order'] },
            total_orders: 1,
            avg_days_between: 1,
            order_frequency: { $divide: ['$total_orders', { $add: ['$customer_lifetime', 1] }] }
          }
        },
        {
          $bucket: {
            groupBy: '$customer_lifetime',
            boundaries: [0, 5, 15, 30, 50, 100],
            default: '100+',
            output: {
              customer_count: { $sum: 1 },
              avg_orders: { $avg: '$total_orders' },
              avg_frequency: { $avg: '$order_frequency' }
            }
          }
        }
      ]),

      // Loyalty segments with RFM-like analysis
      InstacartOrder.aggregate([
        {
          $group: {
            _id: '$user_id',
            recency: { $max: '$order_number' },
            frequency: { $sum: 1 },
            monetary: { $sum: 1 } // Using order count as proxy for monetary value
          }
        },
        {
          $project: {
            recency_score: {
              $switch: {
                branches: [
                  { case: { $gte: ['$recency', 50] }, then: 5 },
                  { case: { $gte: ['$recency', 30] }, then: 4 },
                  { case: { $gte: ['$recency', 15] }, then: 3 },
                  { case: { $gte: ['$recency', 5] }, then: 2 }
                ],
                default: 1
              }
            },
            frequency_score: {
              $switch: {
                branches: [
                  { case: { $gte: ['$frequency', 50] }, then: 5 },
                  { case: { $gte: ['$frequency', 20] }, then: 4 },
                  { case: { $gte: ['$frequency', 10] }, then: 3 },
                  { case: { $gte: ['$frequency', 5] }, then: 2 }
                ],
                default: 1
              }
            },
            frequency: 1,
            recency: 1
          }
        },
        {
          $project: {
            rfm_score: { $add: ['$recency_score', '$frequency_score'] },
            segment: {
              $switch: {
                branches: [
                  { case: { $gte: [{ $add: ['$recency_score', '$frequency_score'] }, 8] }, then: 'Champions' },
                  { case: { $gte: [{ $add: ['$recency_score', '$frequency_score'] }, 6] }, then: 'Loyal Customers' },
                  { case: { $gte: [{ $add: ['$recency_score', '$frequency_score'] }, 4] }, then: 'Potential Loyalists' },
                  { case: { $gte: [{ $add: ['$recency_score', '$frequency_score'] }, 3] }, then: 'At Risk' }
                ],
                default: 'Lost Customers'
              }
            },
            frequency: 1,
            recency: 1
          }
        },
        {
          $group: {
            _id: '$segment',
            customer_count: { $sum: 1 },
            avg_frequency: { $avg: '$frequency' },
            avg_recency: { $avg: '$recency' }
          }
        },
        { $sort: { avg_frequency: -1 } }
      ]),

      // Purchase patterns by time
      InstacartOrder.aggregate([
        {
          $group: {
            _id: {
              hour: '$order_hour_of_day',
              dow: '$order_dow'
            },
            order_count: { $sum: 1 },
            unique_customers: { $addToSet: '$user_id' }
          }
        },
        {
          $project: {
            hour: '$_id.hour',
            dow: '$_id.dow',
            order_count: 1,
            unique_customers_count: { $size: '$unique_customers' },
            orders_per_customer: { $divide: ['$order_count', { $size: '$unique_customers' }] }
          }
        },
        { $sort: { dow: 1, hour: 1 } }
      ]),

      // Customer value distribution
      InstacartOrderProduct.aggregate([
        {
          $lookup: {
            from: 'instacartorders',
            localField: 'order_id',
            foreignField: 'order_id',
            as: 'order'
          }
        },
        { $unwind: '$order' },
        {
          $group: {
            _id: '$order.user_id',
            total_products: { $sum: 1 },
            unique_products: { $addToSet: '$product_id' },
            total_orders: { $addToSet: '$order_id' },
            reorder_count: { $sum: { $cond: [{ $eq: ['$reordered', 1] }, 1, 0] } }
          }
        },
        {
          $project: {
            total_products: 1,
            unique_products_count: { $size: '$unique_products' },
            total_orders_count: { $size: '$total_orders' },
            reorder_rate: { $divide: ['$reorder_count', '$total_products'] },
            avg_products_per_order: { $divide: ['$total_products', { $size: '$total_orders' }] },
            customer_value: {
              $multiply: [
                { $size: '$total_orders' },
                { $divide: ['$total_products', { $size: '$total_orders' }] }
              ]
            }
          }
        },
        {
          $bucket: {
            groupBy: '$customer_value',
            boundaries: [0, 50, 100, 200, 500, 1000],
            default: '1000+',
            output: {
              customer_count: { $sum: 1 },
              avg_orders: { $avg: '$total_orders_count' },
              avg_products_per_order: { $avg: '$avg_products_per_order' },
              avg_reorder_rate: { $avg: '$reorder_rate' }
            }
          }
        }
      ])
    ]);

    res.json({
      customer_lifecycle: customerLifecycle,
      loyalty_segments: loyaltySegments,
      purchase_patterns: purchasePatterns.slice(0, 50), // Limit for performance
      customer_value_distribution: customerValue
    });
  } catch (error) {
    console.error('Error fetching advanced customer behavior:', error);
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Product Affinity Analysis
router.get('/analytics/product-affinity', async (req, res) => {
  try {
    const { product_id } = req.query;

    if (!product_id) {
      return res.status(400).json({ error: 'product_id parameter is required' });
    }

    const affinityAnalysis = await InstacartOrderProduct.aggregate([
      // Find orders containing the target product
      { $match: { product_id: parseInt(product_id) } },
      {
        $lookup: {
          from: 'instacartorderproducts',
          localField: 'order_id',
          foreignField: 'order_id',
          as: 'basket_items'
        }
      },
      { $unwind: '$basket_items' },
      { $match: { 'basket_items.product_id': { $ne: parseInt(product_id) } } },
      {
        $lookup: {
          from: 'instacartproducts',
          localField: 'basket_items.product_id',
          foreignField: 'product_id',
          as: 'related_product'
        }
      },
      { $unwind: '$related_product' },
      {
        $group: {
          _id: '$basket_items.product_id',
          product_name: { $first: '$related_product.product_name' },
          co_occurrence_count: { $sum: 1 },
          orders_with_both: { $addToSet: '$order_id' }
        }
      },
      {
        $project: {
          product_id: '$_id',
          product_name: 1,
          co_occurrence_count: 1,
          unique_orders_count: { $size: '$orders_with_both' }
        }
      },
      { $sort: { co_occurrence_count: -1 } },
      { $limit: 20 }
    ]);

    res.json(affinityAnalysis);
  } catch (error) {
    console.error('Error fetching product affinity:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
