const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function verifyForecasts() {
  try {
    console.log('🔍 Verifying forecast system...');
    
    // Login to get token
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Authentication successful');

    // Check dashboard summary
    const summaryResponse = await axios.get(`${API_BASE}/predictive/dashboard/summary`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('📊 Dashboard Summary:');
    console.log(`  Active Forecasts: ${summaryResponse.data.active_forecasts || 0}`);
    console.log(`  Pending Suggestions: ${summaryResponse.data.pending_suggestions || 0}`);
    console.log(`  High Priority Alerts: ${summaryResponse.data.high_priority_alerts || 0}`);

    // Check if forecasts exist
    const forecastsResponse = await axios.get(`${API_BASE}/predictive/forecasts?limit=5`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`\n📈 Recent Forecasts: ${forecastsResponse.data.forecasts?.length || 0} found`);
    
    if (forecastsResponse.data.forecasts && forecastsResponse.data.forecasts.length > 0) {
      forecastsResponse.data.forecasts.slice(0, 3).forEach((forecast, index) => {
        console.log(`  ${index + 1}. ${forecast.product_name} (${forecast.model_type})`);
      });
    }

    // Check reorder suggestions
    const suggestionsResponse = await axios.get(`${API_BASE}/predictive/reorder-suggestions?limit=3`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`\n🔄 Reorder Suggestions: ${suggestionsResponse.data.suggestions?.length || 0} found`);
    
    if (suggestionsResponse.data.suggestions && suggestionsResponse.data.suggestions.length > 0) {
      suggestionsResponse.data.suggestions.slice(0, 3).forEach((suggestion, index) => {
        console.log(`  ${index + 1}. ${suggestion.product_name} - Priority: ${suggestion.priority}`);
      });
    }

    console.log('\n🎉 Forecast system verification completed successfully!');
    console.log('✅ The "Failed to generate forecasts" error should now be resolved.');
    console.log('🌐 You can now use the Generate Forecasts button in the web interface.');
    
    return true;
  } catch (error) {
    console.error('❌ Verification failed:', error.response?.data?.error || error.message);
    return false;
  }
}

// Run verification
verifyForecasts().then(success => {
  process.exit(success ? 0 : 1);
});
