import React, { useState } from 'react';
import { X, Download, FileText, FileSpreadsheet, Calendar, Filter } from 'lucide-react';
import { reportsAPI } from '../../services/api';
import toast from 'react-hot-toast';

const ExportModal = ({ isOpen, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [exportData, setExportData] = useState({
    reportType: 'inventory',
    format: 'csv',
    dateRange: 'last_30_days',
    customStartDate: '',
    customEndDate: '',
    includeFields: {
      inventory: ['sku', 'name', 'category', 'quantity', 'price', 'supplier'],
      orders: ['orderNumber', 'customer', 'date', 'status', 'total'],
      suppliers: ['name', 'contact', 'categories', 'performance'],
      sales: ['date', 'product', 'quantity', 'revenue', 'profit']
    },
    filters: {
      category: '',
      supplier: '',
      status: '',
      minValue: '',
      maxValue: ''
    }
  });

  const reportTypes = [
    { value: 'inventory', label: 'Inventory Report', icon: FileText },
    { value: 'orders', label: 'Orders Report', icon: FileSpreadsheet },
    { value: 'suppliers', label: 'Suppliers Report', icon: FileText },
    { value: 'sales', label: 'Sales Report', icon: FileSpreadsheet },
    { value: 'low_stock', label: 'Low Stock Report', icon: FileText },
    { value: 'profit_analysis', label: 'Profit Analysis', icon: FileSpreadsheet }
  ];

  const formats = [
    { value: 'csv', label: 'CSV', description: 'Comma-separated values' },
    { value: 'excel', label: 'Excel', description: 'Microsoft Excel format' },
    { value: 'pdf', label: 'PDF', description: 'Portable Document Format' },
    { value: 'json', label: 'JSON', description: 'JavaScript Object Notation' }
  ];

  const dateRanges = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'last_7_days', label: 'Last 7 Days' },
    { value: 'last_30_days', label: 'Last 30 Days' },
    { value: 'last_90_days', label: 'Last 90 Days' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'this_year', label: 'This Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    if (name.startsWith('filters.')) {
      const filterField = name.split('.')[1];
      setExportData(prev => ({
        ...prev,
        filters: {
          ...prev.filters,
          [filterField]: value
        }
      }));
    } else {
      setExportData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleFieldToggle = (field) => {
    const reportType = exportData.reportType;
    setExportData(prev => ({
      ...prev,
      includeFields: {
        ...prev.includeFields,
        [reportType]: prev.includeFields[reportType].includes(field)
          ? prev.includeFields[reportType].filter(f => f !== field)
          : [...prev.includeFields[reportType], field]
      }
    }));
  };

  const handleExport = async () => {
    setLoading(true);
    try {
      // Prepare export parameters
      const params = {
        type: exportData.reportType,
        format: exportData.format,
        dateRange: exportData.dateRange,
        fields: exportData.includeFields[exportData.reportType],
        filters: exportData.filters
      };

      // Add custom date range if selected
      if (exportData.dateRange === 'custom') {
        params.startDate = exportData.customStartDate;
        params.endDate = exportData.customEndDate;
      }

      // Call export API
      const response = await reportsAPI.export(params);
      
      // Handle file download
      if (response.data.downloadUrl) {
        // If API returns a download URL
        window.open(response.data.downloadUrl, '_blank');
      } else {
        // If API returns file data directly
        const blob = new Blob([response.data], { 
          type: exportData.format === 'csv' ? 'text/csv' : 
                exportData.format === 'excel' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' :
                exportData.format === 'pdf' ? 'application/pdf' : 'application/json'
        });
        
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${exportData.reportType}_report.${exportData.format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }

      toast.success('Report exported successfully');
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export report');
    } finally {
      setLoading(false);
    }
  };

  const getFieldOptions = () => {
    const fieldOptions = {
      inventory: [
        { value: 'sku', label: 'SKU' },
        { value: 'name', label: 'Product Name' },
        { value: 'category', label: 'Category' },
        { value: 'quantity', label: 'Quantity' },
        { value: 'price', label: 'Price' },
        { value: 'cost', label: 'Cost' },
        { value: 'supplier', label: 'Supplier' },
        { value: 'location', label: 'Location' },
        { value: 'minStock', label: 'Min Stock' },
        { value: 'reorderPoint', label: 'Reorder Point' }
      ],
      orders: [
        { value: 'orderNumber', label: 'Order Number' },
        { value: 'customer', label: 'Customer' },
        { value: 'date', label: 'Order Date' },
        { value: 'status', label: 'Status' },
        { value: 'total', label: 'Total Amount' },
        { value: 'items', label: 'Items' },
        { value: 'paymentMethod', label: 'Payment Method' },
        { value: 'shippingMethod', label: 'Shipping Method' }
      ],
      suppliers: [
        { value: 'name', label: 'Company Name' },
        { value: 'contact', label: 'Contact Person' },
        { value: 'email', label: 'Email' },
        { value: 'phone', label: 'Phone' },
        { value: 'categories', label: 'Categories' },
        { value: 'paymentTerms', label: 'Payment Terms' },
        { value: 'performance', label: 'Performance Rating' },
        { value: 'address', label: 'Address' }
      ],
      sales: [
        { value: 'date', label: 'Sale Date' },
        { value: 'product', label: 'Product' },
        { value: 'quantity', label: 'Quantity Sold' },
        { value: 'revenue', label: 'Revenue' },
        { value: 'profit', label: 'Profit' },
        { value: 'customer', label: 'Customer' },
        { value: 'margin', label: 'Profit Margin' }
      ]
    };

    return fieldOptions[exportData.reportType] || [];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Download className="h-6 w-6 text-primary-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">Export Report</h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
                disabled={loading}
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Report Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Report Type
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {reportTypes.map(type => {
                    const Icon = type.icon;
                    return (
                      <label key={type.value} className="relative">
                        <input
                          type="radio"
                          name="reportType"
                          value={type.value}
                          checked={exportData.reportType === type.value}
                          onChange={handleChange}
                          className="sr-only"
                          disabled={loading}
                        />
                        <div className={`p-3 border-2 rounded-lg cursor-pointer transition-colors ${
                          exportData.reportType === type.value
                            ? 'border-primary-500 bg-primary-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}>
                          <Icon className="h-5 w-5 mb-2 text-gray-600" />
                          <div className="text-sm font-medium text-gray-900">{type.label}</div>
                        </div>
                      </label>
                    );
                  })}
                </div>
              </div>

              {/* Format */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Export Format
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {formats.map(format => (
                    <label key={format.value} className="relative">
                      <input
                        type="radio"
                        name="format"
                        value={format.value}
                        checked={exportData.format === format.value}
                        onChange={handleChange}
                        className="sr-only"
                        disabled={loading}
                      />
                      <div className={`p-3 border-2 rounded-lg cursor-pointer transition-colors ${
                        exportData.format === format.value
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}>
                        <div className="text-sm font-medium text-gray-900">{format.label}</div>
                        <div className="text-xs text-gray-500">{format.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Date Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Date Range
                </label>
                <select
                  name="dateRange"
                  value={exportData.dateRange}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                  disabled={loading}
                >
                  {dateRanges.map(range => (
                    <option key={range.value} value={range.value}>{range.label}</option>
                  ))}
                </select>

                {exportData.dateRange === 'custom' && (
                  <div className="grid grid-cols-2 gap-4 mt-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Start Date
                      </label>
                      <input
                        type="date"
                        name="customStartDate"
                        value={exportData.customStartDate}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                        disabled={loading}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        End Date
                      </label>
                      <input
                        type="date"
                        name="customEndDate"
                        value={exportData.customEndDate}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                        disabled={loading}
                      />
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  onClick={handleExport}
                  className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
                  disabled={loading}
                >
                  <Download className="h-4 w-4 mr-2" />
                  {loading ? 'Exporting...' : 'Export Report'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExportModal;
