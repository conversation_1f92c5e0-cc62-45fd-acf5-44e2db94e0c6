@echo off
title Python Environment Setup for Demand Forecasting
color 0B

echo.
echo ========================================
echo   🐍 PYTHON ENVIRONMENT SETUP 🐍
echo ========================================
echo.

echo 📋 Setting up Python environment for demand forecasting...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python is available
python --version

REM Check if pip is available
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)

echo ✅ pip is available
echo.

echo 📦 Installing Python dependencies...
echo.

REM Upgrade pip first
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install core dependencies
echo Installing core data science libraries...
pip install numpy>=1.21.0
pip install pandas>=1.3.0
pip install matplotlib>=3.4.0
pip install seaborn>=0.11.0

REM Install Prophet (this might take a while)
echo.
echo 🔮 Installing Prophet (this may take several minutes)...
echo Please be patient, Prophet has many dependencies...
pip install prophet>=1.1.0

if %errorlevel% neq 0 (
    echo.
    echo ⚠️ Prophet installation failed. Trying alternative method...
    echo.
    
    REM Try installing Prophet dependencies first
    pip install pystan>=3.0
    pip install prophet
    
    if %errorlevel% neq 0 (
        echo ❌ Prophet installation failed completely
        echo.
        echo Troubleshooting steps:
        echo 1. Make sure you have Visual Studio Build Tools installed
        echo 2. Try: pip install --upgrade setuptools wheel
        echo 3. Try: conda install -c conda-forge prophet (if using Anaconda)
        echo.
        pause
        exit /b 1
    )
)

REM Install scikit-learn
echo Installing scikit-learn...
pip install scikit-learn>=1.0.0

REM Install visualization libraries
echo Installing visualization libraries...
pip install plotly>=5.0.0

REM Install utility libraries
echo Installing utility libraries...
pip install python-dateutil>=2.8.0
pip install pytz>=2021.1
pip install requests>=2.25.0
pip install scipy>=1.7.0
pip install statsmodels>=0.12.0

echo.
echo 🧪 Testing Prophet installation...

REM Test Prophet import
python -c "import prophet; print('✅ Prophet imported successfully')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Prophet import test failed
    echo Please check the installation and try again
    pause
    exit /b 1
)

REM Test other key libraries
python -c "import pandas, numpy, matplotlib, seaborn, sklearn; print('✅ All core libraries imported successfully')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Core libraries import test failed
    pause
    exit /b 1
)

echo.
echo 🔬 Running Prophet functionality test...

REM Create a simple test script
echo import pandas as pd > test_prophet.py
echo import numpy as np >> test_prophet.py
echo from prophet import Prophet >> test_prophet.py
echo from datetime import datetime, timedelta >> test_prophet.py
echo. >> test_prophet.py
echo # Create sample data >> test_prophet.py
echo dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D') >> test_prophet.py
echo values = np.random.randint(10, 100, len(dates)) >> test_prophet.py
echo df = pd.DataFrame({'ds': dates, 'y': values}) >> test_prophet.py
echo. >> test_prophet.py
echo # Create and fit Prophet model >> test_prophet.py
echo model = Prophet() >> test_prophet.py
echo model.fit(df) >> test_prophet.py
echo. >> test_prophet.py
echo # Make future predictions >> test_prophet.py
echo future = model.make_future_dataframe(periods=30) >> test_prophet.py
echo forecast = model.predict(future) >> test_prophet.py
echo. >> test_prophet.py
echo print('✅ Prophet forecasting test completed successfully') >> test_prophet.py
echo print(f'Generated forecast for {len(forecast)} data points') >> test_prophet.py

REM Run the test
python test_prophet.py
if %errorlevel% neq 0 (
    echo ❌ Prophet functionality test failed
    del test_prophet.py 2>nul
    pause
    exit /b 1
)

REM Clean up test file
del test_prophet.py 2>nul

echo.
echo 📊 Testing integration with inventory system...

REM Test if the main forecasting script exists and runs
if exist module2_prophet_forecasting.py (
    echo ✅ Main forecasting script found
    python -c "import module2_prophet_forecasting; print('✅ Forecasting module can be imported')" 2>nul
    if %errorlevel% neq 0 (
        echo ⚠️ Warning: Forecasting module has import issues
        echo This might affect demand forecasting functionality
    )
) else (
    echo ⚠️ Warning: Main forecasting script not found
    echo Demand forecasting might not work properly
)

echo.
echo ========================================
echo   ✅ PYTHON SETUP COMPLETE!
echo ========================================
echo.
echo 🎉 Python environment is ready for demand forecasting!
echo.
echo 📋 Installed packages:
echo    • Prophet (Facebook's time series forecasting)
echo    • NumPy, Pandas (data manipulation)
echo    • Matplotlib, Seaborn, Plotly (visualization)
echo    • Scikit-learn (machine learning)
echo    • SciPy, Statsmodels (statistical analysis)
echo.
echo 🔧 Next steps:
echo    1. Start your backend server: cd backend ^&^& npm run dev
echo    2. Start your frontend: npm start
echo    3. Test demand forecasting in the web interface
echo.
echo 🧪 To test the complete system, run:
echo    node test-demand-forecast.js
echo.
pause
