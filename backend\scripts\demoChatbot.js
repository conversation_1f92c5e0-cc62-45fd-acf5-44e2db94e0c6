require('dotenv').config();
const ChatbotService = require('../services/chatbotService');

async function demonstrateChatbot() {
  console.log('🤖 Enhanced Chatbot Demonstration\n');
  console.log('=' .repeat(50));
  
  const chatbotService = new ChatbotService();
  const sessionId = `demo_${Date.now()}`;
  
  // Test queries to demonstrate different capabilities
  const testQueries = [
    {
      category: "General AI Question",
      query: "How does artificial intelligence work?",
      description: "Testing general knowledge with inventory redirection"
    },
    {
      category: "Inventory Management",
      query: "What should I consider when managing inventory levels?",
      description: "Testing AI-powered inventory advice"
    },
    {
      category: "Product Search",
      query: "Find products similar to coffee",
      description: "Testing enhanced product search with AI insights"
    },
    {
      category: "Business Advice",
      query: "How can I optimize my supply chain?",
      description: "Testing business consultation capabilities"
    },
    {
      category: "Data Analysis",
      query: "What are the trends in organic products?",
      description: "Testing analytical capabilities"
    }
  ];

  for (let i = 0; i < testQueries.length; i++) {
    const test = testQueries[i];
    
    console.log(`\n${i + 1}. ${test.category}`);
    console.log('-'.repeat(30));
    console.log(`Query: "${test.query}"`);
    console.log(`Purpose: ${test.description}\n`);
    
    try {
      const response = await chatbotService.processQuery(
        test.query,
        sessionId,
        'demo_user'
      );
      
      console.log('✅ Response Generated:');
      console.log(`Source: ${response.source || 'Database'}`);
      console.log(`Confidence: ${Math.round(response.confidence * 100)}%`);
      console.log(`Processing Time: ${response.processing_time}ms`);
      console.log('\nResponse:');
      console.log(response.response.substring(0, 300) + '...\n');
      
      if (response.response_data) {
        console.log('📊 Additional Data Available:');
        if (response.response_data.amazon_products) {
          console.log(`- Amazon Products: ${response.response_data.amazon_products.length}`);
        }
        if (response.response_data.inventory_products) {
          console.log(`- Inventory Products: ${response.response_data.inventory_products.length}`);
        }
        if (response.response_data.recommendations) {
          console.log(`- Recommendations: ${response.response_data.recommendations.length}`);
        }
      }
      
    } catch (error) {
      console.log('❌ Error:', error.message);
    }
    
    console.log('=' .repeat(50));
    
    // Add a small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🎉 Demonstration completed!');
  console.log('\nKey Features Demonstrated:');
  console.log('✅ AI-Enhanced Responses (when Gemini is available)');
  console.log('✅ Intelligent Context Understanding');
  console.log('✅ Business-Focused Recommendations');
  console.log('✅ Graceful Fallback to Database Responses');
  console.log('✅ Multi-Source Data Integration');
  console.log('✅ Confidence Scoring');
  console.log('✅ Performance Monitoring');
  
  console.log('\nTo test the chatbot interactively:');
  console.log('1. Start the backend server: npm run dev');
  console.log('2. Start the frontend: npm start');
  console.log('3. Navigate to the AI Chatbot page');
  console.log('4. Try asking various questions!');
}

// Run the demonstration
if (require.main === module) {
  demonstrateChatbot().catch(console.error);
}

module.exports = demonstrateChatbot;
