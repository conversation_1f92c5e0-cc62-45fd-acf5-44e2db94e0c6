const mongoose = require('mongoose');
require('dotenv').config();
const ChatbotService = require('./services/chatbotService');

async function testEnhancedQueries() {
  console.log('🤖 Testing Enhanced Chatbot Database Access\n');
  
  try {
    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB connected successfully\n');

    const chatbot = new ChatbotService();
    const sessionId = 'test-enhanced-' + Date.now();
    const userId = 'test-user';

    console.log('🧪 Testing Enhanced Database Queries:');
    console.log('=' .repeat(60));

    // Test 1: Direct database count query
    console.log('\n1️⃣ Testing Database Count Query:');
    console.log('Query: "How many products are in the database?"');
    const result1 = await chatbot.processQuery(
      'How many products are in the database?', 
      sessionId, 
      userId
    );
    console.log('Response:', result1.response);
    console.log('Response Type:', result1.response_type);
    console.log('Confidence:', result1.confidence);
    console.log('Processing Time:', result1.processing_time + 'ms');

    // Test 2: Category analysis query
    console.log('\n2️⃣ Testing Category Analysis:');
    console.log('Query: "Show me product categories"');
    const result2 = await chatbot.processQuery(
      'Show me product categories', 
      sessionId, 
      userId
    );
    console.log('Response:', result2.response.substring(0, 300) + '...');
    console.log('Response Type:', result2.response_type);
    console.log('Confidence:', result2.confidence);

    // Test 3: Low stock analysis
    console.log('\n3️⃣ Testing Low Stock Analysis:');
    console.log('Query: "What items are low in stock?"');
    const result3 = await chatbot.processQuery(
      'What items are low in stock?', 
      sessionId, 
      userId
    );
    console.log('Response:', result3.response.substring(0, 300) + '...');
    console.log('Response Type:', result3.response_type);
    console.log('Confidence:', result3.confidence);

    // Test 4: General AI question with inventory context
    console.log('\n4️⃣ Testing AI-Enhanced Query:');
    console.log('Query: "How can I improve my inventory management?"');
    const result4 = await chatbot.processQuery(
      'How can I improve my inventory management?', 
      sessionId, 
      userId
    );
    console.log('Response:', result4.response.substring(0, 400) + '...');
    console.log('Response Type:', result4.response_type);
    console.log('Confidence:', result4.confidence);

    // Test 5: Comprehensive database overview
    console.log('\n5️⃣ Testing Comprehensive Overview:');
    console.log('Query: "Give me a complete database overview"');
    const result5 = await chatbot.processQuery(
      'Give me a complete database overview', 
      sessionId, 
      userId
    );
    console.log('Response:', result5.response.substring(0, 400) + '...');
    console.log('Response Type:', result5.response_type);
    console.log('Confidence:', result5.confidence);

    // Test 6: Specific product search with enhanced context
    console.log('\n6️⃣ Testing Enhanced Product Search:');
    console.log('Query: "Find products related to coffee"');
    const result6 = await chatbot.processQuery(
      'Find products related to coffee', 
      sessionId, 
      userId
    );
    console.log('Response:', result6.response.substring(0, 300) + '...');
    console.log('Response Type:', result6.response_type);
    console.log('Confidence:', result6.confidence);

    console.log('\n📊 Enhanced Chatbot Test Summary:');
    console.log('=' .repeat(60));
    console.log('✅ Database count queries working');
    console.log('✅ Category analysis working');
    console.log('✅ Low stock analysis working');
    console.log('✅ AI-enhanced responses working');
    console.log('✅ Comprehensive database access working');
    console.log('✅ Enhanced product search working');

    console.log('\n🎉 Enhanced chatbot testing completed successfully!');

  } catch (error) {
    console.error('❌ Enhanced chatbot test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n📡 Database connection closed.');
  }
}

// Run the test
testEnhancedQueries();
