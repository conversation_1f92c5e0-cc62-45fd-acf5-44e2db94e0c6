{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## XGBoost Model"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import gc\n", "pd.options.mode.chained_assignment = None\n", "\n", "import xgboost as xgb\n", "root = 'C:/Data/instacart-market-basket-analysis/'\n", "\n", "from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score\n", "from sklearn.metrics import confusion_matrix, accuracy_score, classification_report\n", "from sklearn.metrics import roc_auc_score, roc_curve, precision_score, recall_score, f1_score"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>...</th>\n", "      <th>total_reorders_by_user</th>\n", "      <th>reorder_propotion_by_user</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>orders_3</th>\n", "      <th>orders_2</th>\n", "      <th>orders_1</th>\n", "      <th>reorder_3</th>\n", "      <th>reorder_2</th>\n", "      <th>reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9.0</td>\n", "      <td>8.0</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 71 columns</p>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                          10.0   \n", "1        1       10258                           9.0   \n", "2        1       10326                           1.0   \n", "3        1       12427                          10.0   \n", "4        1       13032                           3.0   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                             9.0                         0.900000   \n", "1                             8.0                         0.888889   \n", "2                             0.0                         0.000000   \n", "3                             9.0                         0.900000   \n", "4                             2.0                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \\\n", "0                 1.400000                   17.600000             10.0   \n", "1                 3.333333                   19.555555             10.0   \n", "2                 5.000000                   28.000000              5.0   \n", "3                 3.300000                   17.600000             10.0   \n", "4                 6.333333                   21.666666             10.0   \n", "\n", "   is_reorder_3  is_reorder_2  ...  total_reorders_by_user  \\\n", "0           1.0           1.0  ...                    41.0   \n", "1           1.0           1.0  ...                    41.0   \n", "2           0.0           0.0  ...                    41.0   \n", "3           1.0           1.0  ...                    41.0   \n", "4           1.0           0.0  ...                    41.0   \n", "\n", "   reorder_propotion_by_user  average_order_size  reorder_in_order  orders_3  \\\n", "0                   0.694915                 5.9          0.705833         6   \n", "1                   0.694915                 5.9          0.705833         6   \n", "2                   0.694915                 5.9          0.705833         6   \n", "3                   0.694915                 5.9          0.705833         6   \n", "4                   0.694915                 5.9          0.705833         6   \n", "\n", "   orders_2  orders_1  reorder_3  reorder_2  reorder_1  \n", "0         6         9   0.666667        1.0   0.666667  \n", "1         6         9   0.666667        1.0   0.666667  \n", "2         6         9   0.666667        1.0   0.666667  \n", "3         6         9   0.666667        1.0   0.666667  \n", "4         6         9   0.666667        1.0   0.666667  \n", "\n", "[5 rows x 71 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_pickle(root + 'Finaldata.pkl')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def reduce_memory(df):\n", "    \n", "    \"\"\"\n", "    This function reduce the dataframe memory usage by converting it's type for easier handling.\n", "    \n", "    Parameters: Dataframe\n", "    Return: Dataframe\n", "    \"\"\"\n", "    \n", "    start_mem_usg = df.memory_usage().sum() / 1024**2 \n", "    print(\"Memory usage of properties dataframe is :\",start_mem_usg,\" MB\")\n", "    \n", "    for col in df.columns:\n", "        if df[col].dtypes in [\"int64\", \"int32\", \"int16\"]:\n", "            \n", "            cmin = df[col].min()\n", "            cmax = df[col].max()\n", "            \n", "            if cmin > np.iinfo(np.int8).min and cmax < np.iinfo(np.int8).max:\n", "                df[col] = df[col].astype(np.int8)\n", "            \n", "            elif cmin > np.iinfo(np.int16).min and cmax < np.iinfo(np.int16).max:\n", "                df[col] = df[col].astype(np.int16)\n", "            \n", "            elif cmin > np.iinfo(np.int32).min and cmax < np.iinfo(np.int32).max:\n", "                df[col] = df[col].astype(np.int32)\n", "        \n", "        if df[col].dtypes in [\"float64\", \"float32\"]:\n", "            \n", "            cmin = df[col].min()\n", "            cmax = df[col].max()\n", "            \n", "            if cmin > np.finfo(np.float16).min and cmax < np.finfo(np.float16).max:\n", "                df[col] = df[col].astype(np.float16)\n", "            \n", "            elif cmin > np.finfo(np.float32).min and cmax < np.finfo(np.float32).max:\n", "                df[col] = df[col].astype(np.float32)\n", "    \n", "    print(\"\")\n", "    print(\"___MEMORY USAGE AFTER COMPLETION:___\")\n", "    mem_usg = df.memory_usage().sum() / 1024**2 \n", "    print(\"Memory usage is: \",mem_usg,\" MB\")\n", "    print(\"This is \",100*mem_usg/start_mem_usg,\"% of the initial size\")\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Memory usage of properties dataframe is : 4428.972461700439  MB\n", "\n", "___MEMORY USAGE AFTER COMPLETION:___\n", "Memory usage is:  1293.1306457519531  MB\n", "This is  29.197080291970803 % of the initial size\n"]}], "source": ["df = reduce_memory(df)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["df['order_diff'] = df.order_number - df.last_ordered_in\n", "df.drop(['user_id', 'product_id'], axis = 1, inplace = True)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>is_reorder_1</th>\n", "      <th>order_number</th>\n", "      <th>...</th>\n", "      <th>reorder_propotion_by_user</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>orders_3</th>\n", "      <th>orders_2</th>\n", "      <th>orders_1</th>\n", "      <th>reorder_3</th>\n", "      <th>reorder_2</th>\n", "      <th>reorder_1</th>\n", "      <th>order_diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.899902</td>\n", "      <td>1.400391</td>\n", "      <td>17.593750</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>0.694824</td>\n", "      <td>5.898438</td>\n", "      <td>0.706055</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9.0</td>\n", "      <td>8.0</td>\n", "      <td>0.888672</td>\n", "      <td>3.333984</td>\n", "      <td>19.562500</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>0.694824</td>\n", "      <td>5.898438</td>\n", "      <td>0.706055</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>0.694824</td>\n", "      <td>5.898438</td>\n", "      <td>0.706055</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "      <td>0.666504</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.899902</td>\n", "      <td>3.300781</td>\n", "      <td>17.593750</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>0.694824</td>\n", "      <td>5.898438</td>\n", "      <td>0.706055</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>0.666504</td>\n", "      <td>6.332031</td>\n", "      <td>21.671875</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>0.694824</td>\n", "      <td>5.898438</td>\n", "      <td>0.706055</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 70 columns</p>\n", "</div>"], "text/plain": ["   total_product_orders_by_user  total_product_reorders_by_user  \\\n", "0                          10.0                             9.0   \n", "1                           9.0                             8.0   \n", "2                           1.0                             0.0   \n", "3                          10.0                             9.0   \n", "4                           3.0                             2.0   \n", "\n", "   user_product_reorder_percentage  avg_add_to_cart_by_user  \\\n", "0                         0.899902                 1.400391   \n", "1                         0.888672                 3.333984   \n", "2                         0.000000                 5.000000   \n", "3                         0.899902                 3.300781   \n", "4                         0.666504                 6.332031   \n", "\n", "   avg_days_since_last_bought  last_ordered_in  is_reorder_3  is_reorder_2  \\\n", "0                   17.593750             10.0           1.0           1.0   \n", "1                   19.562500             10.0           1.0           1.0   \n", "2                   28.000000              5.0           0.0           0.0   \n", "3                   17.593750             10.0           1.0           1.0   \n", "4                   21.671875             10.0           1.0           0.0   \n", "\n", "   is_reorder_1  order_number  ...  reorder_propotion_by_user  \\\n", "0           1.0          11.0  ...                   0.694824   \n", "1           1.0          11.0  ...                   0.694824   \n", "2           0.0          11.0  ...                   0.694824   \n", "3           1.0          11.0  ...                   0.694824   \n", "4           0.0          11.0  ...                   0.694824   \n", "\n", "   average_order_size  reorder_in_order  orders_3  orders_2  orders_1  \\\n", "0            5.898438          0.706055         6         6         9   \n", "1            5.898438          0.706055         6         6         9   \n", "2            5.898438          0.706055         6         6         9   \n", "3            5.898438          0.706055         6         6         9   \n", "4            5.898438          0.706055         6         6         9   \n", "\n", "   reorder_3  reorder_2  reorder_1  order_diff  \n", "0   0.666504        1.0   0.666504         1.0  \n", "1   0.666504        1.0   0.666504         1.0  \n", "2   0.666504        1.0   0.666504         6.0  \n", "3   0.666504        1.0   0.666504         1.0  \n", "4   0.666504        1.0   0.666504         1.0  \n", "\n", "[5 rows x 70 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(8474661, 70)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["label = 'reordered'\n", "x_cols = df.columns.drop('reordered')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["X = df[x_cols]\n", "y = df[label]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(6355995, 69) (6355995,)\n", "(2118666, 69) (2118666,)\n"]}], "source": ["X_train, X_test, y_train, y_test = train_test_split(X, y, stratify = y, test_size = 0.25)\n", "\n", "print(X_train.shape, y_train.shape)\n", "print(X_test.shape, y_test.shape)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.0    5734377\n", "1.0     621618\n", "Name: reordered, dtype: int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["y_train.value_counts()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["10.0"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["np.ceil(y_train.value_counts()[0]/y_train.value_counts()[1])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.0    1911460\n", "1.0     207206\n", "Name: reordered, dtype: int64"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["y_test.value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### XGBoost Model"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Anaconda3\\lib\\site-packages\\xgboost\\core.py:587: FutureWarning: Series.base is deprecated and will be removed in a future version\n", "  if getattr(data, 'base', None) is not None and \\\n"]}], "source": ["D_train = xgb.DMatrix(X_train, label=y_train)\n", "D_test = xgb.DMatrix(X_test, label=y_test)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["xgb_params = {\n", "    \"objective\"        :\"reg:logistic\",\n", "    \"eval_metric\"      :\"logloss\",\n", "    \"eta\"              :0.1,\n", "    \"max_depth\"        :6,\n", "    \"min_child_weight\" :10,\n", "    \"gamma\"            :0.70,\n", "    \"subsample\"        :0.76,\n", "    \"colsample_bytree\" :0.95,\n", "    \"alpha\"            :2e-05,\n", "    \"scale_pos_weight\" :10,\n", "    \"lambda\"           :10\n", "}"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[09:54:19] WARNING: C:\\Jenkins\\workspace\\xgboost-win64_release_0.90\\src\\learner.cc:686: Tree method is automatically selected to be 'approx' for faster speed. To use old behavior (exact greedy algorithm on single machine), set tree_method to 'exact'.\n", "[0]\ttrain-logloss:0.665341\n", "[10]\ttrain-logloss:0.554286\n", "[20]\ttrain-logloss:0.533658\n", "[30]\ttrain-logloss:0.52813\n", "[40]\ttrain-logloss:0.525416\n", "[50]\ttrain-logloss:0.523997\n", "[60]\ttrain-logloss:0.523056\n", "[70]\ttrain-logloss:0.522333\n", "[79]\ttrain-logloss:0.521507\n"]}], "source": ["watchlist= [(D_train, \"train\")]\n", "model = xgb.train(params=xgb_params, dtrain=D_train, num_boost_round = 80, evals = watchlist, verbose_eval = 10)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["probability = model.predict(D_test)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["predictions = [1 if i > 0.5 else 0 for i in probability]"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " Classification report : \n", "               precision    recall  f1-score   support\n", "\n", "         0.0       0.97      0.74      0.84   1911460\n", "         1.0       0.24      0.77      0.37    207206\n", "\n", "    accuracy                           0.74   2118666\n", "   macro avg       0.60      0.75      0.60   2118666\n", "weighted avg       0.90      0.74      0.79   2118666\n", "\n", "Accuracy   Score :  0.7416204347452595\n", "F1 Score:  0.3683727134058397\n", "Area under curve :  0.8347162433873154 \n", "\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x864 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["print (\"\\n Classification report : \\n\",classification_report(y_test, predictions))\n", "print (\"Accuracy   Score : \",accuracy_score(y_test, predictions))\n", "\n", "#confusion matrix\n", "conf_matrix = confusion_matrix(y_test,predictions)\n", "plt.figure(figsize=(12,12))\n", "plt.subplot(221)\n", "sns.heatmap(conf_matrix, fmt = \"d\",annot=True, cmap='Blues')\n", "b, t = plt.ylim()\n", "plt.ylim(b + 0.5, t - 0.5)\n", "plt.title('Confuion Matrix')\n", "plt.ylabel('True Values')\n", "plt.xlabel('Predicted Values')\n", "\n", "#f1-score\n", "f1 = f1_score(y_test, predictions)\n", "print(\"F1 Score: \", f1)\n", "\n", "#roc_auc_score\n", "model_roc_auc = roc_auc_score(y_test,probability) \n", "print (\"Area under curve : \",model_roc_auc,\"\\n\")\n", "fpr,tpr,thresholds = roc_curve(y_test,probability)\n", "gmeans = np.sqrt(tpr * (1-fpr))\n", "ix = np.argmax(gmeans)\n", "threshold = np.round(thresholds[ix],3)\n", "\n", "plt.subplot(222)\n", "plt.plot(fpr, tpr, color='darkorange', lw=1, label = \"Auc : %.3f\" %model_roc_auc)\n", "plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')\n", "plt.scatter(fpr[ix], tpr[ix], marker='o', color='black', label='Best Threshold:' + str(threshold))\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver operating characteristic')\n", "plt.legend(loc=\"lower right\")\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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************************************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\n", "text/plain": ["<Figure size 720x1080 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize = (10,15))\n", "xgb.plot_importance(model, ax = ax)\n", "fig.savefig('XGBoost Feature Importance Plot.png')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}