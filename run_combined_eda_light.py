#!/usr/bin/env python3
"""
Combined EDA Notebook Execution Script - Light Version
Running the Combined_EDA.ipynb step by step with smaller data samples
"""

import numpy as np
import pandas as pd
import os
import seaborn as sns
import matplotlib.pyplot as plt
import datetime
import warnings
warnings.filterwarnings('ignore')

# Set pandas options
pd.set_option('display.max_columns', 150)
pd.set_option('display.max_rows', 100)

# Define Seaborn color palette and matplotlib settings
colors = sns.color_palette('crest', 8)
sns.set_style('darkgrid')

# Set figure parameters
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['figure.dpi'] = 120

# Data directory path
data_directory_path = 'instacartMarketBasket_Consolidated/01_Data/InstarcartMarketBasketAnalysisDataset/'

# Converting the days and hours from numbers to their interpretable form
days_of_week = {0: 'Saturday', 1: 'Sunday', 2: 'Monday', 3: 'Tuesday', 
                4: 'Wednesday', 5: 'Thursday', 6: 'Friday'}
hour_nums = list(range(24))
hours_of_day = {hour_num: datetime.time(hour_num).strftime('%I:00 %p') for hour_num in hour_nums}

print('✅ Step 1: Libraries imported successfully!')
print(f'✅ Pandas version: {pd.__version__}')
print(f'✅ NumPy version: {np.__version__}')

print('\n' + '='*60)
print('COMBINED EDA - LIGHT VERSION (SAMPLE DATA)')
print('='*60)

# Check if data directory exists
if not os.path.exists(data_directory_path):
    print(f'❌ Error: Data directory not found: {data_directory_path}')
    exit(1)

print(f'✅ Data directory found: {data_directory_path}')
print(f'✅ Available files: {os.listdir(data_directory_path)}')

# Step 1: Load smaller datasets first
print('\n📊 Loading smaller datasets...')

# Load aisles
print('\n1. Loading aisles.csv...')
aisles = pd.read_csv(data_directory_path + 'aisles.csv')
print(f'   Shape: {aisles.shape}')
print(f'   Columns: {list(aisles.columns)}')
print(f'   Sample data:\n{aisles.head(3)}')

# Load departments  
print('\n2. Loading departments.csv...')
departments = pd.read_csv(data_directory_path + 'departments.csv')
print(f'   Shape: {departments.shape}')
print(f'   Columns: {list(departments.columns)}')
print(f'   Sample data:\n{departments.head(3)}')

# Load products
print('\n3. Loading products.csv...')
products = pd.read_csv(data_directory_path + 'products.csv')
print(f'   Shape: {products.shape}')
print(f'   Columns: {list(products.columns)}')
print(f'   Sample data:\n{products.head(3)}')

# Add organic flag
print('   Adding organic flag...')
organic = products['product_name'].str.contains('Organic', na=False)
products['is_organic'] = organic
print(f'   Organic products: {organic.sum()} out of {len(products)} ({organic.mean()*100:.1f}%)')

# Step 2: Load sample of larger datasets
print('\n📊 Loading samples of larger datasets...')

# Load orders sample
print('\n4. Loading orders.csv (sample)...')
orders_sample = pd.read_csv(data_directory_path + 'orders.csv', nrows=10000)
print(f'   Sample shape: {orders_sample.shape}')
print(f'   Columns: {list(orders_sample.columns)}')
print(f'   Sample data:\n{orders_sample.head(3)}')

# Process orders sample
print('   Processing orders sample...')
orders_sample['order_hour_of_day_text'] = orders_sample['order_hour_of_day'].map(hours_of_day)
orders_sample['order_dow_text'] = orders_sample['order_dow'].map(days_of_week)
print(f'   Processed sample:\n{orders_sample[["order_dow", "order_dow_text", "order_hour_of_day", "order_hour_of_day_text"]].head(3)}')

# Load order_products_train sample
print('\n5. Loading order_products__train.csv (sample)...')
order_products_train_sample = pd.read_csv(data_directory_path + 'order_products__train.csv', nrows=10000)
print(f'   Sample shape: {order_products_train_sample.shape}')
print(f'   Columns: {list(order_products_train_sample.columns)}')
print(f'   Sample data:\n{order_products_train_sample.head(3)}')

# Load order_products_prior sample
print('\n6. Loading order_products__prior.csv (sample)...')
order_products_prior_sample = pd.read_csv(data_directory_path + 'order_products__prior.csv', nrows=10000)
print(f'   Sample shape: {order_products_prior_sample.shape}')
print(f'   Columns: {list(order_products_prior_sample.columns)}')
print(f'   Sample data:\n{order_products_prior_sample.head(3)}')

# Step 3: Basic Analysis
print('\n' + '='*60)
print('BASIC EXPLORATORY DATA ANALYSIS')
print('='*60)

print('\n📈 BASIC STATISTICS:')
print('-' * 40)

print(f'\n🏪 AISLES:')
print(f'   Total aisles: {len(aisles)}')
print(f'   Sample aisle names: {aisles["aisle"].head(5).tolist()}')

print(f'\n🏬 DEPARTMENTS:')
print(f'   Total departments: {len(departments)}')
print(f'   Department names: {departments["department"].tolist()}')

print(f'\n🛒 PRODUCTS:')
print(f'   Total products: {len(products)}')
print(f'   Organic products: {products["is_organic"].sum()}')
print(f'   Non-organic products: {(~products["is_organic"]).sum()}')
print(f'   Organic percentage: {products["is_organic"].mean()*100:.1f}%')

print(f'\n📦 ORDERS (sample):')
print(f'   Sample size: {len(orders_sample)}')
print(f'   Unique users in sample: {orders_sample["user_id"].nunique()}')
print(f'   Order number range: {orders_sample["order_number"].min()} - {orders_sample["order_number"].max()}')
print(f'   Days of week distribution:\n{orders_sample["order_dow_text"].value_counts()}')

print(f'\n🛍️ ORDER PRODUCTS (train sample):')
print(f'   Sample size: {len(order_products_train_sample)}')
print(f'   Unique orders in sample: {order_products_train_sample["order_id"].nunique()}')
print(f'   Unique products in sample: {order_products_train_sample["product_id"].nunique()}')
print(f'   Reorder rate in sample: {order_products_train_sample["reordered"].mean()*100:.1f}%')

print(f'\n🛍️ ORDER PRODUCTS (prior sample):')
print(f'   Sample size: {len(order_products_prior_sample)}')
print(f'   Unique orders in sample: {order_products_prior_sample["order_id"].nunique()}')
print(f'   Unique products in sample: {order_products_prior_sample["product_id"].nunique()}')
print(f'   Reorder rate in sample: {order_products_prior_sample["reordered"].mean()*100:.1f}%')

# Step 4: Simple Visualizations
print('\n📊 CREATING BASIC VISUALIZATIONS...')

# Create a simple visualization
plt.figure(figsize=(15, 10))

# Plot 1: Departments
plt.subplot(2, 3, 1)
dept_counts = departments['department'].value_counts().head(10)
plt.barh(range(len(dept_counts)), dept_counts.values)
plt.yticks(range(len(dept_counts)), dept_counts.index)
plt.title('Top 10 Departments')
plt.xlabel('Count')

# Plot 2: Order hour distribution
plt.subplot(2, 3, 2)
hour_dist = orders_sample['order_hour_of_day'].value_counts().sort_index()
plt.bar(hour_dist.index, hour_dist.values)
plt.title('Orders by Hour of Day (Sample)')
plt.xlabel('Hour')
plt.ylabel('Count')
plt.xticks(rotation=45)

# Plot 3: Order day of week distribution
plt.subplot(2, 3, 3)
dow_dist = orders_sample['order_dow_text'].value_counts()
plt.bar(range(len(dow_dist)), dow_dist.values)
plt.xticks(range(len(dow_dist)), dow_dist.index, rotation=45)
plt.title('Orders by Day of Week (Sample)')
plt.ylabel('Count')

# Plot 4: Reorder rate comparison
plt.subplot(2, 3, 4)
reorder_rates = [
    order_products_train_sample['reordered'].mean(),
    order_products_prior_sample['reordered'].mean()
]
plt.bar(['Train', 'Prior'], reorder_rates)
plt.title('Reorder Rates (Sample)')
plt.ylabel('Reorder Rate')

# Plot 5: Organic vs Non-organic
plt.subplot(2, 3, 5)
organic_counts = products['is_organic'].value_counts()
plt.pie(organic_counts.values, labels=['Non-Organic', 'Organic'], autopct='%1.1f%%')
plt.title('Organic vs Non-Organic Products')

# Plot 6: Products per aisle (top 10)
plt.subplot(2, 3, 6)
aisle_products = products.groupby('aisle_id').size().sort_values(ascending=False).head(10)
plt.bar(range(len(aisle_products)), aisle_products.values)
plt.title('Products per Aisle (Top 10)')
plt.xlabel('Aisle Rank')
plt.ylabel('Product Count')

plt.tight_layout()
plt.savefig('combined_eda_basic_analysis.png', dpi=120, bbox_inches='tight')
print('✅ Basic visualizations saved as "combined_eda_basic_analysis.png"')

print('\n' + '='*60)
print('COMBINED EDA LIGHT VERSION COMPLETED')
print('='*60)
print('✅ All steps executed successfully!')
print('📊 Basic analysis completed with sample data!')
print('💡 For full analysis, consider running with more memory or cloud resources.')

if __name__ == "__main__":
    plt.show()
