#!/usr/bin/env node

/**
 * Comprehensive System Test
 * Tests all major features of the inventory management system
 */

const axios = require('axios');
// const mongoose = require('mongoose');
require('dotenv').config();

// Test configuration
const BASE_URL = 'http://localhost:5000/api';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = '';

// Test utilities
const log = (message, type = 'info') => {
  const icons = {
    info: 'ℹ️',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    test: '🧪'
  };
  console.log(`${icons[type]} ${message}`);
};

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Authentication
async function authenticate() {
  try {
    log('Authenticating...', 'test');
    const response = await axios.post(`${BASE_URL}/auth/login`, TEST_USER);
    authToken = response.data.token;
    log('Authentication successful', 'success');
    return true;
  } catch (error) {
    log(`Authentication failed: ${error.response?.data?.message || error.message}`, 'error');
    return false;
  }
}

// Test database connectivity
async function testDatabase() {
  try {
    log('Testing database connectivity via API...', 'test');
    const response = await axios.get(`${BASE_URL}/health`);
    log('Database connection successful', 'success');
    return true;
  } catch (error) {
    log(`Database connection failed: ${error.message}`, 'error');
    return false;
  }
}

// Test chatbot functionality
async function testChatbot() {
  try {
    log('Testing enhanced chatbot...', 'test');
    
    const testQueries = [
      'How many products are in the database?',
      'Show me low stock items',
      'Find products related to coffee',
      'What are the most popular categories?',
      'Give me a complete database overview'
    ];

    for (const query of testQueries) {
      try {
        const response = await axios.post(`${BASE_URL}/chatbot/query`, {
          query,
          session_id: 'test_session_' + Date.now(),
          user_id: 'test_user'
        }, {
          headers: { Authorization: `Bearer ${authToken}` }
        });

        log(`Query: "${query}" - Response received (${response.data.confidence} confidence)`, 'success');
        await delay(1000); // Rate limiting
      } catch (error) {
        log(`Query failed: "${query}" - ${error.response?.data?.error || error.message}`, 'error');
      }
    }
    
    return true;
  } catch (error) {
    log(`Chatbot test failed: ${error.message}`, 'error');
    return false;
  }
}

// Test demand forecasting
async function testDemandForecasting() {
  try {
    log('Testing demand forecasting...', 'test');
    
    // Test comprehensive dashboard
    const dashboardResponse = await axios.get(`${BASE_URL}/predictive/dashboard/comprehensive`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    log('Demand forecasting dashboard accessible', 'success');
    
    // Test forecasts endpoint
    const forecastsResponse = await axios.get(`${BASE_URL}/predictive/forecasts?limit=5`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    log(`Found ${forecastsResponse.data.forecasts?.length || 0} forecasts`, 'success');
    
    return true;
  } catch (error) {
    log(`Demand forecasting test failed: ${error.response?.data?.error || error.message}`, 'error');
    return false;
  }
}

// Test Instacart analytics
async function testInstacartAnalytics() {
  try {
    log('Testing Instacart analytics...', 'test');
    
    const endpoints = [
      '/instacart/analytics/summary',
      '/instacart/analytics/top-products?limit=5',
      '/instacart/analytics/department-stats',
      '/instacart/analytics/business-insights'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${BASE_URL}${endpoint}`, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        log(`Endpoint ${endpoint} - OK`, 'success');
      } catch (error) {
        log(`Endpoint ${endpoint} - Failed: ${error.response?.data?.error || error.message}`, 'error');
      }
    }
    
    return true;
  } catch (error) {
    log(`Instacart analytics test failed: ${error.message}`, 'error');
    return false;
  }
}

// Test inventory management
async function testInventoryManagement() {
  try {
    log('Testing inventory management...', 'test');
    
    // Test products endpoint
    const productsResponse = await axios.get(`${BASE_URL}/products?limit=5`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    log(`Found ${productsResponse.data.products?.length || 0} products`, 'success');
    
    // Test suppliers endpoint
    const suppliersResponse = await axios.get(`${BASE_URL}/suppliers?limit=5`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    log(`Found ${suppliersResponse.data.suppliers?.length || 0} suppliers`, 'success');
    
    return true;
  } catch (error) {
    log(`Inventory management test failed: ${error.response?.data?.error || error.message}`, 'error');
    return false;
  }
}

// Test dashboard endpoints
async function testDashboard() {
  try {
    log('Testing dashboard endpoints...', 'test');
    
    const dashboardResponse = await axios.get(`${BASE_URL}/dashboard/summary`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    log('Dashboard summary accessible', 'success');
    
    return true;
  } catch (error) {
    log(`Dashboard test failed: ${error.response?.data?.error || error.message}`, 'error');
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Comprehensive System Test\n');
  console.log('=' * 50);
  
  const results = {
    database: false,
    auth: false,
    chatbot: false,
    forecasting: false,
    analytics: false,
    inventory: false,
    dashboard: false
  };

  // Run tests
  results.database = await testDatabase();
  results.auth = await authenticate();
  
  if (results.auth) {
    results.chatbot = await testChatbot();
    results.forecasting = await testDemandForecasting();
    results.analytics = await testInstacartAnalytics();
    results.inventory = await testInventoryManagement();
    results.dashboard = await testDashboard();
  }

  // Summary
  console.log('\n' + '=' * 50);
  console.log('📊 Test Results Summary:');
  console.log('=' * 50);
  
  Object.entries(results).forEach(([test, passed]) => {
    log(`${test.toUpperCase()}: ${passed ? 'PASSED' : 'FAILED'}`, passed ? 'success' : 'error');
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    log('🎉 All systems operational! The inventory management app is running smoothly.', 'success');
  } else {
    log('⚠️ Some systems need attention. Check the failed tests above.', 'warning');
  }
  
  // Test completed
  
  process.exit(passedTests === totalTests ? 0 : 1);
}

// Run the tests
runTests().catch(error => {
  log(`Test runner failed: ${error.message}`, 'error');
  process.exit(1);
});
