import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line, AreaChart, Area
} from 'recharts';
import {
  ShoppingCart, Package, TrendingUp, Calendar, Users, Target,
  Clock, Star, AlertTriangle, BarChart3, Pie<PERSON>hart as PieChartIcon,
  Activity, Zap, Award, RefreshCw, Building, ShoppingBasket
} from 'lucide-react';
import axios from 'axios';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

const InstacartAnalysis = () => {
  const [summary, setSummary] = useState({});
  const [topProducts, setTopProducts] = useState([]);
  const [ordersByHour, setOrdersByHour] = useState([]);
  const [ordersByDow, setOrdersByDow] = useState([]);
  const [departmentStats, setDepartmentStats] = useState([]);
  const [aisleStats, setAisleStats] = useState([]);
  const [businessInsights, setBusinessInsights] = useState({});
  const [highPriorityProducts, setHighPriorityProducts] = useState([]);
  const [customerSegmentation, setCustomerSegmentation] = useState([]);
  const [reorderAnalysis, setReorderAnalysis] = useState([]);
  const [temporalPatterns, setTemporalPatterns] = useState({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // New state for comprehensive market basket analysis
  const [associationRules, setAssociationRules] = useState([]);
  const [marketBasketInsights, setMarketBasketInsights] = useState({
    basket_size_stats: {},
    top_product_combinations: [],
    category_associations: [],
    reorder_patterns: []
  });
  const [customerBehaviorAdvanced, setCustomerBehaviorAdvanced] = useState({
    customer_lifecycle: [],
    loyalty_segments: [],
    purchase_patterns: [],
    customer_value_distribution: []
  });
  const [productAffinity, setProductAffinity] = useState([]);
  const [selectedProductId, setSelectedProductId] = useState(null);

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const [
        summaryRes,
        topProductsRes,
        ordersByHourRes,
        ordersByDowRes,
        departmentStatsRes,
        aisleStatsRes,
        businessInsightsRes,
        highPriorityRes,
        customerSegmentationRes,
        reorderAnalysisRes,
        temporalPatternsRes,
        associationRulesRes,
        marketBasketInsightsRes,
        customerBehaviorAdvancedRes
      ] = await Promise.all([
        axios.get('/api/instacart/analytics/summary'),
        axios.get('/api/instacart/analytics/top-products?limit=10'),
        axios.get('/api/instacart/analytics/orders-by-hour'),
        axios.get('/api/instacart/analytics/orders-by-dow'),
        axios.get('/api/instacart/analytics/department-stats'),
        axios.get('/api/instacart/analytics/aisle-stats?limit=15'),
        axios.get('/api/instacart/analytics/business-insights'),
        axios.get('/api/instacart/analytics/high-priority-products?limit=10'),
        axios.get('/api/instacart/analytics/customer-segmentation'),
        axios.get('/api/instacart/analytics/reorder-analysis'),
        axios.get('/api/instacart/analytics/temporal-patterns'),
        axios.get('/api/instacart/analytics/association-rules?limit=15'),
        axios.get('/api/instacart/analytics/market-basket-insights'),
        axios.get('/api/instacart/analytics/customer-behavior-advanced')
      ]);

      setSummary(summaryRes.data);
      setTopProducts(topProductsRes.data);
      setOrdersByHour(ordersByHourRes.data);
      setOrdersByDow(ordersByDowRes.data);
      setDepartmentStats(departmentStatsRes.data);
      setAisleStats(aisleStatsRes.data);
      setBusinessInsights(businessInsightsRes.data);
      setHighPriorityProducts(highPriorityRes.data);
      setCustomerSegmentation(customerSegmentationRes.data);
      setReorderAnalysis(reorderAnalysisRes.data);
      setTemporalPatterns(temporalPatternsRes.data);
      setAssociationRules(associationRulesRes.data);
      setMarketBasketInsights(marketBasketInsightsRes.data);
      setCustomerBehaviorAdvanced(customerBehaviorAdvancedRes.data);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: BarChart3 },
    { id: 'insights', name: 'Business Insights', icon: Target },
    { id: 'products', name: 'Product Analysis', icon: Package },
    { id: 'customers', name: 'Customer Behavior', icon: Users },
    { id: 'associations', name: 'Association Rules', icon: ShoppingBasket },
    { id: 'temporal', name: 'Temporal Patterns', icon: Clock }
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Market Basket Analysis</h1>
          <p className="text-gray-600 mt-1">Comprehensive insights from Instacart dataset analysis</p>
        </div>
        <button
          onClick={fetchAnalyticsData}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
        >
          <RefreshCw className="h-4 w-4" />
          <span>Refresh Data</span>
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Summary Cards - Always visible */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <Package className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Products</p>
              <p className="text-2xl font-bold text-gray-900">{summary.totalProducts?.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <ShoppingCart className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">{summary.totalOrders?.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-purple-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Departments</p>
              <p className="text-2xl font-bold text-gray-900">{summary.totalDepartments}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-orange-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Aisles</p>
              <p className="text-2xl font-bold text-gray-900">{summary.totalAisles}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <Calendar className="h-8 w-8 text-red-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Order Items</p>
              <p className="text-2xl font-bold text-gray-900">{summary.totalOrderProducts?.toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Business Insights Tab */}
      {activeTab === 'insights' && (
        <div className="space-y-6">
          {/* Combined EDA Executive Summary */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Target className="h-6 w-6 text-blue-600 mr-3" />
              Combined EDA Executive Summary
            </h2>
            <p className="text-gray-600 mb-6">
              Comprehensive insights from the Combined_EDA_executed notebook analysis covering {summary.totalProducts?.toLocaleString()} products, {summary.totalOrders?.toLocaleString()} orders, and {summary.totalDepartments} departments.
            </p>

            {/* Key Dataset Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h4 className="font-semibold text-gray-900 mb-2">Dataset Scale</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• <strong>206,209</strong> unique customers</li>
                  <li>• <strong>49,688</strong> unique products</li>
                  <li>• <strong>134</strong> aisles across <strong>21</strong> departments</li>
                  <li>• <strong>4-100</strong> orders per customer</li>
                </ul>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h4 className="font-semibold text-gray-900 mb-2">Customer Behavior</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• <strong>~59%</strong> overall reorder rate</li>
                  <li>• Peak activity on <strong>weekends</strong></li>
                  <li>• <strong>Saturday afternoons</strong> busiest</li>
                  <li>• Strong customer loyalty patterns</li>
                </ul>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h4 className="font-semibold text-gray-900 mb-2">Product Insights</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Fresh produce dominates orders</li>
                  <li>• Organic products show high loyalty</li>
                  <li>• Dairy & eggs peak in winter</li>
                  <li>• Seasonal patterns identified</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Target className="h-6 w-6 text-blue-600 mr-3" />
              Comprehensive Business Insights
            </h2>
            <p className="text-gray-600 mb-6">
              Strategic insights derived from comprehensive analysis of customer behavior, product performance, and temporal patterns.
            </p>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Temporal Patterns */}
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Clock className="h-5 w-5 text-blue-600 mr-2" />
                  Temporal Shopping Patterns
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Peak Shopping Day:</span>
                    <span className="font-medium text-blue-600">{businessInsights.temporal_patterns?.peak_day}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Peak Shopping Hour:</span>
                    <span className="font-medium text-blue-600">{businessInsights.temporal_patterns?.peak_hour}:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Reorder Cycle:</span>
                    <span className="font-medium text-blue-600">{businessInsights.temporal_patterns?.avg_reorder_cycle} days</span>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Recommendations:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    {businessInsights.temporal_patterns?.recommendations?.map((rec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-600 mr-2">•</span>
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Product Performance */}
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Star className="h-5 w-5 text-yellow-600 mr-2" />
                  Product Performance
                </h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-gray-600 block">Most Popular Product:</span>
                    <span className="font-medium text-yellow-600 text-sm">{businessInsights.product_performance?.top_product}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Top Department:</span>
                    <span className="font-medium text-yellow-600">{businessInsights.product_performance?.top_department}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Reorder Rate:</span>
                    <span className="font-medium text-yellow-600">{businessInsights.product_performance?.overall_reorder_rate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Organic Products:</span>
                    <span className="font-medium text-yellow-600">{businessInsights.product_performance?.organic_products_pct}%</span>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                  <h4 className="font-medium text-yellow-900 mb-2">Recommendations:</h4>
                  <ul className="text-sm text-yellow-800 space-y-1">
                    {businessInsights.product_performance?.recommendations?.map((rec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-600 mr-2">•</span>
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Customer Behavior */}
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Users className="h-5 w-5 text-green-600 mr-2" />
                  Customer Behavior
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Orders/Customer:</span>
                    <span className="font-medium text-green-600">{businessInsights.customer_behavior?.avg_orders_per_customer}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">High Loyalty Customers:</span>
                    <span className="font-medium text-green-600">{businessInsights.customer_behavior?.high_loyalty_customers?.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Loyalty Rate:</span>
                    <span className="font-medium text-green-600">{businessInsights.customer_behavior?.high_loyalty_percentage}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Order Size:</span>
                    <span className="font-medium text-green-600">{businessInsights.customer_behavior?.avg_order_size} items</span>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-green-50 rounded-lg">
                  <h4 className="font-medium text-green-900 mb-2">Recommendations:</h4>
                  <ul className="text-sm text-green-800 space-y-1">
                    {businessInsights.customer_behavior?.recommendations?.map((rec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-green-600 mr-2">•</span>
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Combined EDA Visualizations */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
              <Activity className="h-5 w-5 text-green-600 mr-2" />
              Combined EDA Visualizations & Analysis
            </h3>

            {/* Key Visualizations Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* General Analysis EDA */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">General Analysis Overview</h4>
                <img
                  src="/images/instacart/general-analysis-eda.png"
                  alt="General Analysis EDA"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>

              {/* Time Heatmap */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Shopping Time Patterns</h4>
                <img
                  src="/images/instacart/time-heatmap-eda.png"
                  alt="Time Heatmap Analysis"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>

              {/* Department Analysis */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Department Performance</h4>
                <img
                  src="/images/instacart/department-analysis.png"
                  alt="Department Analysis"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>

              {/* Reordering Heatmap */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Reordering Patterns</h4>
                <img
                  src="/images/instacart/reordering_heatmap.png"
                  alt="Reordering Heatmap"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>
            </div>

            {/* Additional Analysis Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
              <div className="bg-gray-50 rounded-lg p-3">
                <h5 className="font-medium text-gray-900 mb-2 text-sm">Day of Week Patterns</h5>
                <img
                  src="/images/instacart/dow.png"
                  alt="Day of Week Analysis"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>

              <div className="bg-gray-50 rounded-lg p-3">
                <h5 className="font-medium text-gray-900 mb-2 text-sm">Hour of Day Distribution</h5>
                <img
                  src="/images/instacart/houe_of_day.png"
                  alt="Hour of Day Analysis"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>

              <div className="bg-gray-50 rounded-lg p-3">
                <h5 className="font-medium text-gray-900 mb-2 text-sm">Product Count per Basket</h5>
                <img
                  src="/images/instacart/product_count_per_basket.png"
                  alt="Product Count per Basket"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>
            </div>
          </div>

          {/* Combined EDA Key Findings */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              <Activity className="h-5 w-5 text-green-600 mr-2" />
              Combined EDA Key Findings & Recommendations
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Temporal Insights */}
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 border-b pb-2">1. Temporal Shopping Patterns</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Peak shopping day:</span>
                    <span className="font-medium text-blue-600">Saturday</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Peak shopping hour:</span>
                    <span className="font-medium text-blue-600">14:00 (2 PM)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Average reorder cycle:</span>
                    <span className="font-medium text-blue-600">15.2 days</span>
                  </div>
                </div>
                <div className="bg-blue-50 p-3 rounded-lg">
                  <h5 className="font-medium text-blue-900 mb-1">Recommendations:</h5>
                  <ul className="text-xs text-blue-800 space-y-1">
                    <li>• Schedule inventory restocking before peak days</li>
                    <li>• Increase staffing during peak hours (2-4 PM)</li>
                    <li>• Plan promotional campaigns around 15-day cycles</li>
                  </ul>
                </div>
              </div>

              {/* Product Performance */}
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 border-b pb-2">2. Product Performance Analysis</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Top department:</span>
                    <span className="font-medium text-yellow-600">Produce</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Highest reorder rate:</span>
                    <span className="font-medium text-yellow-600">Bananas (85%)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Organic preference:</span>
                    <span className="font-medium text-yellow-600">23% of products</span>
                  </div>
                </div>
                <div className="bg-yellow-50 p-3 rounded-lg">
                  <h5 className="font-medium text-yellow-900 mb-1">Recommendations:</h5>
                  <ul className="text-xs text-yellow-800 space-y-1">
                    <li>• Prioritize fresh produce inventory</li>
                    <li>• Expand organic product offerings</li>
                    <li>• Focus on high-reorder staples</li>
                  </ul>
                </div>
              </div>

              {/* Customer Segmentation */}
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 border-b pb-2">3. Customer Behavior Insights</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Average orders per customer:</span>
                    <span className="font-medium text-green-600">17.1</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">High loyalty customers:</span>
                    <span className="font-medium text-green-600">20% drive 60% of sales</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Most loyal customer:</span>
                    <span className="font-medium text-green-600">100 orders</span>
                  </div>
                </div>
                <div className="bg-green-50 p-3 rounded-lg">
                  <h5 className="font-medium text-green-900 mb-1">Recommendations:</h5>
                  <ul className="text-xs text-green-800 space-y-1">
                    <li>• Implement loyalty rewards program</li>
                    <li>• Target high-value customer segments</li>
                    <li>• Personalize recommendations for repeat buyers</li>
                  </ul>
                </div>
              </div>

              {/* Predictive Insights */}
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 border-b pb-2">4. Predictive Demand Insights</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Seasonal patterns:</span>
                    <span className="font-medium text-purple-600">Weekend peaks</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Reorder prediction:</span>
                    <span className="font-medium text-purple-600">7-30 day cycles</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Category turnover:</span>
                    <span className="font-medium text-purple-600">Fresh > Dairy > Pantry</span>
                  </div>
                </div>
                <div className="bg-purple-50 p-3 rounded-lg">
                  <h5 className="font-medium text-purple-900 mb-1">For Demand Forecasting:</h5>
                  <ul className="text-xs text-purple-800 space-y-1">
                    <li>• Use 15-day moving averages for predictions</li>
                    <li>• Apply seasonal adjustments for weekends</li>
                    <li>• Weight fresh products higher in forecasts</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Summary Conclusion */}
            <div className="mt-6 p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-200">
              <h4 className="font-bold text-gray-900 mb-2">📊 Combined EDA Summary</h4>
              <p className="text-sm text-gray-700">
                This comprehensive analysis of the Instacart Market Basket dataset reveals strong customer loyalty patterns,
                clear temporal shopping behaviors, and significant opportunities for predictive demand forecasting.
                The insights provide a solid foundation for implementing intelligent inventory management systems
                that can anticipate customer needs and optimize business operations.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Summary Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Total Products</p>
                  <p className="text-2xl font-bold">{summary.totalProducts?.toLocaleString() || 'N/A'}</p>
                </div>
                <Package className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">Total Orders</p>
                  <p className="text-2xl font-bold">{summary.totalOrders?.toLocaleString() || 'N/A'}</p>
                </div>
                <ShoppingCart className="h-8 w-8 text-green-200" />
              </div>
            </div>
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">Departments</p>
                  <p className="text-2xl font-bold">{summary.totalDepartments?.toLocaleString() || 'N/A'}</p>
                </div>
                <Building className="h-8 w-8 text-purple-200" />
              </div>
            </div>
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm">Avg Basket Size</p>
                  <p className="text-2xl font-bold">{marketBasketInsights.basket_size_stats?.avg_basket_size?.toFixed(1) || 'N/A'}</p>
                </div>
                <ShoppingBasket className="h-8 w-8 text-orange-200" />
              </div>
            </div>
          </div>

          {/* Main Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Products */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top 10 Most Ordered Products</h3>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={topProducts}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="product_name"
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={10}
                  />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="order_count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Orders by Hour */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Orders by Hour of Day</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={ordersByHour}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="_id" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="count" stroke="#8884d8" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Orders by Day of Week */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Orders by Day of Week</h3>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={ordersByDow}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day_name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Department Distribution */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Department Popularity</h3>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={departmentStats.slice(0, 8)}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ department_name, percent }) => `${department_name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="total_orders"
                  >
                    {departmentStats.slice(0, 8).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Market Basket Analysis Overview */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <ShoppingBasket className="h-5 w-5 text-blue-600 mr-2" />
              Market Basket Analysis Overview
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Product Combinations */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Top Product Combinations</h4>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {marketBasketInsights.top_product_combinations?.slice(0, 8).map((combo, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <div className="text-sm">
                        <span className="font-medium">{combo._id?.product1}</span>
                        <span className="text-gray-500 mx-2">+</span>
                        <span className="font-medium">{combo._id?.product2}</span>
                      </div>
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {combo.frequency} times
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Basket Size Distribution */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Basket Statistics</h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Average Basket Size:</span>
                    <span className="font-medium">{marketBasketInsights.basket_size_stats?.avg_basket_size?.toFixed(1) || 'N/A'} items</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Min Basket Size:</span>
                    <span className="font-medium">{marketBasketInsights.basket_size_stats?.min_basket_size || 'N/A'} items</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max Basket Size:</span>
                    <span className="font-medium">{marketBasketInsights.basket_size_stats?.max_basket_size || 'N/A'} items</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Baskets:</span>
                    <span className="font-medium">{marketBasketInsights.basket_size_stats?.total_baskets?.toLocaleString() || 'N/A'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Product Analysis Tab */}
      {activeTab === 'products' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* High Priority Products */}
            <div className="bg-white rounded-lg shadow-md">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                  High Priority Products (Top Reorder Rates)
                </h3>
                <p className="text-sm text-gray-600 mt-1">Products with highest customer loyalty and reorder frequency</p>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reorder Rate
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Orders
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {highPriorityProducts.slice(0, 8).map((product, index) => (
                      <tr key={product.product_id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="px-6 py-4 text-sm font-medium text-gray-900">
                          <div className="max-w-xs truncate" title={product.product_name}>
                            {product.product_name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            product.reorder_rate > 0.8 ? 'bg-red-100 text-red-800' :
                            product.reorder_rate > 0.6 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {(product.reorder_rate * 100).toFixed(1)}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {product.total_orders.toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Reorder Analysis by Department */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <RefreshCw className="h-5 w-5 text-blue-600 mr-2" />
                Reorder Analysis by Department
              </h3>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={reorderAnalysis.slice(0, 10)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="department_name"
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={10}
                  />
                  <YAxis />
                  <Tooltip
                    formatter={(value, name) => [
                      name === 'reorder_rate' ? `${(value * 100).toFixed(1)}%` : value.toLocaleString(),
                      name === 'reorder_rate' ? 'Reorder Rate' : 'Total Orders'
                    ]}
                  />
                  <Bar dataKey="reorder_rate" fill="#8884d8" name="reorder_rate" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Product Analysis Visualizations */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Package className="h-5 w-5 text-purple-600 mr-2" />
              Product Analysis Visualizations
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Most Popular Products */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Most Popular Products</h4>
                <img
                  src="/images/instacart/Most-popular-products.png"
                  alt="Most Popular Products"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>

              {/* Organic vs Inorganic Products */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Organic vs Inorganic Products</h4>
                <img
                  src="/images/instacart/Total-organic-inorganic-products.png"
                  alt="Organic vs Inorganic Products"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>

              {/* Reorder Analysis */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Reorder vs Organic/Inorganic</h4>
                <img
                  src="/images/instacart/Reorder-organic-inorganic-products.png"
                  alt="Reorder Analysis"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>

              {/* Add to Cart vs Reorder */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Add to Cart vs Reorder</h4>
                <img
                  src="/images/instacart/Add-to-cart-VS-reorder.png"
                  alt="Add to Cart vs Reorder"
                  className="w-full h-auto rounded-lg shadow-sm"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>
            </div>
          </div>

          {/* Top Aisles Table */}
          <div className="bg-white rounded-lg shadow-md">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Package className="h-5 w-5 text-purple-600 mr-2" />
                Top Performing Aisles
              </h3>
              <p className="text-sm text-gray-600 mt-1">Aisles with highest order volumes and customer engagement</p>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aisle Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Orders
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unique Products
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reorder Rate
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {aisleStats.map((aisle, index) => (
                    <tr key={aisle.aisle_id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {aisle.aisle_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {aisle.total_orders.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {aisle.unique_products_count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          aisle.reorder_rate > 0.6 ? 'bg-green-100 text-green-800' :
                          aisle.reorder_rate > 0.4 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {(aisle.reorder_rate * 100).toFixed(1)}%
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
      {/* Customer Behavior Tab */}
      {activeTab === 'customers' && (
        <div className="space-y-6">
          {/* Advanced Customer Loyalty Segments */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Award className="h-5 w-5 text-purple-600 mr-2" />
              Advanced Customer Loyalty Segments (RFM Analysis)
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={customerBehaviorAdvanced.loyalty_segments || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="_id"
                      angle={-45}
                      textAnchor="end"
                      height={100}
                      fontSize={10}
                    />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        value.toLocaleString(),
                        name === 'customer_count' ? 'Customers' : 'Avg Frequency'
                      ]}
                    />
                    <Bar dataKey="customer_count" fill="#8884d8" name="customer_count" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Loyalty Segment Breakdown</h4>
                {customerBehaviorAdvanced.loyalty_segments?.map((segment, index) => (
                  <div key={segment._id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                      <span className="font-medium text-gray-900">{segment._id}</span>
                      <p className="text-sm text-gray-600">
                        Avg Frequency: {segment.avg_frequency?.toFixed(1)} orders
                      </p>
                    </div>
                    <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      {segment.customer_count?.toLocaleString()} customers
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Customer Value Distribution */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Target className="h-5 w-5 text-green-600 mr-2" />
              Customer Value Distribution
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={customerBehaviorAdvanced.customer_value_distribution || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="_id" />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        value.toLocaleString(),
                        name === 'customer_count' ? 'Customers' : 'Avg Orders'
                      ]}
                    />
                    <Bar dataKey="customer_count" fill="#10b981" name="customer_count" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Value Segment Analysis</h4>
                {customerBehaviorAdvanced.customer_value_distribution?.map((segment, index) => (
                  <div key={segment._id} className="p-3 border border-gray-200 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium text-gray-900">Value Range: {segment._id}</span>
                      <span className="text-sm text-gray-600">{segment.customer_count?.toLocaleString()} customers</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Avg Orders:</span>
                        <span className="ml-2 font-medium">{segment.avg_orders?.toFixed(1)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Reorder Rate:</span>
                        <span className="ml-2 font-medium">{(segment.avg_reorder_rate * 100)?.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Customer Lifecycle Analysis */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <TrendingUp className="h-5 w-5 text-blue-600 mr-2" />
              Customer Lifecycle Analysis
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={customerBehaviorAdvanced.customer_lifecycle || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="_id" />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        value.toLocaleString(),
                        name === 'customer_count' ? 'Customers' : 'Avg Orders'
                      ]}
                    />
                    <Area type="monotone" dataKey="customer_count" stackId="1" stroke="#3b82f6" fill="#3b82f6" />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Lifecycle Insights</h4>
                <div className="space-y-3">
                  {customerBehaviorAdvanced.customer_lifecycle?.map((stage, index) => (
                    <div key={stage._id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <div>
                        <span className="text-sm font-medium">Lifetime: {stage._id} orders</span>
                        <p className="text-xs text-gray-600">
                          Avg Frequency: {stage.avg_frequency?.toFixed(2)}
                        </p>
                      </div>
                      <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {stage.customer_count} customers
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Original Customer Segmentation */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Customer Segmentation */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Users className="h-5 w-5 text-green-600 mr-2" />
                Customer Segmentation by Order Volume
              </h3>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={customerSegmentation}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="segment_label"
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={10}
                  />
                  <YAxis />
                  <Tooltip
                    formatter={(value, name) => [
                      value.toLocaleString(),
                      name === 'customer_count' ? 'Customers' : 'Avg Orders'
                    ]}
                  />
                  <Bar dataKey="customer_count" fill="#82ca9d" name="customer_count" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Customer Loyalty Distribution */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Award className="h-5 w-5 text-purple-600 mr-2" />
                Customer Loyalty Distribution
              </h3>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={customerSegmentation}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ segment_label, percent }) => `${segment_label.split(' ')[0]} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="customer_count"
                  >
                    {customerSegmentation.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [value.toLocaleString(), 'Customers']}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Customer Segmentation Table */}
          <div className="bg-white rounded-lg shadow-md">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Detailed Customer Segmentation</h3>
              <p className="text-sm text-gray-600 mt-1">Customer distribution by order frequency and behavior patterns</p>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer Segment
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer Count
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Avg Orders
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Avg Days Between Orders
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {customerSegmentation.map((segment, index) => (
                    <tr key={segment._id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {segment.segment_label}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {segment.customer_count.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {segment.avg_orders?.toFixed(1) || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {segment.avg_days_between?.toFixed(1) || 'N/A'} days
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Association Rules Tab */}
      {activeTab === 'associations' && (
        <div className="space-y-6">
          {/* Association Rules Analysis */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <ShoppingBasket className="h-5 w-5 text-purple-600 mr-2" />
              Association Rules Analysis
            </h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Antecedent (If)
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Consequent (Then)
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Support
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Support Count
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {associationRules.map((rule, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 text-sm font-medium text-gray-900">
                        <div className="max-w-xs truncate" title={rule.antecedent}>
                          {rule.antecedent}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        <div className="max-w-xs truncate" title={rule.consequent}>
                          {rule.consequent}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          rule.support > 0.05 ? 'bg-green-100 text-green-800' :
                          rule.support > 0.02 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {(rule.support * 100).toFixed(2)}%
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {rule.support_count?.toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Category Associations */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Package className="h-5 w-5 text-blue-600 mr-2" />
              Category Associations
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={marketBasketInsights.category_associations || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="_id.dept1"
                      angle={-45}
                      textAnchor="end"
                      height={100}
                      fontSize={10}
                    />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => [value.toLocaleString(), 'Co-occurrence Frequency']}
                      labelFormatter={(label) => `Department: ${label}`}
                    />
                    <Bar dataKey="frequency" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Top Department Combinations</h4>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {marketBasketInsights.category_associations?.slice(0, 10).map((assoc, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <div className="text-sm">
                        <span className="font-medium">{assoc._id?.dept1}</span>
                        <span className="text-gray-500 mx-2">+</span>
                        <span className="font-medium">{assoc._id?.dept2}</span>
                      </div>
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {assoc.frequency} orders
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Reorder Patterns in Baskets */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <RefreshCw className="h-5 w-5 text-green-600 mr-2" />
              Reorder Patterns in Market Baskets
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={marketBasketInsights.reorder_patterns || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="_id"
                      formatter={(value) => {
                        if (value === 0) return '0-25%';
                        if (value === 0.25) return '25-50%';
                        if (value === 0.5) return '50-75%';
                        if (value === 0.75) return '75-100%';
                        return value;
                      }}
                    />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        value.toLocaleString(),
                        name === 'order_count' ? 'Orders' : 'Avg Basket Size'
                      ]}
                      labelFormatter={(label) => {
                        if (label === 0) return 'Reorder Ratio: 0-25%';
                        if (label === 0.25) return 'Reorder Ratio: 25-50%';
                        if (label === 0.5) return 'Reorder Ratio: 50-75%';
                        if (label === 0.75) return 'Reorder Ratio: 75-100%';
                        return `Reorder Ratio: ${label}`;
                      }}
                    />
                    <Bar dataKey="order_count" fill="#10b981" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Reorder Pattern Insights</h4>
                <div className="space-y-3">
                  {marketBasketInsights.reorder_patterns?.map((pattern, index) => (
                    <div key={pattern._id} className="p-3 border border-gray-200 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-gray-900">
                          Reorder Ratio: {
                            pattern._id === 0 ? '0-25%' :
                            pattern._id === 0.25 ? '25-50%' :
                            pattern._id === 0.5 ? '50-75%' :
                            pattern._id === 0.75 ? '75-100%' : pattern._id
                          }
                        </span>
                        <span className="text-sm text-gray-600">{pattern.order_count?.toLocaleString()} orders</span>
                      </div>
                      <div className="text-sm text-gray-600">
                        Avg Basket Size: {pattern.avg_basket_size?.toFixed(1)} items
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Market Basket Insights Summary */}
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Zap className="h-5 w-5 text-purple-600 mr-2" />
              Market Basket Analysis Key Insights
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-purple-900">Product Associations</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-start">
                    <span className="text-purple-600 mr-2">•</span>
                    <span className="text-gray-700">Strong associations found between complementary products</span>
                  </div>
                  <div className="flex items-start">
                    <span className="text-purple-600 mr-2">•</span>
                    <span className="text-gray-700">Cross-category purchases indicate diverse shopping patterns</span>
                  </div>
                  <div className="flex items-start">
                    <span className="text-purple-600 mr-2">•</span>
                    <span className="text-gray-700">High reorder ratios suggest customer loyalty to specific combinations</span>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <h4 className="font-medium text-purple-900">Business Recommendations</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-start">
                    <span className="text-purple-600 mr-2">•</span>
                    <span className="text-gray-700">Bundle frequently associated products for promotions</span>
                  </div>
                  <div className="flex items-start">
                    <span className="text-purple-600 mr-2">•</span>
                    <span className="text-gray-700">Place complementary items near each other in stores</span>
                  </div>
                  <div className="flex items-start">
                    <span className="text-purple-600 mr-2">•</span>
                    <span className="text-gray-700">Use association rules for personalized recommendations</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Temporal Patterns Tab */}
      {activeTab === 'temporal' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Detailed Hourly Patterns */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Clock className="h-5 w-5 text-blue-600 mr-2" />
                Detailed Hourly Shopping Patterns
              </h3>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={temporalPatterns.hourly_patterns}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip
                    formatter={(value, name) => [
                      value.toLocaleString(),
                      name === 'order_count' ? 'Orders' : 'Unique Customers'
                    ]}
                  />
                  <Area type="monotone" dataKey="order_count" stackId="1" stroke="#8884d8" fill="#8884d8" />
                  <Area type="monotone" dataKey="unique_customers_count" stackId="2" stroke="#82ca9d" fill="#82ca9d" />
                </AreaChart>
              </ResponsiveContainer>
            </div>

            {/* Daily Patterns with Customer Behavior */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Calendar className="h-5 w-5 text-green-600 mr-2" />
                Weekly Shopping Patterns
              </h3>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={temporalPatterns.daily_patterns}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day_name" />
                  <YAxis />
                  <Tooltip
                    formatter={(value, name) => [
                      value.toLocaleString(),
                      name === 'order_count' ? 'Orders' : 'Unique Customers'
                    ]}
                  />
                  <Bar dataKey="order_count" fill="#8884d8" name="order_count" />
                  <Bar dataKey="unique_customers_count" fill="#82ca9d" name="unique_customers_count" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Reorder Cycle Analysis */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <RefreshCw className="h-5 w-5 text-purple-600 mr-2" />
                Reorder Cycle Analysis
              </h3>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={temporalPatterns.reorder_cycles}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="days_range"
                    formatter={(value) => `${value} days`}
                  />
                  <YAxis />
                  <Tooltip
                    formatter={(value, name) => [
                      value.toLocaleString(),
                      name === 'order_count' ? 'Orders' : 'Customers'
                    ]}
                  />
                  <Bar dataKey="order_count" fill="#ff7300" name="order_count" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Predictive Insights Summary */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Zap className="h-5 w-5 text-purple-600 mr-2" />
                Predictive Insights for Demand Forecasting
              </h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <span className="text-purple-600 mr-2">•</span>
                  <span className="text-gray-700"><strong>Seasonal patterns:</strong> Weekend shopping peaks with highest activity on Sundays</span>
                </div>
                <div className="flex items-start">
                  <span className="text-purple-600 mr-2">•</span>
                  <span className="text-gray-700"><strong>Reorder cycles:</strong> Most customers reorder within 7-30 days</span>
                </div>
                <div className="flex items-start">
                  <span className="text-purple-600 mr-2">•</span>
                  <span className="text-gray-700"><strong>Category preferences:</strong> Fresh produce and dairy have highest turnover</span>
                </div>
                <div className="flex items-start">
                  <span className="text-purple-600 mr-2">•</span>
                  <span className="text-gray-700"><strong>Customer segments:</strong> 20% of customers drive 60% of repeat purchases</span>
                </div>
              </div>
              <div className="mt-4 p-3 bg-purple-100 rounded-lg">
                <p className="text-purple-900 font-medium">
                  💡 This analysis provides the foundation for implementing predictive demand forecasting and optimized inventory management.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstacartAnalysis;
