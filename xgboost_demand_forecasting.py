#!/usr/bin/env python3
"""
XGBoost Demand Forecasting Script
Predicts product reorder probabilities and converts them to demand forecasts
"""

import argparse
import pandas as pd
import numpy as np
import json
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Custom JSON encoder to handle numpy types
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

# XGBoost and ML libraries
import xgboost as xgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, precision_score, recall_score
from sklearn.preprocessing import LabelEncoder
import joblib

class XGBoostDemandForecaster:
    def __init__(self, output_path='./models_output'):
        self.output_path = output_path
        self.model = None
        self.feature_columns = [
            'total_product_orders_by_user',
            'total_product_reorders_by_user', 
            'user_product_reorder_percentage',
            'avg_add_to_cart_order',
            'avg_days_since_last_bought',
            'last_order_dow',
            'last_order_hour',
            'order_count',
            'aisle_id',
            'department_id'
        ]
        
    def load_and_prepare_data(self, data_path):
        """Load and prepare feature data for XGBoost training"""
        print(f"Loading data from: {data_path}")
        
        # Load the CSV data
        df = pd.read_csv(data_path)
        print(f"Loaded {len(df)} records")
        
        # Create target variable (reorder probability)
        df['reorder_target'] = (df['reorder_count'] > 0).astype(int)
        
        # Handle missing values
        df = df.fillna(0)
        
        # Ensure all feature columns exist
        for col in self.feature_columns:
            if col not in df.columns:
                df[col] = 0
                
        print(f"Data preparation completed. Shape: {df.shape}")
        return df
    
    def train_model(self, df):
        """Train XGBoost model for reorder prediction"""
        print("Training XGBoost model...")
        
        # Prepare features and target
        X = df[self.feature_columns].copy()
        y = df['reorder_target']
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Train XGBoost model
        self.model = xgb.XGBClassifier(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='logloss'
        )
        
        self.model.fit(X_train, y_train)
        
        # Evaluate model
        y_pred = self.model.predict(X_test)
        y_pred_proba = self.model.predict_proba(X_test)[:, 1]
        
        performance = {
            'accuracy': accuracy_score(y_test, y_pred),
            'f1_score': f1_score(y_test, y_pred),
            'auc_score': roc_auc_score(y_test, y_pred_proba),
            'precision': precision_score(y_test, y_pred),
            'recall': recall_score(y_test, y_pred)
        }
        
        print(f"Model Performance:")
        for metric, value in performance.items():
            print(f"  {metric}: {value:.4f}")
            
        return performance
    
    def generate_forecasts(self, df, forecast_days=30):
        """Generate demand forecasts for products"""
        print(f"Generating forecasts for {forecast_days} days...")
        
        forecasts = []
        
        # Group by product to generate individual forecasts
        product_groups = df.groupby('product_id')
        
        for product_id, group in product_groups:
            try:
                # Get product information
                product_info = group.iloc[0]
                
                # Prepare features for prediction
                features = group[self.feature_columns].mean().values.reshape(1, -1)
                
                # Predict reorder probability
                reorder_prob = self.model.predict_proba(features)[0, 1]
                
                # Calculate historical average orders
                historical_avg = group['order_count'].mean()
                
                # Get feature importance for this prediction
                feature_importance = dict(zip(
                    self.feature_columns, 
                    self.model.feature_importances_
                ))
                
                forecast_data = {
                    'product_id': int(product_id),
                    'product_name': product_info['product_name'],
                    'aisle_id': int(product_info['aisle_id']),
                    'department_id': int(product_info['department_id']),
                    'reorder_probability': float(reorder_prob),
                    'historical_avg_orders': float(historical_avg),
                    'confidence_score': float(min(0.95, max(0.5, reorder_prob))),
                    'feature_importance': feature_importance,
                    'forecast_generated_at': datetime.now().isoformat()
                }
                
                forecasts.append(forecast_data)
                
            except Exception as e:
                print(f"Error generating forecast for product {product_id}: {e}")
                continue
        
        print(f"Generated forecasts for {len(forecasts)} products")
        return forecasts
    
    def save_forecasts(self, forecasts, performance):
        """Save forecasts to JSON files"""
        os.makedirs(self.output_path, exist_ok=True)
        
        # Save individual product forecasts
        for forecast in forecasts:
            filename = f"product_{forecast['product_id']}_xgboost_forecast.json"
            filepath = os.path.join(self.output_path, filename)
            
            # Add performance metrics to each forecast
            forecast_with_performance = forecast.copy()
            forecast_with_performance.update(performance)
            
            with open(filepath, 'w') as f:
                json.dump(forecast_with_performance, f, indent=2, cls=NumpyEncoder)
        
        # Save summary
        summary = {
            'total_forecasts': len(forecasts),
            'model_performance': performance,
            'forecast_generated_at': datetime.now().isoformat(),
            'model_type': 'XGBoost',
            'feature_columns': self.feature_columns
        }
        
        summary_path = os.path.join(self.output_path, 'xgboost_forecast_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, cls=NumpyEncoder)
        
        print(f"Forecasts saved to: {self.output_path}")
        
        # Save model
        model_path = os.path.join(self.output_path, 'xgboost_reorder_model.pkl')
        joblib.dump(self.model, model_path)
        print(f"Model saved to: {model_path}")

def main():
    parser = argparse.ArgumentParser(description='XGBoost Demand Forecasting')
    parser.add_argument('--data_path', required=True, help='Path to feature data CSV file')
    parser.add_argument('--forecast_days', type=int, default=30, help='Number of days to forecast')
    parser.add_argument('--output_path', default='./models_output', help='Output directory for forecasts')
    parser.add_argument('--model_type', default='xgboost', help='Model type identifier')
    
    args = parser.parse_args()
    
    try:
        # Initialize forecaster
        forecaster = XGBoostDemandForecaster(args.output_path)
        
        # Load and prepare data
        df = forecaster.load_and_prepare_data(args.data_path)
        
        # Train model
        performance = forecaster.train_model(df)
        
        # Generate forecasts
        forecasts = forecaster.generate_forecasts(df, args.forecast_days)
        
        # Save results
        forecaster.save_forecasts(forecasts, performance)
        
        print("XGBoost demand forecasting completed successfully!")
        
    except Exception as e:
        print(f"Error in XGBoost forecasting: {e}")
        raise

if __name__ == "__main__":
    main()
