const mongoose = require('mongoose');
const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');
require('dotenv').config();

// Import models
const InstacartAisle = require('../models/InstacartAisle');
const InstacartDepartment = require('../models/InstacartDepartment');
const InstacartProduct = require('../models/InstacartProduct');
const InstacartOrder = require('../models/InstacartOrder');
const InstacartOrderProduct = require('../models/InstacartOrderProduct');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/inventory_management', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('MongoDB connected successfully'))
.catch(err => console.error('MongoDB connection error:', err));

const DATA_PATH = path.join(__dirname, '../../InstarcartMarketBasketAnalysisDataset');

// Helper function to read CSV and return promise
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });
}

// Import aisles data
async function importAisles() {
  console.log('Importing aisles...');
  try {
    const aislesData = await readCSV(path.join(DATA_PATH, 'aisles.csv'));
    
    // Clear existing data
    await InstacartAisle.deleteMany({});
    
    const aisles = aislesData.map(row => ({
      aisle_id: parseInt(row.aisle_id),
      aisle: row.aisle
    }));
    
    await InstacartAisle.insertMany(aisles);
    console.log(`✓ Imported ${aisles.length} aisles`);
  } catch (error) {
    console.error('Error importing aisles:', error);
    throw error;
  }
}

// Import departments data
async function importDepartments() {
  console.log('Importing departments...');
  try {
    const departmentsData = await readCSV(path.join(DATA_PATH, 'departments.csv'));
    
    // Clear existing data
    await InstacartDepartment.deleteMany({});
    
    const departments = departmentsData.map(row => ({
      department_id: parseInt(row.department_id),
      department: row.department
    }));
    
    await InstacartDepartment.insertMany(departments);
    console.log(`✓ Imported ${departments.length} departments`);
  } catch (error) {
    console.error('Error importing departments:', error);
    throw error;
  }
}

// Import products data
async function importProducts() {
  console.log('Importing products...');
  try {
    const productsData = await readCSV(path.join(DATA_PATH, 'products.csv'));
    
    // Clear existing data
    await InstacartProduct.deleteMany({});
    
    const products = productsData.map(row => ({
      product_id: parseInt(row.product_id),
      product_name: row.product_name,
      aisle_id: parseInt(row.aisle_id),
      department_id: parseInt(row.department_id)
    }));
    
    // Insert in batches to avoid memory issues
    const batchSize = 1000;
    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);
      await InstacartProduct.insertMany(batch);
      console.log(`  Imported ${Math.min(i + batchSize, products.length)}/${products.length} products`);
    }
    
    console.log(`✓ Imported ${products.length} products`);
  } catch (error) {
    console.error('Error importing products:', error);
    throw error;
  }
}

// Import orders data (sample only due to size)
async function importOrders() {
  console.log('Importing orders (sample)...');
  try {
    const ordersData = await readCSV(path.join(DATA_PATH, 'orders.csv'));
    
    // Clear existing data
    await InstacartOrder.deleteMany({});
    
    // Take only first 10000 orders for demo purposes
    const sampleOrders = ordersData.slice(0, 10000);
    
    const orders = sampleOrders.map(row => ({
      order_id: parseInt(row.order_id),
      user_id: parseInt(row.user_id),
      eval_set: row.eval_set,
      order_number: parseInt(row.order_number),
      order_dow: parseInt(row.order_dow),
      order_hour_of_day: parseInt(row.order_hour_of_day),
      days_since_prior_order: row.days_since_prior_order ? parseFloat(row.days_since_prior_order) : null
    }));
    
    // Insert in batches
    const batchSize = 1000;
    for (let i = 0; i < orders.length; i += batchSize) {
      const batch = orders.slice(i, i + batchSize);
      await InstacartOrder.insertMany(batch);
      console.log(`  Imported ${Math.min(i + batchSize, orders.length)}/${orders.length} orders`);
    }
    
    console.log(`✓ Imported ${orders.length} orders (sample)`);
    return orders.map(o => o.order_id);
  } catch (error) {
    console.error('Error importing orders:', error);
    throw error;
  }
}

// Import order products data (for sample orders only)
async function importOrderProducts(sampleOrderIds) {
  console.log('Importing order products (for sample orders)...');
  try {
    const orderProductsData = await readCSV(path.join(DATA_PATH, 'order_products__prior.csv'));
    
    // Clear existing data
    await InstacartOrderProduct.deleteMany({});
    
    // Filter only for our sample orders
    const sampleOrderProducts = orderProductsData.filter(row => 
      sampleOrderIds.includes(parseInt(row.order_id))
    );
    
    const orderProducts = sampleOrderProducts.map(row => ({
      order_id: parseInt(row.order_id),
      product_id: parseInt(row.product_id),
      add_to_cart_order: parseInt(row.add_to_cart_order),
      reordered: parseInt(row.reordered)
    }));
    
    // Insert in batches
    const batchSize = 1000;
    for (let i = 0; i < orderProducts.length; i += batchSize) {
      const batch = orderProducts.slice(i, i + batchSize);
      await InstacartOrderProduct.insertMany(batch);
      console.log(`  Imported ${Math.min(i + batchSize, orderProducts.length)}/${orderProducts.length} order products`);
    }
    
    console.log(`✓ Imported ${orderProducts.length} order products`);
  } catch (error) {
    console.error('Error importing order products:', error);
    throw error;
  }
}

// Main import function
async function importAllData() {
  try {
    console.log('Starting Instacart data import...');
    console.log('=====================================');
    
    await importAisles();
    await importDepartments();
    await importProducts();
    const sampleOrderIds = await importOrders();
    await importOrderProducts(sampleOrderIds);
    
    console.log('=====================================');
    console.log('✓ All Instacart data imported successfully!');
    
    // Display summary
    const aisleCount = await InstacartAisle.countDocuments();
    const departmentCount = await InstacartDepartment.countDocuments();
    const productCount = await InstacartProduct.countDocuments();
    const orderCount = await InstacartOrder.countDocuments();
    const orderProductCount = await InstacartOrderProduct.countDocuments();
    
    console.log('\nImport Summary:');
    console.log(`- Aisles: ${aisleCount}`);
    console.log(`- Departments: ${departmentCount}`);
    console.log(`- Products: ${productCount}`);
    console.log(`- Orders: ${orderCount}`);
    console.log(`- Order Products: ${orderProductCount}`);
    
  } catch (error) {
    console.error('Import failed:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the import
if (require.main === module) {
  importAllData();
}

module.exports = { importAllData };
