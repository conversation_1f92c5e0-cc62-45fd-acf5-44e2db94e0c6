require('dotenv').config();
const GeminiService = require('../services/geminiService');

async function demonstrateGeminiIntegration() {
  console.log('🤖 Gemini AI Integration Demonstration\n');
  console.log('=' .repeat(60));
  
  const geminiService = new GeminiService();
  
  // Check if Gemini is available
  if (!geminiService.isAvailable()) {
    console.log('❌ Gemini API key not configured');
    console.log('Please set GEMINI_API_KEY in your .env file');
    return;
  }
  
  console.log('✅ Gemini API is available and configured\n');
  
  // Test different types of queries
  const testCases = [
    {
      title: "General AI Question",
      description: "Testing how Gemini handles general questions with inventory context",
      query: "How does artificial intelligence work?",
      method: "handleGeneralQuestion"
    },
    {
      title: "Inventory Management Advice",
      description: "Testing business consultation capabilities",
      query: "What are the best practices for inventory management?",
      method: "handleGeneralQuestion"
    },
    {
      title: "Product Recommendation",
      description: "Testing product analysis and recommendations",
      query: "Recommend the best coffee products for a grocery store",
      method: "generateProductRecommendations",
      data: {
        recommendations: [
          { title: "Premium Arabica Coffee Beans", rating: 4.8, reviews: 1250, sentiment: "positive" },
          { title: "Organic Fair Trade Coffee", rating: 4.6, reviews: 890, sentiment: "positive" },
          { title: "Dark Roast Espresso Blend", rating: 4.7, reviews: 2100, sentiment: "positive" }
        ]
      }
    },
    {
      title: "Inventory Data Analysis",
      description: "Testing data pattern explanation",
      query: "Analyze seasonal trends in product sales",
      method: "explainDataPatterns",
      data: {
        monthly_sales: [
          { month: "Jan", sales: 15000, top_category: "beverages" },
          { month: "Feb", sales: 12000, top_category: "snacks" },
          { month: "Mar", sales: 18000, top_category: "beverages" }
        ]
      },
      analysisType: "sales_trends"
    },
    {
      title: "Supply Chain Optimization",
      description: "Testing business strategy advice",
      query: "How can I optimize my supply chain for better efficiency?",
      method: "handleGeneralQuestion"
    }
  ];
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    
    console.log(`\n${i + 1}. ${testCase.title}`);
    console.log('-'.repeat(40));
    console.log(`Description: ${testCase.description}`);
    console.log(`Query: "${testCase.query}"`);
    console.log(`Method: ${testCase.method}\n`);
    
    try {
      let response;
      const startTime = Date.now();
      
      switch (testCase.method) {
        case "handleGeneralQuestion":
          response = await geminiService.handleGeneralQuestion(testCase.query);
          break;
        case "generateProductRecommendations":
          response = await geminiService.generateProductRecommendations(testCase.query, testCase.data);
          break;
        case "explainDataPatterns":
          response = await geminiService.explainDataPatterns(testCase.data, testCase.analysisType);
          break;
        default:
          response = await geminiService.generateResponse(testCase.query);
      }
      
      const processingTime = Date.now() - startTime;
      
      if (response && response.text) {
        console.log('✅ Response Generated Successfully:');
        console.log(`Source: ${response.source || 'Gemini'}`);
        console.log(`Confidence: ${Math.round((response.confidence || 0.8) * 100)}%`);
        console.log(`Processing Time: ${processingTime}ms`);
        console.log('\n📝 Response:');
        console.log('-'.repeat(40));
        
        // Display first 400 characters of response
        const displayText = response.text.length > 400 
          ? response.text.substring(0, 400) + '...\n[Response truncated for display]'
          : response.text;
        
        console.log(displayText);
        
        // Show key insights if it's a long response
        if (response.text.length > 400) {
          const lines = response.text.split('\n');
          const keyPoints = lines.filter(line => 
            line.includes('•') || 
            line.includes('-') || 
            line.includes('1.') || 
            line.includes('2.') || 
            line.includes('3.')
          ).slice(0, 3);
          
          if (keyPoints.length > 0) {
            console.log('\n🔑 Key Points:');
            keyPoints.forEach(point => console.log(`  ${point.trim()}`));
          }
        }
        
      } else {
        console.log('❌ No response generated');
      }
      
    } catch (error) {
      console.log('❌ Error:', error.message);
    }
    
    console.log('\n' + '=' .repeat(60));
    
    // Add delay between requests to respect rate limits
    if (i < testCases.length - 1) {
      console.log('⏳ Waiting 2 seconds before next request...\n');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('\n🎉 Gemini Integration Demonstration Completed!\n');
  
  console.log('📊 Summary of Capabilities Demonstrated:');
  console.log('✅ General Question Handling with Context');
  console.log('✅ Business Consultation and Advice');
  console.log('✅ Product Analysis and Recommendations');
  console.log('✅ Data Pattern Analysis and Insights');
  console.log('✅ Supply Chain and Operations Guidance');
  console.log('✅ Intelligent Response Generation');
  console.log('✅ Context-Aware Business Focus');
  
  console.log('\n🚀 Next Steps:');
  console.log('1. Start the backend server with: npm run dev');
  console.log('2. Start the frontend application');
  console.log('3. Navigate to the AI Chatbot page');
  console.log('4. Test the enhanced chatbot with various questions');
  console.log('5. Look for the "AI Enhanced" indicator on responses');
  
  console.log('\n💡 Tips for Testing:');
  console.log('• Try both inventory-specific and general questions');
  console.log('• Notice the difference in response quality and depth');
  console.log('• Check the confidence scores and processing times');
  console.log('• Look for the sparkles icon indicating AI-enhanced responses');
}

// Run the demonstration
if (require.main === module) {
  demonstrateGeminiIntegration().catch(console.error);
}

module.exports = demonstrateGeminiIntegration;
