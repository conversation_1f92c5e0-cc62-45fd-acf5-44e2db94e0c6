const fs = require('fs').promises;
const path = require('path');
const csv = require('csv-parser');
const { createReadStream } = require('fs');
const AmazonReview = require('../models/AmazonReview');
const AmazonProduct = require('../models/AmazonProduct');

class AmazonReviewsService {
  constructor() {
    this.dataPath = path.join(__dirname, '../../amazonFinefoodReview');
    this.batchSize = 1000;
    this.processingStats = {
      totalProcessed: 0,
      totalErrors: 0,
      startTime: null,
      endTime: null
    };
  }

  /**
   * Import Amazon Fine Food Reviews from CSV file
   */
  async importReviewsFromCSV(csvFilePath) {
    try {
      console.log('Starting Amazon Fine Food Reviews import...');
      this.processingStats.startTime = new Date();
      
      const fullPath = path.join(this.dataPath, csvFilePath);
      
      // Check if file exists
      try {
        await fs.access(fullPath);
      } catch (error) {
        throw new Error(`CSV file not found: ${fullPath}`);
      }

      const reviews = [];
      let processedCount = 0;

      return new Promise((resolve, reject) => {
        createReadStream(fullPath)
          .pipe(csv())
          .on('data', (row) => {
            try {
              const review = this.parseReviewRow(row);
              reviews.push(review);

              if (reviews.length >= this.batchSize) {
                this.processBatch(reviews.splice(0, this.batchSize))
                  .then(() => {
                    processedCount += this.batchSize;
                    console.log(`Processed ${processedCount} reviews...`);
                  })
                  .catch(error => {
                    console.error('Error processing batch:', error);
                    this.processingStats.totalErrors++;
                  });
              }
            } catch (error) {
              console.error('Error parsing row:', error);
              this.processingStats.totalErrors++;
            }
          })
          .on('end', async () => {
            try {
              // Process remaining reviews
              if (reviews.length > 0) {
                await this.processBatch(reviews);
                processedCount += reviews.length;
              }

              this.processingStats.totalProcessed = processedCount;
              this.processingStats.endTime = new Date();
              
              console.log(`✓ Import completed. Processed ${processedCount} reviews`);
              console.log(`Processing time: ${this.processingStats.endTime - this.processingStats.startTime}ms`);
              
              resolve({
                totalProcessed: processedCount,
                totalErrors: this.processingStats.totalErrors,
                processingTime: this.processingStats.endTime - this.processingStats.startTime
              });
            } catch (error) {
              reject(error);
            }
          })
          .on('error', (error) => {
            reject(error);
          });
      });
    } catch (error) {
      console.error('Error importing reviews:', error);
      throw error;
    }
  }

  /**
   * Parse a single review row from CSV
   */
  parseReviewRow(row) {
    return {
      review_id: row.Id || `review_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      product_id: row.ProductId,
      user_id: row.UserId,
      profile_name: row.ProfileName || 'Anonymous',
      helpfulness_numerator: parseInt(row.HelpfulnessNumerator) || 0,
      helpfulness_denominator: parseInt(row.HelpfulnessDenominator) || 0,
      score: parseInt(row.Score) || 3,
      time: new Date(parseInt(row.Time) * 1000), // Convert Unix timestamp
      summary: row.Summary || '',
      text: row.Text || '',
      // Calculate basic features
      text_length: (row.Text || '').length,
      word_count: (row.Text || '').split(/\s+/).length,
      helpfulness_ratio: parseInt(row.HelpfulnessDenominator) > 0 
        ? parseInt(row.HelpfulnessNumerator) / parseInt(row.HelpfulnessDenominator) 
        : 0
    };
  }

  /**
   * Process a batch of reviews
   */
  async processBatch(reviews) {
    try {
      // Use insertMany with ordered: false to continue on errors
      await AmazonReview.insertMany(reviews, { ordered: false });
    } catch (error) {
      // Handle duplicate key errors gracefully
      if (error.code === 11000) {
        console.log('Some reviews already exist, skipping duplicates...');
      } else {
        throw error;
      }
    }
  }

  /**
   * Perform text processing and sentiment analysis on reviews
   */
  async processReviewsText(limit = 1000) {
    try {
      console.log('Starting text processing for reviews...');
      
      const unprocessedReviews = await AmazonReview.find({ 
        is_processed: false 
      }).limit(limit);

      console.log(`Found ${unprocessedReviews.length} unprocessed reviews`);

      let processedCount = 0;
      for (const review of unprocessedReviews) {
        try {
          await this.processReviewText(review);
          processedCount++;
          
          if (processedCount % 100 === 0) {
            console.log(`Processed ${processedCount}/${unprocessedReviews.length} reviews`);
          }
        } catch (error) {
          console.error(`Error processing review ${review.review_id}:`, error);
        }
      }

      console.log(`✓ Text processing completed. Processed ${processedCount} reviews`);
      return { processedCount, totalFound: unprocessedReviews.length };
    } catch (error) {
      console.error('Error in text processing:', error);
      throw error;
    }
  }

  /**
   * Process text for a single review
   */
  async processReviewText(review) {
    try {
      // Basic text preprocessing
      review.processText();
      
      // Simple sentiment analysis based on score and keywords
      review.sentiment_score = this.calculateSentimentScore(review);
      review.sentiment_label = this.getSentimentLabel(review.sentiment_score);
      
      // Extract keywords
      review.keywords = this.extractKeywords(review.text);
      
      // Categorize review
      review.review_category = this.categorizeReview(review.text, review.summary);
      
      await review.save();
    } catch (error) {
      console.error('Error processing review text:', error);
      throw error;
    }
  }

  /**
   * Calculate sentiment score based on rating and text analysis
   */
  calculateSentimentScore(review) {
    // Base sentiment from rating (1-5 scale to -1 to 1 scale)
    let sentimentScore = (review.score - 3) / 2; // Maps 1->-1, 3->0, 5->1
    
    // Adjust based on text content (simple keyword-based approach)
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'perfect', 'best', 'wonderful', 'delicious', 'tasty'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'worst', 'horrible', 'disgusting', 'nasty', 'poor', 'disappointing'];
    
    const text = review.text.toLowerCase();
    let positiveCount = 0;
    let negativeCount = 0;
    
    positiveWords.forEach(word => {
      if (text.includes(word)) positiveCount++;
    });
    
    negativeWords.forEach(word => {
      if (text.includes(word)) negativeCount++;
    });
    
    // Adjust sentiment based on keyword counts
    const keywordAdjustment = (positiveCount - negativeCount) * 0.1;
    sentimentScore = Math.max(-1, Math.min(1, sentimentScore + keywordAdjustment));
    
    return Math.round(sentimentScore * 100) / 100;
  }

  /**
   * Get sentiment label from score
   */
  getSentimentLabel(sentimentScore) {
    if (sentimentScore > 0.2) return 'positive';
    if (sentimentScore < -0.2) return 'negative';
    return 'neutral';
  }

  /**
   * Extract keywords from text using simple frequency analysis
   */
  extractKeywords(text, maxKeywords = 10) {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'my', 'your', 'his', 'her', 'its', 'our', 'their'
    ]);
    
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word));
    
    const wordFreq = {};
    words.forEach(word => {
      wordFreq[word] = (wordFreq[word] || 0) + 1;
    });
    
    return Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, maxKeywords)
      .map(([word, frequency]) => ({
        word,
        frequency,
        tfidf_score: frequency / words.length // Simple TF score
      }));
  }

  /**
   * Categorize review based on content
   */
  categorizeReview(text, summary) {
    const categories = {
      food_quality: ['quality', 'fresh', 'stale', 'expired', 'spoiled'],
      taste: ['taste', 'flavor', 'delicious', 'bland', 'sweet', 'salty', 'bitter'],
      packaging: ['package', 'packaging', 'box', 'container', 'wrapped', 'sealed'],
      delivery: ['delivery', 'shipping', 'arrived', 'fast', 'slow', 'damaged'],
      price: ['price', 'expensive', 'cheap', 'value', 'cost', 'money', 'worth']
    };
    
    const combinedText = (text + ' ' + summary).toLowerCase();
    let maxScore = 0;
    let bestCategory = 'general';
    
    Object.entries(categories).forEach(([category, keywords]) => {
      const score = keywords.reduce((sum, keyword) => {
        return sum + (combinedText.includes(keyword) ? 1 : 0);
      }, 0);
      
      if (score > maxScore) {
        maxScore = score;
        bestCategory = category;
      }
    });
    
    return bestCategory;
  }

  /**
   * Generate product insights from reviews
   */
  async generateProductInsights(productId) {
    try {
      const reviews = await AmazonReview.find({ 
        product_id: productId,
        is_processed: true 
      });

      if (reviews.length === 0) {
        return null;
      }

      // Calculate aggregated metrics
      const insights = {
        total_reviews: reviews.length,
        average_rating: reviews.reduce((sum, r) => sum + r.score, 0) / reviews.length,
        sentiment_distribution: {
          positive: reviews.filter(r => r.sentiment_label === 'positive').length,
          neutral: reviews.filter(r => r.sentiment_label === 'neutral').length,
          negative: reviews.filter(r => r.sentiment_label === 'negative').length
        },
        top_keywords: this.getTopKeywordsFromReviews(reviews),
        common_themes: this.getCommonThemes(reviews),
        strengths: this.extractStrengths(reviews),
        weaknesses: this.extractWeaknesses(reviews)
      };

      // Update or create product record
      await this.updateProductInsights(productId, insights);

      return insights;
    } catch (error) {
      console.error('Error generating product insights:', error);
      throw error;
    }
  }

  /**
   * Get top keywords from multiple reviews
   */
  getTopKeywordsFromReviews(reviews, limit = 20) {
    const allKeywords = {};
    
    reviews.forEach(review => {
      if (review.keywords) {
        review.keywords.forEach(keyword => {
          if (allKeywords[keyword.word]) {
            allKeywords[keyword.word].frequency += keyword.frequency;
            allKeywords[keyword.word].count++;
          } else {
            allKeywords[keyword.word] = {
              frequency: keyword.frequency,
              count: 1,
              sentiment: review.sentiment_label
            };
          }
        });
      }
    });

    return Object.entries(allKeywords)
      .map(([word, data]) => ({
        word,
        frequency: data.frequency,
        sentiment: data.sentiment
      }))
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, limit);
  }

  /**
   * Extract common themes from reviews
   */
  getCommonThemes(reviews) {
    const themes = {};
    
    reviews.forEach(review => {
      const category = review.review_category;
      if (!themes[category]) {
        themes[category] = {
          frequency: 0,
          sentiment_scores: []
        };
      }
      themes[category].frequency++;
      themes[category].sentiment_scores.push(review.sentiment_score);
    });

    return Object.entries(themes)
      .map(([theme, data]) => ({
        theme,
        frequency: data.frequency,
        sentiment_score: data.sentiment_scores.reduce((sum, score) => sum + score, 0) / data.sentiment_scores.length
      }))
      .sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * Extract product strengths from positive reviews
   */
  extractStrengths(reviews) {
    const positiveReviews = reviews.filter(r => r.sentiment_label === 'positive');
    const strengthKeywords = {};
    
    positiveReviews.forEach(review => {
      if (review.keywords) {
        review.keywords.forEach(keyword => {
          strengthKeywords[keyword.word] = (strengthKeywords[keyword.word] || 0) + keyword.frequency;
        });
      }
    });

    return Object.entries(strengthKeywords)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  /**
   * Extract product weaknesses from negative reviews
   */
  extractWeaknesses(reviews) {
    const negativeReviews = reviews.filter(r => r.sentiment_label === 'negative');
    const weaknessKeywords = {};
    
    negativeReviews.forEach(review => {
      if (review.keywords) {
        review.keywords.forEach(keyword => {
          weaknessKeywords[keyword.word] = (weaknessKeywords[keyword.word] || 0) + keyword.frequency;
        });
      }
    });

    return Object.entries(weaknessKeywords)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  /**
   * Update product insights in database
   */
  async updateProductInsights(productId, insights) {
    try {
      const existingProduct = await AmazonProduct.findOne({ product_id: productId });
      
      if (existingProduct) {
        // Update existing product
        existingProduct.total_reviews = insights.total_reviews;
        existingProduct.average_rating = Math.round(insights.average_rating * 100) / 100;
        existingProduct.sentiment_distribution = insights.sentiment_distribution;
        existingProduct.top_keywords = insights.top_keywords;
        existingProduct.common_themes = insights.common_themes;
        existingProduct.strengths = insights.strengths;
        existingProduct.weaknesses = insights.weaknesses;
        existingProduct.recommendation_score = existingProduct.calculateRecommendationScore();
        existingProduct.is_analyzed = true;
        existingProduct.last_analyzed = new Date();
        
        await existingProduct.save();
      } else {
        // Create new product record
        const newProduct = new AmazonProduct({
          product_id: productId,
          title: `Product ${productId}`, // This would be updated when product data is available
          total_reviews: insights.total_reviews,
          average_rating: Math.round(insights.average_rating * 100) / 100,
          sentiment_distribution: insights.sentiment_distribution,
          top_keywords: insights.top_keywords,
          common_themes: insights.common_themes,
          strengths: insights.strengths,
          weaknesses: insights.weaknesses,
          is_analyzed: true,
          last_analyzed: new Date()
        });
        
        newProduct.recommendation_score = newProduct.calculateRecommendationScore();
        await newProduct.save();
      }
    } catch (error) {
      console.error('Error updating product insights:', error);
      throw error;
    }
  }

  /**
   * Get processing statistics
   */
  getProcessingStats() {
    return this.processingStats;
  }
}

module.exports = AmazonReviewsService;