const mongoose = require('mongoose');
const XGBoostForecastingService = require('./services/xgboostForecastingService');

async function testXGBoostForecasting() {
  try {
    console.log('🚀 Testing XGBoost Demand Forecasting...');
    
    // Connect to database
    require('dotenv').config();
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/inventory_management';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
    
    // Initialize XGBoost forecasting service
    const xgboostService = new XGBoostForecastingService();
    
    // Test feature data preparation
    console.log('📊 Preparing XGBoost features...');
    const featureData = await xgboostService.prepareXGBoostFeatures();
    
    console.log(`✅ Prepared ${featureData.length} feature records for XGBoost`);
    
    if (featureData.length > 0) {
      console.log('📋 Sample feature data:');
      console.log(JSON.stringify(featureData[0], null, 2));
      
      // Test a small forecast generation
      console.log('🔮 Testing forecast generation with limited data...');
      const limitedProductIds = featureData.slice(0, 5).map(f => f.product_id);
      
      try {
        const forecasts = await xgboostService.generateDemandForecasts(limitedProductIds, {
          forecastDays: 7
        });
        
        console.log(`✅ Generated ${forecasts.length} XGBoost forecasts successfully!`);
        
        if (forecasts.length > 0) {
          console.log('📈 Sample forecast:');
          console.log(`Product: ${forecasts[0].product_name}`);
          console.log(`Model Type: ${forecasts[0].model_type}`);
          console.log(`Forecast Points: ${forecasts[0].forecast_data.length}`);
        }
        
      } catch (forecastError) {
        console.log('⚠️ Forecast generation test skipped (Python dependencies may be missing)');
        console.log('Error:', forecastError.message);
      }
    }
    
    console.log('🎉 XGBoost forecasting service test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testXGBoostForecasting();
