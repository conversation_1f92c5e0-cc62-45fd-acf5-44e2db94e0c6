{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Modeling"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using TensorFlow backend.\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import gc\n", "pd.options.mode.chained_assignment = None\n", "\n", "root = 'C:/Data/instacart-market-basket-analysis/'\n", "\n", "from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score\n", "from sklearn.metrics import confusion_matrix, accuracy_score, classification_report\n", "from sklearn.metrics import roc_auc_score, roc_curve, precision_score, recall_score, f1_score\n", "from imblearn.over_sampling import SMOTE"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>product_id</th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>...</th>\n", "      <th>total_reorders_by_user</th>\n", "      <th>reorder_propotion_by_user</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>orders_3</th>\n", "      <th>orders_2</th>\n", "      <th>orders_1</th>\n", "      <th>reorder_3</th>\n", "      <th>reorder_2</th>\n", "      <th>reorder_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>196</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>1.400000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>10258</td>\n", "      <td>9.0</td>\n", "      <td>8.0</td>\n", "      <td>0.888889</td>\n", "      <td>3.333333</td>\n", "      <td>19.555555</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10326</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>12427</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.900000</td>\n", "      <td>3.300000</td>\n", "      <td>17.600000</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13032</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>0.666667</td>\n", "      <td>6.333333</td>\n", "      <td>21.666666</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>41.0</td>\n", "      <td>0.694915</td>\n", "      <td>5.9</td>\n", "      <td>0.705833</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666667</td>\n", "      <td>1.0</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 71 columns</p>\n", "</div>"], "text/plain": ["   user_id  product_id  total_product_orders_by_user  \\\n", "0        1         196                          10.0   \n", "1        1       10258                           9.0   \n", "2        1       10326                           1.0   \n", "3        1       12427                          10.0   \n", "4        1       13032                           3.0   \n", "\n", "   total_product_reorders_by_user  user_product_reorder_percentage  \\\n", "0                             9.0                         0.900000   \n", "1                             8.0                         0.888889   \n", "2                             0.0                         0.000000   \n", "3                             9.0                         0.900000   \n", "4                             2.0                         0.666667   \n", "\n", "   avg_add_to_cart_by_user  avg_days_since_last_bought  last_ordered_in  \\\n", "0                 1.400000                   17.600000             10.0   \n", "1                 3.333333                   19.555555             10.0   \n", "2                 5.000000                   28.000000              5.0   \n", "3                 3.300000                   17.600000             10.0   \n", "4                 6.333333                   21.666666             10.0   \n", "\n", "   is_reorder_3  is_reorder_2  ...  total_reorders_by_user  \\\n", "0           1.0           1.0  ...                    41.0   \n", "1           1.0           1.0  ...                    41.0   \n", "2           0.0           0.0  ...                    41.0   \n", "3           1.0           1.0  ...                    41.0   \n", "4           1.0           0.0  ...                    41.0   \n", "\n", "   reorder_propotion_by_user  average_order_size  reorder_in_order  orders_3  \\\n", "0                   0.694915                 5.9          0.705833         6   \n", "1                   0.694915                 5.9          0.705833         6   \n", "2                   0.694915                 5.9          0.705833         6   \n", "3                   0.694915                 5.9          0.705833         6   \n", "4                   0.694915                 5.9          0.705833         6   \n", "\n", "   orders_2  orders_1  reorder_3  reorder_2  reorder_1  \n", "0         6         9   0.666667        1.0   0.666667  \n", "1         6         9   0.666667        1.0   0.666667  \n", "2         6         9   0.666667        1.0   0.666667  \n", "3         6         9   0.666667        1.0   0.666667  \n", "4         6         9   0.666667        1.0   0.666667  \n", "\n", "[5 rows x 71 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_pickle(root + 'Finaldata.pkl')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def reduce_memory(df):\n", "    \n", "    \"\"\"\n", "    This function reduce the dataframe memory usage by converting it's type for easier handling.\n", "    \n", "    Parameters: Dataframe\n", "    Return: Dataframe\n", "    \"\"\"\n", "    \n", "    start_mem_usg = df.memory_usage().sum() / 1024**2 \n", "    print(\"Memory usage of properties dataframe is :\",start_mem_usg,\" MB\")\n", "    \n", "    for col in df.columns:\n", "        if df[col].dtypes in [\"int64\", \"int32\", \"int16\"]:\n", "            \n", "            cmin = df[col].min()\n", "            cmax = df[col].max()\n", "            \n", "            if cmin > np.iinfo(np.int8).min and cmax < np.iinfo(np.int8).max:\n", "                df[col] = df[col].astype(np.int8)\n", "            \n", "            elif cmin > np.iinfo(np.int16).min and cmax < np.iinfo(np.int16).max:\n", "                df[col] = df[col].astype(np.int16)\n", "            \n", "            elif cmin > np.iinfo(np.int32).min and cmax < np.iinfo(np.int32).max:\n", "                df[col] = df[col].astype(np.int32)\n", "        \n", "        if df[col].dtypes in [\"float64\", \"float32\"]:\n", "            \n", "            cmin = df[col].min()\n", "            cmax = df[col].max()\n", "            \n", "            if cmin > np.finfo(np.float16).min and cmax < np.finfo(np.float16).max:\n", "                df[col] = df[col].astype(np.float16)\n", "            \n", "            elif cmin > np.finfo(np.float32).min and cmax < np.finfo(np.float32).max:\n", "                df[col] = df[col].astype(np.float32)\n", "    \n", "    print(\"\")\n", "    print(\"___MEMORY USAGE AFTER COMPLETION:___\")\n", "    mem_usg = df.memory_usage().sum() / 1024**2 \n", "    print(\"Memory usage is: \",mem_usg,\" MB\")\n", "    print(\"This is \",100*mem_usg/start_mem_usg,\"% of the initial size\")\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Memory usage of properties dataframe is : 4428.972461700439  MB\n", "\n", "___MEMORY USAGE AFTER COMPLETION:___\n", "Memory usage is:  1293.1306457519531  MB\n", "This is  29.197080291970803 % of the initial size\n"]}], "source": ["df = reduce_memory(df)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["df['order_diff'] = df.order_number - df.last_ordered_in\n", "df.drop(['user_id', 'product_id'], axis = 1, inplace = True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>total_product_orders_by_user</th>\n", "      <th>total_product_reorders_by_user</th>\n", "      <th>user_product_reorder_percentage</th>\n", "      <th>avg_add_to_cart_by_user</th>\n", "      <th>avg_days_since_last_bought</th>\n", "      <th>last_ordered_in</th>\n", "      <th>is_reorder_3</th>\n", "      <th>is_reorder_2</th>\n", "      <th>is_reorder_1</th>\n", "      <th>order_number</th>\n", "      <th>...</th>\n", "      <th>reorder_propotion_by_user</th>\n", "      <th>average_order_size</th>\n", "      <th>reorder_in_order</th>\n", "      <th>orders_3</th>\n", "      <th>orders_2</th>\n", "      <th>orders_1</th>\n", "      <th>reorder_3</th>\n", "      <th>reorder_2</th>\n", "      <th>reorder_1</th>\n", "      <th>order_diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.899902</td>\n", "      <td>1.400391</td>\n", "      <td>17.593750</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>0.694824</td>\n", "      <td>5.898438</td>\n", "      <td>0.706055</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9.0</td>\n", "      <td>8.0</td>\n", "      <td>0.888672</td>\n", "      <td>3.333984</td>\n", "      <td>19.562500</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>0.694824</td>\n", "      <td>5.898438</td>\n", "      <td>0.706055</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>0.694824</td>\n", "      <td>5.898438</td>\n", "      <td>0.706055</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "      <td>0.666504</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "      <td>0.899902</td>\n", "      <td>3.300781</td>\n", "      <td>17.593750</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>0.694824</td>\n", "      <td>5.898438</td>\n", "      <td>0.706055</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>0.666504</td>\n", "      <td>6.332031</td>\n", "      <td>21.671875</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>0.694824</td>\n", "      <td>5.898438</td>\n", "      <td>0.706055</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "      <td>0.666504</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 70 columns</p>\n", "</div>"], "text/plain": ["   total_product_orders_by_user  total_product_reorders_by_user  \\\n", "0                          10.0                             9.0   \n", "1                           9.0                             8.0   \n", "2                           1.0                             0.0   \n", "3                          10.0                             9.0   \n", "4                           3.0                             2.0   \n", "\n", "   user_product_reorder_percentage  avg_add_to_cart_by_user  \\\n", "0                         0.899902                 1.400391   \n", "1                         0.888672                 3.333984   \n", "2                         0.000000                 5.000000   \n", "3                         0.899902                 3.300781   \n", "4                         0.666504                 6.332031   \n", "\n", "   avg_days_since_last_bought  last_ordered_in  is_reorder_3  is_reorder_2  \\\n", "0                   17.593750             10.0           1.0           1.0   \n", "1                   19.562500             10.0           1.0           1.0   \n", "2                   28.000000              5.0           0.0           0.0   \n", "3                   17.593750             10.0           1.0           1.0   \n", "4                   21.671875             10.0           1.0           0.0   \n", "\n", "   is_reorder_1  order_number  ...  reorder_propotion_by_user  \\\n", "0           1.0          11.0  ...                   0.694824   \n", "1           1.0          11.0  ...                   0.694824   \n", "2           0.0          11.0  ...                   0.694824   \n", "3           1.0          11.0  ...                   0.694824   \n", "4           0.0          11.0  ...                   0.694824   \n", "\n", "   average_order_size  reorder_in_order  orders_3  orders_2  orders_1  \\\n", "0            5.898438          0.706055         6         6         9   \n", "1            5.898438          0.706055         6         6         9   \n", "2            5.898438          0.706055         6         6         9   \n", "3            5.898438          0.706055         6         6         9   \n", "4            5.898438          0.706055         6         6         9   \n", "\n", "   reorder_3  reorder_2  reorder_1  order_diff  \n", "0   0.666504        1.0   0.666504         1.0  \n", "1   0.666504        1.0   0.666504         1.0  \n", "2   0.666504        1.0   0.666504         6.0  \n", "3   0.666504        1.0   0.666504         1.0  \n", "4   0.666504        1.0   0.666504         1.0  \n", "\n", "[5 rows x 70 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(8474661, 70)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["label = 'reordered'\n", "x_cols = df.columns.drop('reordered')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["X = df[x_cols]\n", "y = df[label]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(6355995, 69) (6355995,)\n", "(2118666, 69) (2118666,)\n"]}], "source": ["X_train, X_test, y_train, y_test = train_test_split(X, y, stratify = y, test_size = 0.25)\n", "\n", "print(X_train.shape, y_train.shape)\n", "print(X_test.shape, y_test.shape)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.0    5734377\n", "1.0     621618\n", "Name: reordered, dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["y_train.value_counts()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["10.0"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["np.ceil(y_train.value_counts()[0]/y_train.value_counts()[1])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.0    1911460\n", "1.0     207206\n", "Name: reordered, dtype: int64"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["y_test.value_counts()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["68"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# freeing memory\n", "del df, X, y\n", "gc.collect()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Neural Network model"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["import keras\n", "from keras.models import Sequential\n", "from keras.layers import Dense, Dropout\n", "from keras.regularizers import l2\n", "from keras.callbacks import History\n", "from keras import backend as K\n", "from sklearn.preprocessing import MinMaxScaler"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["sc = MinMaxScaler()\n", "X_train_sc = sc.fit_transform(X_train)\n", "X_test_sc = sc.transform(X_test)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["69"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["input_dim =  X_train_sc.shape[1]\n", "input_dim"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def recall_m(y_true, y_pred):\n", "    true_positives = K.sum(K.round(<PERSON><PERSON>clip(y_true * y_pred, 0, 1)))\n", "    possible_positives = K.sum(K.round(<PERSON><PERSON>clip(y_true, 0, 1)))\n", "    recall = true_positives / (possible_positives + K.epsilon())\n", "    return recall\n", "\n", "def precision_m(y_true, y_pred):\n", "    true_positives = K.sum(K.round(<PERSON><PERSON>clip(y_true * y_pred, 0, 1)))\n", "    predicted_positives = K.sum(K.round(K.clip(y_pred, 0, 1)))\n", "    precision = true_positives / (predicted_positives + K.epsilon())\n", "    return precision\n", "\n", "def f1_m(y_true, y_pred):\n", "    precision = precision_m(y_true, y_pred)\n", "    recall = recall_m(y_true, y_pred)\n", "    return 2*((precision*recall)/(precision+recall+K.epsilon()))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From C:\\Users\\<USER>\\Anaconda3\\lib\\site-packages\\tensorflow\\python\\framework\\op_def_library.py:263: colocate_with (from tensorflow.python.framework.ops) is deprecated and will be removed in a future version.\n", "Instructions for updating:\n", "Colocations handled automatically by placer.\n", "_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "dense_1 (<PERSON><PERSON>)              (None, 64)                4480      \n", "_________________________________________________________________\n", "dense_2 (<PERSON><PERSON>)              (None, 15)                975       \n", "_________________________________________________________________\n", "dense_3 (<PERSON><PERSON>)              (None, 4)                 64        \n", "_________________________________________________________________\n", "dense_4 (<PERSON><PERSON>)              (None, 1)                 5         \n", "=================================================================\n", "Total params: 5,524\n", "Trainable params: 5,524\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["history = History()\n", "\n", "classifier = Sequential()\n", "\n", "classifier.add(Den<PERSON>(units = 64, activation = 'relu', input_dim = input_dim))\n", "classifier.add(Dense(units = 15, activation = 'relu'))\n", "classifier.add(Dense(units = 4, activation = 'relu'))\n", "classifier.add(Dense(units = 1, activation ='sigmoid'))\n", "\n", "classifier.compile(optimizer = \"adam\", loss = 'binary_crossentropy', metrics = ['accuracy', f1_m, precision_m, recall_m])\n", "\n", "classifier.summary()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From C:\\Users\\<USER>\\Anaconda3\\lib\\site-packages\\tensorflow\\python\\ops\\math_ops.py:3066: to_int32 (from tensorflow.python.ops.math_ops) is deprecated and will be removed in a future version.\n", "Instructions for updating:\n", "Use tf.cast instead.\n", "Train on 5402595 samples, validate on 953400 samples\n", "Epoch 1/50\n", "5402595/5402595 [==============================] - 206s 38us/step - loss: 0.9766 - acc: 0.7298 - f1_m: 0.3567 - precision_m: 0.2346 - recall_m: 0.7627 - val_loss: 0.9573 - val_acc: 0.7362 - val_f1_m: 0.3620 - val_precision_m: 0.2379 - val_recall_m: 0.7662\n", "Epoch 2/50\n", "5402595/5402595 [==============================] - 130s 24us/step - loss: 0.9546 - acc: 0.7325 - f1_m: 0.3602 - precision_m: 0.2364 - recall_m: 0.7713 - val_loss: 0.9559 - val_acc: 0.7298 - val_f1_m: 0.3592 - val_precision_m: 0.2346 - val_recall_m: 0.7754c: 0.7319 - f1_m: 0.3601 - precision_m: 0.2362 - recall_m: 0.77 - ETA: 12s - loss: 0.9552 - acc: 0.7319 - - ETA: 11s - loss: 0.9553 - acc: 0.7319 - f1_m: 0.3600 - precision_m: 0.2361  - ET - ETA: 7s - loss: 0.9549 - acc: 0.\n", "Epoch 3/50\n", "5402595/5402595 [==============================] - 136s 25us/step - loss: 0.9520 - acc: 0.7346 - f1_m: 0.3616 - precision_m: 0.2376 - recall_m: 0.7704 - val_loss: 0.9559 - val_acc: 0.7072 - val_f1_m: 0.3484 - val_precision_m: 0.2234 - val_recall_m: 0.8010\n", "Epoch 4/50\n", "5402595/5402595 [==============================] - 126s 23us/step - loss: 0.9504 - acc: 0.7356 - f1_m: 0.3624 - precision_m: 0.2382 - recall_m: 0.7707 - val_loss: 0.9533 - val_acc: 0.7211 - val_f1_m: 0.3551 - val_precision_m: 0.2302 - val_recall_m: 0.7860\n", "Epoch 5/50\n", "5402595/5402595 [==============================] - 134s 25us/step - loss: 0.9493 - acc: 0.7371 - f1_m: 0.3634 - precision_m: 0.2390 - recall_m: 0.7696 - val_loss: 0.9531 - val_acc: 0.7566 - val_f1_m: 0.3744 - val_precision_m: 0.2509 - val_recall_m: 0.7461 0.7370 - f1_m: 0.3631 - precisio - ETA: 9s - loss:\n", "Epoch 6/50\n", "5402595/5402595 [==============================] - 111s 21us/step - loss: 0.9483 - acc: 0.7368 - f1_m: 0.3636 - precision_m: 0.2391 - recall_m: 0.7713 - val_loss: 0.9505 - val_acc: 0.7290 - val_f1_m: 0.3598 - val_precision_m: 0.2347 - val_recall_m: 0.7796\n", "Epoch 7/50\n", "5402595/5402595 [==============================] - 130s 24us/step - loss: 0.9462 - acc: 0.7380 - f1_m: 0.3646 - precision_m: 0.2398 - recall_m: 0.7715 - val_loss: 0.9511 - val_acc: 0.7689 - val_f1_m: 0.3825 - val_precision_m: 0.2598 - val_recall_m: 0.7335\n", "Epoch 8/50\n", "5402595/5402595 [==============================] - 125s 23us/step - loss: 0.9447 - acc: 0.7390 - f1_m: 0.3653 - precision_m: 0.2406 - recall_m: 0.7710 - val_loss: 0.9495 - val_acc: 0.7375 - val_f1_m: 0.3646 - val_precision_m: 0.2397 - val_recall_m: 0.7713 - f1_m:\n", "Epoch 9/50\n", "5402595/5402595 [==============================] - 115s 21us/step - loss: 0.9438 - acc: 0.7399 - f1_m: 0.3660 - precision_m: 0.2411 - recall_m: 0.7705 - val_loss: 0.9478 - val_acc: 0.7374 - val_f1_m: 0.3647 - val_precision_m: 0.2397 - val_recall_m: 0.7720\n", "Epoch 10/50\n", "5402595/5402595 [==============================] - 130s 24us/step - loss: 0.9431 - acc: 0.7407 - f1_m: 0.3667 - precision_m: 0.2417 - recall_m: 0.7704 - val_loss: 0.9504 - val_acc: 0.7696 - val_f1_m: 0.3834 - val_precision_m: 0.2606 - val_recall_m: 0.7339\n", "Epoch 11/50\n", "5402595/5402595 [==============================] - 135s 25us/step - loss: 0.9425 - acc: 0.7413 - f1_m: 0.3671 - precision_m: 0.2421 - recall_m: 0.7700 - val_loss: 0.9460 - val_acc: 0.7326 - val_f1_m: 0.3626 - val_precision_m: 0.2372 - val_recall_m: 0.7786s: 0.9425 - acc: 0.7413 - f1_m: 0.3670 - p - ETA: 2s - loss: 0.9424 - acc: 0.7414 - f1_m: 0.367\n", "Epoch 12/50\n", "5402595/5402595 [==============================] - 127s 23us/step - loss: 0.9421 - acc: 0.7413 - f1_m: 0.3671 - precision_m: 0.2421 - recall_m: 0.7702 - val_loss: 0.9482 - val_acc: 0.7092 - val_f1_m: 0.3501 - val_precision_m: 0.2248 - val_recall_m: 0.8018\n", "Epoch 13/50\n", "5402595/5402595 [==============================] - 141s 26us/step - loss: 0.9418 - acc: 0.7422 - f1_m: 0.3676 - precision_m: 0.2426 - recall_m: 0.7691 - val_loss: 0.9472 - val_acc: 0.7250 - val_f1_m: 0.3584 - val_precision_m: 0.2329 - val_recall_m: 0.7866\n", "Epoch 14/50\n", "5402595/5402595 [==============================] - 127s 23us/step - loss: 0.9415 - acc: 0.7424 - f1_m: 0.3678 - precision_m: 0.2428 - recall_m: 0.7693 - val_loss: 0.9455 - val_acc: 0.7516 - val_f1_m: 0.3733 - val_precision_m: 0.2487 - val_recall_m: 0.7578\n", "Epoch 15/50\n", "5402595/5402595 [==============================] - 135s 25us/step - loss: 0.9412 - acc: 0.7421 - f1_m: 0.3676 - precision_m: 0.2426 - recall_m: 0.7696 - val_loss: 0.9449 - val_acc: 0.7344 - val_f1_m: 0.3636 - val_precision_m: 0.2382 - val_recall_m: 0.7769\n", "Epoch 16/50\n", "5402595/5402595 [==============================] - 114s 21us/step - loss: 0.9408 - acc: 0.7421 - f1_m: 0.3678 - precision_m: 0.2427 - recall_m: 0.7703 - val_loss: 0.9448 - val_acc: 0.7449 - val_f1_m: 0.3694 - val_precision_m: 0.2444 - val_recall_m: 0.7652\n", "Epoch 17/50\n", "5402595/5402595 [==============================] - 137s 25us/step - loss: 0.9407 - acc: 0.7423 - f1_m: 0.3679 - precision_m: 0.2428 - recall_m: 0.7702 - val_loss: 0.9469 - val_acc: 0.7576 - val_f1_m: 0.3767 - val_precision_m: 0.2525 - val_recall_m: 0.7503\n", "Epoch 18/50\n", "5402595/5402595 [==============================] - 137s 25us/step - loss: 0.9404 - acc: 0.7425 - f1_m: 0.3679 - precision_m: 0.2428 - recall_m: 0.7697 - val_loss: 0.9443 - val_acc: 0.7450 - val_f1_m: 0.3696 - val_precision_m: 0.2446 - val_recall_m: 0.7654\n", "Epoch 19/50\n", "5402595/5402595 [==============================] - 128s 24us/step - loss: 0.9402 - acc: 0.7425 - f1_m: 0.3681 - precision_m: 0.2429 - recall_m: 0.7699 - val_loss: 0.9449 - val_acc: 0.7370 - val_f1_m: 0.3648 - val_precision_m: 0.2396 - val_recall_m: 0.7731\n", "Epoch 20/50\n", "5402595/5402595 [==============================] - 130s 24us/step - loss: 0.9402 - acc: 0.7426 - f1_m: 0.3681 - precision_m: 0.2430 - recall_m: 0.7697 - val_loss: 0.9440 - val_acc: 0.7404 - val_f1_m: 0.3670 - val_precision_m: 0.2418 - val_recall_m: 0.7706\n", "Epoch 21/50\n", "5402595/5402595 [==============================] - 131s 24us/step - loss: 0.9400 - acc: 0.7426 - f1_m: 0.3682 - precision_m: 0.2430 - recall_m: 0.7702 - val_loss: 0.9442 - val_acc: 0.7438 - val_f1_m: 0.3687 - val_precision_m: 0.2437 - val_recall_m: 0.7663\n", "Epoch 22/50\n", "5402595/5402595 [==============================] - 139s 26us/step - loss: 0.9399 - acc: 0.7424 - f1_m: 0.3681 - precision_m: 0.2429 - recall_m: 0.7703 - val_loss: 0.9454 - val_acc: 0.7523 - val_f1_m: 0.3738 - val_precision_m: 0.2491 - val_recall_m: 0.7569 0.2430 - recall_m: 0. - ETA: 0s - loss: 0.9400 - acc: 0.7424 - f1_m: 0.3681 - precision_m: 0.2430 -\n", "Epoch 23/50\n", "5402595/5402595 [==============================] - 126s 23us/step - loss: 0.9398 - acc: 0.7428 - f1_m: 0.3683 - precision_m: 0.2431 - recall_m: 0.7699 - val_loss: 0.9440 - val_acc: 0.7418 - val_f1_m: 0.3680 - val_precision_m: 0.2428 - val_recall_m: 0.7698\n", "Epoch 24/50\n", "5402595/5402595 [==============================] - 134s 25us/step - loss: 0.9396 - acc: 0.7425 - f1_m: 0.3681 - precision_m: 0.2429 - recall_m: 0.7704 - val_loss: 0.9450 - val_acc: 0.7236 - val_f1_m: 0.3580 - val_precision_m: 0.2324 - val_recall_m: 0.7890.9393 \n", "Epoch 25/50\n", "5402595/5402595 [==============================] - 131s 24us/step - loss: 0.9395 - acc: 0.7427 - f1_m: 0.3682 - precision_m: 0.2430 - recall_m: 0.7702 - val_loss: 0.9446 - val_acc: 0.7467 - val_f1_m: 0.3706 - val_precision_m: 0.2456 - val_recall_m: 0.7637\n", "Epoch 26/50\n", "5402595/5402595 [==============================] - 133s 25us/step - loss: 0.9395 - acc: 0.7427 - f1_m: 0.3682 - precision_m: 0.2430 - recall_m: 0.7700 - val_loss: 0.9453 - val_acc: 0.7344 - val_f1_m: 0.3639 - val_precision_m: 0.2384 - val_recall_m: 0.7778\n", "Epoch 27/50\n", "5402595/5402595 [==============================] - 114s 21us/step - loss: 0.9394 - acc: 0.7429 - f1_m: 0.3683 - precision_m: 0.2432 - recall_m: 0.7699 - val_loss: 0.9438 - val_acc: 0.7450 - val_f1_m: 0.3697 - val_precision_m: 0.2446 - val_recall_m: 0.7657\n", "Epoch 28/50\n"]}, {"name": "stdout", "output_type": "stream", "text": ["5402595/5402595 [==============================] - 118s 22us/step - loss: 0.9392 - acc: 0.7431 - f1_m: 0.3685 - precision_m: 0.2433 - recall_m: 0.7700 - val_loss: 0.9485 - val_acc: 0.7700 - val_f1_m: 0.3841 - val_precision_m: 0.2612 - val_recall_m: 0.7346\n", "Epoch 29/50\n", "5402595/5402595 [==============================] - 128s 24us/step - loss: 0.9390 - acc: 0.7430 - f1_m: 0.3685 - precision_m: 0.2432 - recall_m: 0.7702 - val_loss: 0.9458 - val_acc: 0.7216 - val_f1_m: 0.3569 - val_precision_m: 0.2313 - val_recall_m: 0.7909\n", "Epoch 30/50\n", "5402595/5402595 [==============================] - 129s 24us/step - loss: 0.9391 - acc: 0.7431 - f1_m: 0.3686 - precision_m: 0.2434 - recall_m: 0.7700 - val_loss: 0.9454 - val_acc: 0.7274 - val_f1_m: 0.3597 - val_precision_m: 0.2342 - val_recall_m: 0.7838- precision_m: 0.2433 - recall_m: - ETA: 4s - lo\n", "Epoch 31/50\n", "5402595/5402595 [==============================] - 118s 22us/step - loss: 0.9389 - acc: 0.7429 - f1_m: 0.3683 - precision_m: 0.2431 - recall_m: 0.7699 - val_loss: 0.9450 - val_acc: 0.7427 - val_f1_m: 0.3683 - val_precision_m: 0.2432 - val_recall_m: 0.7680\n", "Epoch 32/50\n", "5402595/5402595 [==============================] - 115s 21us/step - loss: 0.9387 - acc: 0.7430 - f1_m: 0.3685 - precision_m: 0.2432 - recall_m: 0.7701 - val_loss: 0.9466 - val_acc: 0.7655 - val_f1_m: 0.3812 - val_precision_m: 0.2578 - val_recall_m: 0.7397\n", "Epoch 33/50\n", "5402595/5402595 [==============================] - 118s 22us/step - loss: 0.9387 - acc: 0.7433 - f1_m: 0.3688 - precision_m: 0.2435 - recall_m: 0.7702 - val_loss: 0.9444 - val_acc: 0.7548 - val_f1_m: 0.3753 - val_precision_m: 0.2508 - val_recall_m: 0.7543\n", "Epoch 34/50\n", "5402595/5402595 [==============================] - 132s 24us/step - loss: 0.9387 - acc: 0.7434 - f1_m: 0.3689 - precision_m: 0.2436 - recall_m: 0.7703 - val_loss: 0.9440 - val_acc: 0.7335 - val_f1_m: 0.3634 - val_precision_m: 0.2379 - val_recall_m: 0.7790\n", "Epoch 35/50\n", "5402595/5402595 [==============================] - 135s 25us/step - loss: 0.9385 - acc: 0.7430 - f1_m: 0.3686 - precision_m: 0.2433 - recall_m: 0.7705 - val_loss: 0.9443 - val_acc: 0.7499 - val_f1_m: 0.3725 - val_precision_m: 0.2477 - val_recall_m: 0.7601\n", "Epoch 36/50\n", "5402595/5402595 [==============================] - 108s 20us/step - loss: 0.9385 - acc: 0.7430 - f1_m: 0.3685 - precision_m: 0.2433 - recall_m: 0.7700 - val_loss: 0.9463 - val_acc: 0.7191 - val_f1_m: 0.3559 - val_precision_m: 0.2301 - val_recall_m: 0.7946m: 0.2433 - recal - ETA: 1s - loss: 0.9384 - acc: 0.7431 - f1_m: 0.3\n", "Epoch 37/50\n", "5402595/5402595 [==============================] - 141s 26us/step - loss: 0.9384 - acc: 0.7432 - f1_m: 0.3688 - precision_m: 0.2434 - recall_m: 0.7705 - val_loss: 0.9449 - val_acc: 0.7253 - val_f1_m: 0.3591 - val_precision_m: 0.2334 - val_recall_m: 0.7876\n", "Wall time: 1h 21min 4s\n"]}, {"data": {"text/plain": ["<keras.callbacks.History at 0x133061542b0>"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "# fit the model\n", "classifier.fit(X_train_sc, y_train, epochs=50, batch_size=512, validation_split=0.15, verbose=1,class_weight= {0:1, 1:10},\n", "          callbacks = [history, keras.callbacks.EarlyStopping(monitor='val_loss',\n", "                                                              min_delta=0, patience=10, verbose=0, mode='auto')])"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6355995/6355995 [==============================] - 252s 40us/step\n", "loss:  0.48910188602026355 and Accuracy:  0.7590791056318541\n"]}], "source": ["eval_model=classifier.evaluate(X_train_sc, y_train)\n", "print('loss: ', eval_model[0], 'and Accuracy: ', eval_model[1])"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, 'Loss')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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****************************************+DZ+690UOqa/isPpKDq2r5ND6SurDQUQkbhXa19bLgX07OQFY1aDY1B7gH4EnV63ildoaYjHFQEzR0xelpUuLlQNdfVzd/Ws+Ln4+8NN99NESn4dPoLYySH1VkIDfR1NHhIpeXcblxU27eKGvgoGYoqLUT0WwhMpQCZVB/agpD9DW08/rOw+yvz1C34DWEV8ueYuL/eVcfO/rQ/7OIjB3cjmH1Yf54BFTWTi1kgX1YQ6pq6C81Fou3iuHB+7isSvmwyGnAnCgq493mzqZ/dvv0h+ZxcyaMjbu7WBLdwmLY+089fY+YgpiShGLKQJ+H5MqSplcXsq82nKOLq+Jvz6kNsOPl8JzPLBVKfWufv/yCHAB4BRri4HPW89XAb+2zl0MlCilngZQSnVa+wU4A7jCuuYB4Mt4KNZMgoHBUNy4EWs/BI5xvO5Ksc9gE2kfnAkK+huwbFJqy1ppZebyEaWVWui5cYMGq9KfA5aIVDrWKVOcXLph+rXAaOmMMK1xL0EJ89DqbUQGosyoLmMBlRwN9B/YTmD2cUNv0L5bi7wUmaAD0RgtXX00tkfYcaCLHS3d7GzpZseBLna2dLO3vZeA6mddMMBDj/ycrw7k5iarootng7fzmlrAR1+ah3rptUHHq8sC1IWDhEMl7Gjp5kBXwmN1ZaiOk4lQ1bWdx1+bTFdfouZdZbCEunCQxvbe+P4jZAdPBuEXGyM8QycfC5RxYPs6Ht9+HH6/4BchFPAzuaKUyRWlLKiv5IO799EWPZSvfegY6sJB6sMh6quCTKkI4vclfT4aKuA++MkVS2DhWSilyJakrZTiYHc/+9p6qfv9T5D2GTzw4eOpDPqpDAbiIq+i1E+JP4uQD9v9QRNJBvq9TIbYfph3LD/+qPXv//wrsOq3rLn5NJ0xPPaYCTiLAzYA70s6503gErSr9CIgLCJTgIVAq4j8HzAfeAa4CR3T26qUGnDcc2aqwUXkWuBagDlz5uT9JiqDAZNgYDAUMW7EmlgJBkDc/Zlj9+cJhO0GTaY8RcupSGfmeDXQQi5UndkN2tMKpeHsTbmd/UFdirW2nn4efXUX//vyDnYeSMRIPRzYiU9K+fofEsHzYbp5KwTffPRZfvW7ScyoKaOi1E/A76O0xMexPS/xGeB/NpbzzrY3aO6MWI++QeLIprYyyNwp5ZxwyBTmTCmnMlhCy+tHc0HfNiLHHU5MqbhQKS/1U1FaQnnQ2pb6KS8tocQviMDUP3+Jmrc76fnoYzxTv4SevihNHZaLsT1CY0eEpo4IbT39nLV4Kgumhlk4tZKFU8PU98yDH97N3WeUoJaezf72CNuaOvWjsZPmrj5OX1TPtOog06rLWNTZD0/D9z51FqWHnAT3HsmVgW6uvObs1H9kpeBb78KCs/i741x8YSclGLippiMicXHIwF6YfhinLhxGggEMLYwbi+oaa0delNgXtwy3Ze/MMDqk+uMlZ07cCPyPiFwD/AnYjU60KgFOAY4GdgK/QGfMP+HinnqnUvcA9wCsWLEiz8J5ujBuZ2SAWEzhSxb3BoNh3ONGdL1rJRnYJvx/At71bkrjnN721Fl2ZZOHlu7ocymayrL0B83Wasomh2bu7zV38dO/vMdjrzXQ3RflffMnc+mKWdRWBplSGWT501HUpPlsuOxsSnw+9rX1sru1h76Hw5w3PUrblHr2tPXQ26/dkn1RxcndbxND+OXuGqIlrdRWljJvSgUr5k2mrjJIbThIXWWQOZPL4+JsCOoseO5W/vG4muwNz232vglvPwjHfYo5R2ZoL5WOikVQEoK9byLLLtUxYNUhTjosjfh4U9dkK62y4q7qD4d3nkp//459OkPTbTmT4SQYKKUD/uednPu1NsEqKCkb2h+0Y5+OWaxxCM6QI+ZybIq1BnRygM0sYI/zBKXUHuBiACsu7RKlVJuINABvOFyovwZOQCcj1IhIiWVdG3LPQlNldTHo7BugKjQmLZgGg2EYuBFrnwG+B/wn+tfhs1hme0MKIh0wJUUJuvLJQ8taRDozJxfYhLKUP3At1rTFL9LVSkugx7JM6TiiaEwRU7C7tYcHX9zOc5sbCfh8fPioGXz8pHksmZl0/5VtUFMPVhzTnClaYDF5DsurOln+0RTC4+EfQMsCVl13fva5pmPeB/R2x19g8Ueynx+L6aSC8inukwqS8ZforM6Wbe7Oj2dbWqK97nB442fasmonmzixM03dljOJ11nLQ6x1t2j3+6R5uV9rI2IVxk0Sa/bnu9oh1sZ+f9BXgQVW9uZu4DISsWYAiEgtcEApFQNuRosx+9pJIlKnlGpCx6mtUUopEVkFfBSdEXo18Bsv34SzmbsRawZD8ZFVrCmlGtELmMEN6dygZZNhz9rB+/pcuEFBC7FslrWy9GU7lFJsaexk8+YOPgx85serWdWfvrn4lIpSPnfGAq48YQ714VCqG1oJBiksW9WzoDVNf9C963SLp+Ew42idkLH9BXdi7c2HoOEVuPBHGf9GWameqWPu3NDVBP7SxOfALlPStEmX80jG7j06dYm7+9uWtXzEml1GYzhiDaBy6lDLmt0XNp1lbQyilBoQkeuAP6JLd9yvlHpbRL6KFl5PAKcBt4uIQrtBP2tdGxWRG4FnraSC14B7rVt/EXhERG4F3kAXFfeMsCXQTJKBwVCcuKmzFkKnnR8JxL+5lVKf8HBe45dU2aCgC+MmJxhEOt0VJi2rydwZoLct0d7HvvVAlOc2NrJqcyMvbGlmb1svh0sjHw7COYdVcPYRS/H5BJ9VTsEngs8nlAf8nLygdkh25CD6OrW7qyyFlah6Vuoenl0t0N6Qe+eCZEpKYc4J8J6LemvdB+DpW3SngqOG+Xujaibsfi37eaA7BFTUJxJH6hbpbePGNGJtLUw+ZGhiSjpKrJi1fNyghRJr4anQvHXwvlaruo/zsziCljWrq0qDUioiIqcBy9AlNzIOrpRaic56d+67xfH8ceDxNNc+bY2TvP9ddKbpiGA3c++MmFprBkMx4sYN+r/AJuBs4KvAlYCrkh0uik3eCZxuvSwH6pVSNY7jVdZYv1JKXedmzFFlIALRSOov3bLJMNCrv2DjMUcdOVjWsrlBtVVmd2sPD/11B794dRfNnX1UhUo4eUEtn1tQx2lTD4OfwN8tq4Gj8888S1kQ16Zmtp5rchJDvHNBjj1BUzHvFHj2K1oUZWpG/tyt2qJz3rfcN41PR9VM/b77e3WB3kx0NQ6Oz6qepf+d0/UI3bcOZuSQXO3zacGWTweDg+/prd1hIF8qp8L2Pw/e17pT//iwEyDAYVlLitf0hl8CK0TkMLQl6wngISD3WjHjDNsNaixrBkNx4kasHaaU+lsRucAqCvkQ2mWQETfFJpVSn3ecfz06q8rJ14DnXcxxbBBvNZUifsxZGNcWaznFrLVp92MK0aF6W9nbW8qXH1zDMxu1a+qMw6fysRPnctKhUxKlGOwvzOEWxrWzWlPFX8Vrre1OFISFhKtv2tLhjQ0w34pb2/4CLLk49Tl73oA198P7Pq37bQ6XaqvyQvvu1DGJTrqatJixEbF6hKb4jdNzUIucY6/JbT6l5flb1iqnJj6D+VI5Tc99IAIlQb2vdddgFyiMdMxazHJrXgTcpZT6voi8MRIDjzZ2goERawZDceKmMqptV28VkSVANTDPxXXxYpNWmxW72GQ6Lgcetl+IyLHAVCBDGt0YI17vLIUb1K647yzfkUvMWrRvSOPuzsgAD/x5KxJp57G3O1iz4yCfOfVQ/vRvp3Pf1Ss4dWHd4JpZpe6zQTMSF2tpYtYgEb9ks/dN/UWeSuDlyvTl+u+WbNmxsZMKKurg9H8f/niQaJ/kJm7NdoM6qTsitWVt31t6Oy1Hi2OgIr9G7gd3DN8FCo7yHY6G7q07h4q1kqC2Ao5MzFq/iFyODuj/nbVvQkTbJ9ygRqwZDMWIG8vaPSIyCZ0N+gRQCXzJxXVuik0C8d5784HnrNc+4Nvoli4fTDdAoQpKFgxbBKVzg0Iibm2gTwswN5a1uHWiDUrL2dnSzU9f3M5ja3YhkVauDsHpyw/jMxeekbmnor9EB+dH2t2/p1TEm7iniVmDoWJt37rhx6vZ+Et0HFq6PqFrfwa718BF97jLknVDXKxlqcCglLasJZepqFuk55WcEbo3x0xQm0C+btDtqePmcsVZGLdmthbIbbvg8BSZvtnc+IXj4+js9duUUu9ZGZ4/G4mBR5tEgoGJWTMYipGMYs0STe1KqYPoLKhDcri3m2KTNpcBjyul7NLw/wSsVErtylTws1AFJQuGLYLSFcWFhCvS7gta6qLOmhX388Y773H327t5dtN+/CKcv2w6n142HR6FZYfOBTfNrwvRHzQes5ZCrIWn644LzoSISAe0bIVlfze8cZ3MPwWeflrX9rKFA1hJBf8Fc94Pyy4t3HhVM/Q2U6IHaFES6x/aJL3ezgjdPDgjdt867VLM0lR9CPm4QQf69PwLalmzMkK7GvWPj2TLGmSvE1ggrBCLzwFYPzDDyXGyxUp5wI+ILt1hMBiKj4xizepWcB3waB73zlps0sFlWOnwFicCp4jIP6EteaUi0qmUuimPeYwcvbZYS+UGtYSN7UKMx7dltqzFYoo1+6IcD9z2y5d4t3wZnz3tMK46YS7TqkOJBuMhl2UpCiXWxJd6TJ9fW6Gcombfer0tRHKBzbxT9Hb7n2HpRxP7n7tVC4Pzvjn8pAInpeXalZ3NstZllURJzvK1M0KbNg4Wa3vX5W5VA8sNmqNYa9sFKJg0P/fxkrFj8uwuBnaNtVSJC6GaEXGDishq4CPodW0t0CQizyul/sXzwUcZn0+oDJbQYdygBkNR4sYN+rRVS+gX6L6gACilDqS/BHBRbBJARBahe+m95Lj3lY7j1wArxrxQg8xu0PIkN2jcspZarMViipXr9/L9Z7cSbNzLE0H43En1HH/2GYPLarht4m4TDCdEZb50H9DCJV0z+ORaa7agLJQbFLTwC1bBe39KiLV4UsFnCpNUkEzVrOwxa3YMV7JYq549NCO0vwea30ntOsxGabl2t+aCnQlaCMtaRR0gifcbF2tpLGtua9QNj2qlVLuIfAr4iVLqv0Rk3UgMPOKkSDYKB00zd4OhWHGTYPAJtNXrT+iij68Ba7JdZLVZsYtNbgQetYtNioizmunlwCPO/qPjlrgbNIVwKglqa0h3UkZmkmUtGlP8Zu1uzr7rT1z30BsMxGJ87vxjAfjA7MDQ+mf5iLVCWNZSxavZVM9Ksqyt0wH3TnflcPH5deyVnWQwKKng5sKN46R6ps5yzYQtoJLFmgjULtS11mz2bwAVzdOyVpa7G7RQNdZAN2Uvn6Ld0JC6xppNqAZ6vHeDAiUiMh24lESCQfGx9Vn48ZlDXMuVoRLjBjUYihQ3HQzy9plkKzZpvf5ylnv8FPhpvnMYUTK5QUFb12zLWmRwzJpSiqc37Ofrf9jEtqYuFtRX8r3Lj+b8pdPx9xyAZ0gd92O7l1yLtSroes/duenoOZA6E9SmZjas/6Vu7O3zJ1x9hXRLgnaFvvMH7Zrc+kzhkwqSqZoBu17JfI4t1lLFoNUfob9obfYNw+KYjxv04Hbd49RZVmQ4hKc5LGu79GeitGLoeWVZ2qUVjq+ifxz+RSn1qogcAmwZiYFHFH9AW5EfuwaueEwn3KCTDDpMUVyDoShx08Hg71PtV0o9WPjpjHMi7frLsKQ09fGymkTMWl/CsrZxbztf+90GXtzWwmH1ldx9xTGcu2QaPp8lbmzx0bFXu85KQgnhMyqWtQOZi6pWz9IWo4692sLUtBEWnjW8MVMx34pb2/AEPP/1wicVJFM1UwtVZ2HjZDobAUktZusWwdqf6ySTsklaxAar87N0lZZDX47ZoAe363+3dO7rXKmsHxyzlsoFCtqyFmlPiHePUEo9BjzmeP0ucIlnA44W8z8A538Hfvs5+MNNcP63AF0Yt7W7b5QnZzAYvMBNzNpxjuchdCmN1wEj1pJJ1xfUpmyoZe3rq3bz/97cTlVZgK9ecCRXHD9ncG000L+cQzXw5zv1Q/zafVoahoEeQDKP6yQYHn7pju4DMGN5+uPxwrgN2tIUGyhsvJrN1KX67xl01TsAACAASURBVPL0l7QQKHRSQTLO8h21h6U+p6tJC7VUoqTOkRE65wSrnMnS/OYcKM+9ztrB7YVxgdpUToNmy3DVujOR8ZqMs/RMIerspUFEZgHfB05CZ57/GbhBKZUlhXcccuzV0LIFXvy+dq+/71rCoRJ2HcyjULLBYBjzuHGDXu98LSLV6BZUhmR62+PJBWt3tfKNP2wi4Pcxc1IZM2vKuCBSxpTOnTQf6GbXxh28H3hs3UGufv8SbvjgAmrK01jkAK74hS6gGmnXQq+v09p2wJTD3FtLbMtamm4IWcnUxN0mXmutIZFIkU9cVjZ8Pph3Mmz6HbzvH71JKnDi7GKQSaylK8Ph7BE6cwXsfxtW5NliN1CuhXos5u7fXildEHdOAWqs2VTW69Iddo21hWenPs+2+vYc9FSsAT9Bt5f6W+v1Vda+M70cdNT4m69Ayzb4wxdh8iGEQ/UmwcBgKFLcWNaS6QYWFHoiRUGkHRUMc8/z2/jmHzdTWxmkLhzkrd1tHOjqo6pkgPP9TZzyjVVc59/O+wPwyPVncth0F19gc07Qj+ESDGsXZX9Pfi2H+rt1/9NsCQagv8Bbd1muvgKUi0jF0o/qcbxKKnDipotBqoK4NtWzdaxZ0yZtFRnozd/iaP/b9Xe7K6zcc1AL/YJa1qbq2mr2e0nnGg85LGveUqeU+onj9U9F5J+9HnTU8Pnh4nvh/nPg8Y8zb8EP6ezNZ0k3GAxjHTcxa78lUczWBywmv7prY4fWXdodWci6X0B/dxvvtMDtT27i3CXTuOOSZVSX6criPX1Rev74Vya9/hy3X3QkH9i5GjaVuhNqhSToaDmVj1jL1MTdOUaoRv+dh+Pqc8ORF+nHSGAXxs0k1jobYeaxqY/5fFC3UIu1fDsX2ARyFGuFLNthE7YSFRpe1dtUmaAwkv1Bm0XkKhJt6y4HWrwedFQJVsIVj8C9Z3DZli/w//pvoT8aI5AcSmEwGMY1bn6GfcvxfADYMe5jQB6+XAuKTzxZsFv+ZWsz0/bsoyE2nVsvXMKV75uDs/tCWamfstppoGJcvrQKmqLu+oIWGrusSKQj8WWbC5m6Fzipnq3LOexbn7+rb6wRKNMiNVP5jq7moWU7nNQdAdue0yK2JAS1i/Kbi5116TYjtJBlO2wqk8VahgQDGInCuJ8A/ge4E/0D80V0C6ripnoWXP4wFT8+l3tKv0NPwxEE/FH92Yg/emDyoTAnZcc/g8EwxnEj1nYCe5VSvQAiUiYi85RS2z2dmZccfr7OHuxsgsoMX6wuGIjGuOuZLdy9eit/DfXwviPmUnNCGneQs+VUX6c7i0ihiVvW8kwyyNTE3Un1LF2wdqDHm3i10aJqRnrLWn+PjiHM9JmqWwRvPqT/NvWL42UXcsa2rLmttRYXaxmyeHOl0qqb12CVXaweXcuaUmonuoNBHMsNepenA48FZh7Lq8tv48TXb4SfnJr6nJIQ/PNbubc2MxgMo44bW/ljQMzxOoojPX5ccsSHAAWbV2Y9NR128dqz7voT/7NqK5ceO5u60gg1NWnilUCXawAteCKd7vqCFhqnGzQfbLGWKWYNtEvMbjReYHfzqFI1K33LqXhB3AxfhnbG5L4820zZON2gbji4Xc8rVR20fLG/9Bs3aOtZqs4dMJKWtVQUfaspm7ZDPsTFkS+z629+pOuvXf07+NRz8I8vwcefhIEIvPyD0Z6mwWDIAzdirUQpFS/eYz3PkLY4Dpi6RAdDb8q9yHlcpN35PDc8spaAz8c9HzuWr1+8BIl0pv/CgoTA6TmgLTCjalnLU6z15GBZA/1rfkoR5aNUzUjfzL0zTfcCJ3UOt+dwypnY8YZua60VumwH6M9SoBxULL0LFLT72F86UoVxk8kaLCki54jIZhHZKiJD2tqJyFwReVZE1onIaqtEiH0sKiJrrccTjv0/FZH3HMcy1LopDJXBAK+rheydebauazj/FJh1LExdrLt9HHkhvHLfaIlmg8EwDNyItSZneygRuQBo9m5KI4AIHPFheHe16z6Z0ZjiiTf3cPZdf+KGR9ZS4vPxgyuP4ckbTuGsI6dZRW5V+u4FkHCDxi1r41CsdbcAknBtpcMWa1OPzN/VNxapnqlFRyqR1GVV88/kBq2ek7CKDcfiGLesuay15oVYE0lY1zKJNZERa+aegoxt7ETED9wNnItOnrpcRBYnnfYt4EGl1DJ0l4TbHcd6lFLLrcdHkq77guPY2uG9jeyEQ/r/WUdvmi4Gp/yrXqdevdfrqRgMhgLjRqx9Bvh3EdkpIjuBLwKf9nZaI8DhH9JlB7Y+nfXUpo4IF9z9Zz738Bv4BO6+Qou085ZOT3QZiPf6zGRZs9yg8Zi10XCDWvMbjhu0rCZ7JXo7fqmYXKCg3aCQ2hWari+oE59PFzEVn45Zy5d4goELy1q0X1sDCy3WIBG3lkmsga615pFlTUQ6RKQ9xaMDmJHl8uOBrUqpdy2vwSPABUnnLAbsPmGrUhwfE1RaYq0zkqbW2rSlsOBseOkHuXe/MBgMo0pWsaaU2qaUOgG9YB2plHq/Umqr91PzmNnH6y/VjZldoY0dvVx+78tsa+ziu5ct5w83fIDzlzlEmo1tocvkBg3V6C/pHsuyNipuUGvMSJ41r7IVxLWZfKh2gc49Kb9xxip2+Y5UrlC7T2YmsQa6XdDck/IrnWITKNNbNwkGbbu0q9ITsebCsgZWf1Bv6qwppcJKqaoUj7BSKptZdyawy/G6wdrn5E0SbasuAsIiYv8nCInIGhF5WUQuTLruNst1eqeIBFMNLiLXWtevaWpqyv5mM5CwrGUojHvKv+r157UHhjWWwWAYWbKKNRH5bxGpUUp1KqU6RGSSiNw6EpPzFJ8fFp0LW57WgbcpaGzv5bJ7XmZPaw8//fhxXLB85lCRZmNnV2aylvl8WrB1H9CWtdFIMCgJgj84vJi1bMkFABVT4PMbYEmRtWasdrScSqarWf+b2kIqHWd9Da7JPV5yEIEcSncc8KDGmo1dviOrZW3U3KDZSPUfOtl1eiNwqoi8AZwK7EaXMQKYo5RaAVwB3CUih1r7bwYOR7frm4z2SAwdSKl7lFIrlFIr6uqGl5keDuqaju3p3KCgS3fMPVm3qUqz7hkMhrGHGzfouUqp+CqrlDoInOfdlEaQwz+sYzjefX7IoX1tWqjtb+vlgU8cz/sOyWJNirtBszRUL5+srVOjVboDhtfM3a1lDbRg87JX52gQzlAYt6tx2KVgXJNLgoEXNdZs7Fp96cp22JTVjFaCQTYaAOfkZwGDlLhSao9S6mKl1NHAf1j72uxj1vZdYDVwtPV6r9JE0C2vjvf4fRAK+JhZU8ZzGxszn3jKv0DHHnjzEa+nZDAYCoQbseZ3mvBFpAxIadIfdxxyqraEbPrtoN1723q47J6XaOyI8OAnj+e4eS4sSbaLJ5MbFHTcWvse7ZYajQQDGKZY87y/49gmEILy2jRirSlz2Y5CUhICxF2CwcHtOhszPL3w85h9gs6unpKmV6rN2LWsvQosEJH5IlIKXAY84TxBRGpFxF4rbwbut/ZPstdGEalFN5DfYL2ebm0FuBBY7/UbERGu/cAhrNlxkFfeO5D+xEPPgOnL4c93QtT0EjUYxgNuxNrPgGdF5JMi8kngaaA4Ah5KgrDgTNi0EmJRAPa09nDZPS/T3NnHA584nmPnuhQmbtygoF2IrTutc8ejWGuZ2GINtCs0VReDzgx9QQuNiM4IdeMGPbhdl6px0/A9V+afAv/4l+zxd3bMWiyW+bwRRik1AFwH/BHYCDyqlHpbRL7qyII/DdgsIu8AU4HbrP1HAGtE5E104sEdSqkN1rGfi8hbwFtALTAioSOXrpjNlIpSfrA6Q1ixCHzgRt2CbMOvR2JaBoNhmGStqaCU+oaIrAP+Bh3f8QeggGXQR5kjPgRv/x/s+iv7ao7hsnte5mBXHw9+8niOmTPJ/X3cZIOCFjqd+/Tz0YhZAz1HlyVLBtHXrTsSuIlZK2aqZiZci066GmHuiSM3j9Jy925QL1yguRCqAZT+UZOt7MsIo5RaCaxM2neL4/njwOMprnsRWJrmnmcUeJquKCv184mT5/PNP25m/e42lsxME5ax6Hzd6uyFb8ORF3sj5A0GQ8Fw+z90H7qLwSXAB9G/QIuDw84EfynRDb/l/7d33uFRlWn//9wz6ZNGSKGX0BEQEbFQRLFgW7GDfV1Xd11117bib4uuu/v67quu7tpW1t5dWVRU7KKiAoLSpEMACS0QAiQBUp/fH89MMgkzmZmQKUnuz3XNNTPnnDnPc06SJ/fc7furV76nuKyCF689NjRDDazxI47AHeK9DZ1oedaSM237kFAJtiFuWye966Fh0JpqWzgSqBK0JQnGs2ZMbBhrkRNzb/dcflxP0hLjeOKL9f4Pcjhs7lrRClj7YeQmpyhKs/BrrIlIfxH5o4isxIojbwbEGHOSMebRiM0w3CSlQ/54She/yXebdvM/5w9lePdmfPOv2GfDi4ES6lO8jMBo5ayl5tb3BAuFYEXc2zrpXWxIr6Ksftv+YsBE1lhLcAU21g6U2N/NaBtrSW4PT2zmrbUpMpLjufz4nsxato2CnWX+Dxxyga3i/fIBa9QrihKzNOVZW4X1op1jjBljjHkEqwva5liZOY7Mim3cNqySc4c3brEUJBWlgStBITY8a64ca1zUhvjjDFbEva2T4aMxbjANcVua+OTAfdbCWQkaCh590DD1WlMacs3o3iQ4HTz5RYH/g5zxMPrXsGUhPDEaZlwP3zxqlV3KiyM2V0VRAtOUsXYBNvw5W0T+LSITCEJnr7Wxdc8BbljYiRoc/DJ3ReAP+OPgvsCVoNDQKxWtnDVXDmDqPWXB4jlec9bs8z6vxrh1UlMRqgaF4MKgsWKsaRg0ouSkJXLJMd2ZsaiQbXubqBg+6ko4+Q/WW7zhC/jod/DCuXB/Pjw4EOY/GblJK4riF7/GmjHmTWPMJdjGjp8DtwB5IvKEiJwWofmFlaqaWm56dRFFNWlUdTmGuDWzAn/IH54waCBiwrPmrlgMNRTqyXNr7561OhUDr7y1crdcbqRad4ANgwYqMKgz1qJcE+TxrGkYNGL8fGw+tQaemrPB/0FxCbYy9PLpcNsquGM9XPEWnPYX60H+6A9Quj1yk1YUxSfByE2VG2NeNsacjW0YuRiYGszJRWSiiKwWkXUicshn3DIsi92PNSKyx729p4h8596+XER+EeJ1BcUDH67mu00l3HfBMJKGngtFy2F3E2GDpji4N3AlKNTrg0L0ctY8BkWoxlqdZy3E4ou2hsdY8w6D1klNRah1B7g9awH6rJVstH3hoqFD64161iJO96wUzj2yC6/M/5GS8srgPuTKhj4nwQk3wfnToLbaqh0oihJVQqrXNsbsNsY8GUxZuog4gceAM7C6olNEpIFytTHmFmPMcGPMcOARYIZ71zbgBPf2Y4GpIhJIkDkkPl25gye/LOCyY3vwkyO7WGF3CKgV6peK0maEQaOYswb13qBg2b/bJoo7A3Z8advEJdp72CAMutM2nk0KIm+xpUgIMgwa7RAo2N91capnLcL8cnwfDlTV8Ow3G0P/cFY+DL0IFjxtewgqihI1wtlcZxSwzhhTYIypBF4Dzm3i+CnAqwDGmEq3TAtYtYQWneeWPQe47Y0lDO6czh/OdtuPHXpCp6GwqrnGWohh0HhX9Hobebw/ZQFkaRoTitRUWye966EFBq6cyMprxQfRZy1WjDWRWJacarP0y0vjtMF5PP/NRsoqmqFWMPY2qD4Ic9tOAwBFaY2E01roim334aHQve0QRKQn0Bv4zGtbd3cz3s3A3zwafI0+d52ILBSRhTt3BvfNr7bWcPOri6iuMTx22QiS4p31OweeA5u/Db0SyhhbYBBMGDQhxUoFRStfDWz+kCOueWHQ9l5c4CG9kYpBWVFkK0EhcIFBTRXsLYSs3pGbU1PEruRUm+aGk/qy90AVr87/MfQP5/SHI86DBU/VV4MrihJxwmms+XIx+GvmMxmYboyp6yVhjNlsjBkG9AWuEpG8Q05mzDRjzEhjzMicnOD+UTocwvXj8nngomH0zm7UwLbTEDvFPZuCOlcd1RVQWxVcGBSswROtEChYj15KdjMKDHarZ81DRqPGuB7PWiRJcEFNpX99x72FYGpiw7MGNkSsnrWIM7x7JqP7duSJL9azfe/B0E8w7naoLIN5T7T85BRFCYpwGmuFQHev992AQ7xjbibjDoE2xu1RWw6MbamJnXZEJyYO8SFq3dzE+zpd0CCNtZSs6HrWAFJzmpez1t4b4npI72p/7h7ZrvKdkW3bAdazBv69a7HStsNDsnrWosU95xzBwaoafvnyd1RUh9hfMe8Im9M7/0ntk6coUSKcxtoCoJ+I9BaRBKxBNrPxQSIyAOgAzPXa1k1Ekt2vOwCjgdVhnKvF88821FyugyEaa7mDoGO/0MZoaVw5zQiDqmetjrpea1ttGDwanrX4ZPvcWoy1pEz9Zx8l+uWlcf+FR7Loxz38+d1m9JMcdwdU7IX501p+coqiBCRsxpoxphq4EfgQqyX6H2PMchG5V0R+4nXoFOA1YxronQwC5ovIEuAL4AFjzLJwzbUOj7FWHqKx5vGsBRsGPe9JWxYfTVw5oV1n1UGoKte2HR4yPMbaFmuA1FRGJwwK/osMSjbaCtU0H17kaKAFBlHlrGGduX5cPi/N+5E3Fm4O/AFvugyHfqfDvMds5buiKBElrD0YjDGzgFmNtv2x0ft7fHzuY2BYOOfmk/hkqyoQapl6XRg0yF5WDmfgY8KNK8QwqIq4N6Su19oWyHQ3nI1aGNRPr7WSjVb7MRZ+36C+wMCYyFbNKnXccfoAlm3Zy+/e+oFBndMZ0jWEVjMn/haemmBbeYz5TcN9xsDGr+DbJ2HYZBh0dstOXFHaOVHqHRHDpIbocYLQw6CxgCvHhs8CtX7woCLuDUnrAoitCC2PQkNcsJXF0HQYNFZCoGA9a6bGJqsrUSHO6eCRKUeR7Urg+he/C75ZLkC3kZB/km3j4dGkNQbWfgzPnA7Pn237VM64DnauCc8FKEo7RY21xrhyQ89Z84QFgg2DxgJ1jXGD9CKqiHtD4hKsJ23fFi8R9yh51poKg8aSsaaSUzFBx9REnrj8aHaWVnDza4uoqfVXpO+DE++0v+/fPQsr34FpJ8LLF9ovLWc+ADd/D/FJ8MZV9QadoiiHjRprjUnNaYax1ko9axB8KLTOs6bGWh3pXayxVic1FYU+a+Dbs7Zrnc0Py+4f2Tk1hUpOxQxHds/kz5OOYM7aXfz94xBqt3oeD73Gwof/D16/3H5R/cmjcPMiGPVzq3pw/jQoWgnv/zZ8F6Ao7Qw11hrjyj2MMGiU9RdDIVQVA0/OmjbFrcejYlC+E5DIG7KeAgNfOWs/TAekXkYtFvBIccWYZy0IDeOeIvKpiCwVkc9FpJvXvhovfeOZXtt7i8h8EVkrIq+7K+JjikuO6cGUUd15bPZ6XgmlYe6p90L+eDj/KfjVAhhxhfU0e+h7ilU+WPQiLHmt+RN87zb4+O7mf15R2hBqrDUmNRcOlNju78FSsc96OZzx4ZtXS5MaYk+5ujCoGmt1eFQMyndaQy3Smqn+wqDGwLLp0GtMfdVqLJAUe561YDSMgQeAF9xNuu8F7vPad8Cjb2yM8a5y/xvwkDGmH1AC/CxsF3EY3POTIzhpQA7/781lPDZ7HQ2L8v3QdQRc+TYMu8j/7/z4u6DnaHj3FtjZjK5LO1ZY1YSFz4S2FitKG0WNtcaEmssFweuCxhIpbs9a0MZasQ3ztiaDNNxkdIXKUiheH/kQKPjvs7ZtCRSvhSEXRH5OTVEXBo2pXmvBaBgPBj51v57tY38DRESAk4Hp7k3PA5NabMYtSGKck2lXjmTS8C7c/+Fq/vLeSmpDyWHzhzMOLnjafqF44+rQ89e+esg+V+yDwgWHPx9FaeWosdaY5jTGDVYXNJaIT7JzDjpnTdULDsHTGHfbYpvrGGn89Vlb9gY44mFwkzZF5InNAoNgNIyXAB7L9zwgTUQ8Me8ktz7xPBHxGGQdgT3uXpP+zgk0T9+4pYl3Ovj7xcO5+oRePP3VBm6fvoSqmtrDP3F6Z6/8tTuC/9zuDTaMP+Iqq2G89uPDn4uitHLUWGtMcySnKkpbVyWoB1d28Pl5KuJ+KB5j7eDe6HjWnAkgzoY5a7W18MMMmzcUa8Z1YjogTYdBq0NoJdEyBKNhfDtwoogsAk4EtgAeQ6yHMWYkcCnwsIj0CfKcdmMz9I3DgcMh3H3OYG49tT8zvt/CL1/6joNVIcpS+aLvBKstuuglWOxTUfBQvvmnNdLG3wXdj4V1aqwpihprjfF4SELxrLXGMCiEJjmlIu6H4p0PFum2HWAbyya4GoZBf/wGSrfC0AsjP59AOBy2yMCfZ61kI/xfPqx+P5KzCqhhbIzZaow53xhzFPA797a9nn3u5wLgc+AoYBeQKSJx/s4Zi4gIN0/ox58nDeHTVUVc+fS37DvYAvliJ06FnmPgvVth19qmjy3dbg274ZdZz1zfU2D7MrtdUdoxaqw1xtUMyanWGAaF0FQM9hfHnqcm2qR1ps6JEumGuB7iUxqGQZe9YbcNOCM68wlEU5JTK9+xOYA5AyM5o4AaxiKSLSKetfIu4Bn39g4ikug5BqthvMItnTcb8FjMVwFvh/1KWogrjuvJPycfxaLNJZz32Nes3n6Y8lLOOLjg3xCXBNOvgeoK/8fOfRRqq2H0zfZ9v1Pt87pPDm8OitLKUWOtMYmp9p9dKJJTrTYMGqRnrbYWSndEXk4p1nHGQ2qefR2texOfXO9Zq66E5W/BwLPq89liDY/klC9WzIROQyGrd8SmE6SG8XhgtYisAfKAv7q3DwIWujWMZwP/a4zxqKTfCdwqIuuwOWxPR+SCWohzjuzCC9ccy94D1Zz72Fe8vuDH4CpF/ZHeBSY9DtuXwif3+D5m/25Y+KwtjMnKt9vyhkBqJzXWlHZPhHsNtBJCFTmvaMWetf3FUFvTtH5k2Q6oqajXwFTqSe8CZdujEwYFa5R5Ku3Wf2a9VkNiMATqISnDt2dt31Yo/BZO+n3EpxRIw9gYM536yk7vY74Bhvo5ZwG20rTVcnyfjsz69RhueX0xd/53GfMKdvOXSUNwJTbz38aAM2DU9TDvcdunrf/pDfd/+28rRTbmlvptIjYUuuodqKmOfHscRYkR1LPmi9QQJKdq3VqHrdVYM7X1PdT8sWeTfY4l6aJYwZO3Fo0CA7BeYI9nbdkbkNwB+pwcnbkEQ7Ifz9rKd+3z4J8cuk+JGrlpSbxwzbHcemp/3l68hXMe/YqV2/Y1/4Sn3gt5Q+GtXzbMQ6sog/lPwIAzIe+Ihp/pd4ot4tmysPnjKtHFmKbD30pA1FjzhSs3+MR7j9RUawyDpgbZU67EbaypZ+1Q0t3N7KPRugOsmHvVfpu3tnoWDJ7UsJt8rJGU6bvP2sqZkD0AcgZEfk5KkzgdtvDgpWuPpfRgNZMe+5rXvg1B8cCb+CS48BlbwTzjOptiAfD987YZ+ZhbD/1M/km26llbeLReFj4NDw6A8uJoz6TVosaaL0LRB/WIuLfWalAIbKx5PGuZPcI7n9ZI52G2pUm0wqDxKTYMuvp9a7QNvSg68wgWT4GBd/5T+S7Y9DUMOid681ICckKfbGbdPJZjemUxdcYy5qxtZl+4nP5wxt9gwxfwzT+sx+WbR6zmaPdjDj0+ORO6jwoub23lO7BjefPmpYSPle9YY3xhq0rdjCnUWPOFK9fmctVUBz72YCsUcfcQrLFWsskm+cYnhX9OrY0jp8Btq6J3b+JToKrchkDTu0KP46Mzj2BJyoSayoa94Va9Z8PxGgKNeXLSEnnqqpH0yErhT++saH7z3KOugCPOg8/+Au/fCaXbrJ6oP/pOsM2nm/oSvXmBFZefNt5KVR1OQYTSclQdhB/n2dfzn7TvlZBRY80XqbmAsQZbIFpzGDRoY22j5qv5QwTiEqM3foK7cnndJzDkfNvLLJZJ9qEPunKmDbF3GhadOSkhkRTv5A9nD2ZdURkvzt3UvJOIwNkPQ1oX+O5Z6HKULTrwR19PC49Pfe+vrYUP7rRfKnuPsyLw039a/2VaiR6b50P1QTj+Rti/C5YE2RxZaUCMr+xRIjWEXmt1YdBWaKwlZdpckGDCoB00Xy0miXfZ3mS11bEfAoVDJacO7IGCL6xXTXw1/ldikVMG5TK2XzYPfbKG4rJmJo4nZ8KFT9svjSf/vumff6dhNuLhT81g6euw5Ts49U9w6Rsw4W7bCubJcVYrV4keBZ+7FSmmQucj3b30WkDOrJ2hxpovXCHog7bmMKjDEbjXWk0V7NuixQWxikfMvWO/1uGZSsqwzx7P2poPoLYKBsWYjqnSJCJWnupAZQ0PfLS6+SfqPgpuW2PbczSFw2GPWf+ZrcD3pqLM9m7rejQMvdgeO/ZWuPpdmw/31CkaFo0mBZ9Dt2NsXvcJN0PxOvt3r4SEGmu+SA1BH7TCXdnWGsOgEFjFYO9mm0+knrXYJCHFPg+9qHV4ppIbedZWzLShsK5HR29OSrPom5vGVSf04rUFm/lhi48K32AJNnTfd4JNUt/yfcPtX/3d9jqc+LeG5+p5AvxiTn1Y9M3r1aMTaQ6UwNZF9SHuwZMgo7stKFFCQo01X7hC0AdtzdWg4BZzb8Io1bYdsU1KNiCxqQXqiySvnLWKMlj/qa0CjfVcO8UnN0/oR1ZKAvfMXH54CgfB0OdkEEfDUGjJRvjmURh2ie9KUle2DYuO+60Nlc57LLxzVBqyYQ5g6o01Zxwcd4PVMC7UvnmhoCukLxLTrI5dMDlrB/fZvK/4lPDPKxy4ArQpqWuIq8ZaTHLkZLj+S+jYJ9ozCY7kDvb54F77T7f6oFaBLeyeSAAAIABJREFUtmIykuO54/QBLNxUwswlYdaqT8mCriMb9lv76A9WfeWUe/x/zuGAk/4fDDwbPvnToZ65lsIYmPMgbJobnvO3Rgo+h4TUhp7zEVfYdIhv/hm1abVG1FjzhYjNWwtGH7SsyH57aw0hKF+k5jYdBi3ZZJND07tGbk5K8MQn215vrQVPztqBPTYE6sqJ/XYjSpNcNLI7Q7tmcN+sVeyvDKLd0eHQ71QbVivfZb02K2faRrrpXZr+nAj85BG73v33Z/URkZZkxdvw6b1WnSGYtk/tgYLPodcYq6PsITENRl5je6/tLmjZ8Wqq22xuYliNNRGZKCKrRWSdiEz1sf8hEVnsfqwRkT3u7cNFZK6ILBeRpSJySTjn6ZPUIPVBd6+HrFbi1fCFK9v26aos971/zybI6Na0dqiiBIvDaYtxyrbD2o+s6Lz+brVqnA7hnp8MZvu+gzw+e314B+t7CmDs784HUyGjB5xwY3CfTcmC8/9tQ6ez7mjZeVWUwgd3QUpHKNkAPxwiJdv+2POj/f+YP/7QfaOutxGpeU+03HjlxfD4cdZYboOEzVgTESfwGHAGMBiYIiKDvY8xxtxijBlujBkOPALMcO/aD1xpjDkCmAg8LCKZ4ZqrT4L1rBWvg4754Z9PuKjrtebHu1aySfPVlJYlKdNqgVaWwSANgbYFju6ZxaThXZg2p4Afi/eHb6DOw22e5kd/gB0/wGn31ldEB0Ov0TZ/bcmrsOT1lpvX7PtsY98pr0PeEPjygUOrVtsbBV/Y5/zxh+5L7wzDLoZFLwXWpg6GmmqYfjUUr7U/241fH/45/VG20xr7Jc3sMdhMwulZGwWsM8YUGGMqgdeApurzpwCvAhhj1hhj1rpfbwWKgMiKLwbjWTu41ybnd+wbmTmFg0CNcbXHmtLSJGfY5phJGVZiSGkTTD1jEPEO4abXFnGwKkyGisNhq0L374Keo211YaiMu8OG3t+7tWXCcNuXwfx/wdFX2yKHcbdbo2HFW4d/7tZMweeQmgc5A33vP+EmK5HXEhJUH/0eNnwJZz9k9Zo/mBo+Y/nDu+DbafDipOBlKVuAcBprXYHNXu8L3dsOQUR6Ar2Bz3zsGwUkAIf410XkOhFZKCILd+5spk6dP1zuXK6mSr2L3VNq1WHQJoy1ynK7XT1rSkviqQgdcGZsi84rIdEpI4m/XzKcpYV7uO0/S6itDVPu0OBJ4EyEifc1L1fYGWfDoQ4nTP8ZVFc2fy61tfDurbZw5pS77bZB51oD5csH2m+rkNpaa6z1PtH/zyh3kFWmmD/t8CSoFr0E85+wVaYjr7GNkbcvDY9SwvrPrLTfEedD6XZ46XzrtIkA4TTWfP2E/P31TgamG2MamMIi0hl4EfipMeaQ33pjzDRjzEhjzMicnBZ2vKXmgqmBA024aD3fytqqZ23Pj/ZZpaaUlsRTZKAh0DbH6Ud04q4zBvLesm38/eM14Rlk4JkwdZPtht9cMrvbgoOt38PsvzT/PItegMJv4bS/1Fc6Oxww9nYoWgGr3m3+uVszRSus9zN/fNPHnXCTjWA9czp8/U+bTxgKhQvh3VusUXjqn+22IRdAt1G22KMlC0mqDtp+fVl9YNITcPGLULQSXr00Inqn4TTWCoHuXu+7Af5quyfjDoF6EJF04D3g98aYeWGZYVME02uteJ19zuod/vmEC1e2ffZlrGmPNSUcpHWyRQZ9To72TJQw8POx+UwZ1Z1HZ69j+neF4RkklDw1fww+F47+KXz9D5tz5q/Iyh/lu+Dju6HnGNtCx5sh59t/6l/eH9nqxLKi2BBKL/jcPuef2PRxvcfBGfcDBj7+A/zjSCsR9uUDsGtd05/dtw1euwzSOsNFz1mPKVhP3sT/hbIdMOfvh3khXsx50Dpozv47xCdBv1PgvCdh09cw/ZqwVwCH01hbAPQTkd4ikoA1yGY2PkhEBgAdgLle2xKAN4EXjDFvhHGO/glGH7R4ve3G3BILR7SIT4aENN8FBtpjTQkHJ06Faz60C14MEETVek8R+dRdmf65iHRrtD9dRLaIyKNe2z53n9NT7Z4biWuJBUSEe88dwui+HblrxlLmFRRHe0r+Of1/bFj1i/+FR462IbVgc50+/qMtkjnrwUNDfQ6nzV3bvhTWfNjy8/bFj/PgH8PhuTOhMoxFHvt3ww//tVKE/tjwhZXAy+jm/xiw9+3Y62yvyJsXW++YMwE++zM8ejQ8Ogrev9Pew4qy+s9VV8B/rrCesymv2kpfb7odDcMmw9zHQvfW+WLnGvjqIStnlj++fvvQC+GM/4PV78E7vw6rYR42Y80YUw3cCHwIrAT+Y4xZLiL3ioh3/GMK8Jpp2P76YmAccLXXYjc8XHP1SZ0+aBO5cMXrIKsVV4J6cGX79iCWbLLNfl2Rre1Q2jipOZA3OPBxESCYqnXgAewXx2HAvcB9jfb/GfjCx+kv81S7G2Mil4kcA8Q7HTx+6dH0yErhFy99x4ZdIXqtIkVCClz8vP3ykN4V3v4VPHkirJ/d9Oc2fQOLX7ZhvFw/CfRDL7JRiS//L/zetc0L4KULbCh2y/cw4+fBG527NzQ0hPxRdQC+ehj+Odx6kt671fd1VVfaasz88aFcgY1Qjb4Zrv0Ebllu5cMyusF3z8ErF8PfesGzZ1pv5Vs3QOECOO8JyDvC9/km/NEazR/fHdo8GmOMDbUmpMDpfz10/7HX2S+gi1+yBnyYiAvbmQFjzCxgVqNtf2z0/h4fn3sJeCmccwtIqieXy88aa4ztITOklcj8NEVqrp+ctU2Q2aP1NvxVlMDUVa0DiIinan2F1zGDgVvcr2cDdWV+InI0kAd8AIyMxIRbCxkp8Txz9TFMeuxrrnluAW/ecAKZKTFaUNLjOGskLJ9hReFfnGST30dcYT094rS5aOK0TcJn3WF7vI1rol+bM94Kyr/zayur5kusvqbKGledhkCCq3lz3/KdTXRPzYWrZ9kq1A+m2grJiY2/V3hRW2vz9eY8aL+UDzrHttPIP6lh78PaGivV9dlfYV8h9DvdGlELn7ah3jG/aTSfhbZ3Z/745l0P2PMf9wv7qDoIm+dZA3r9Z/CZO8dw3G9tKNvvObrC6N/A5/9jjeueJzRvLktehU1fwdkP10fcGjN+KuwvtqoMKR0PvSctQFiNtVZNUqb9Iy3b4Xv//mJbBdJaZH6awpVjv101RnusKW0fX1XrxzY6ZglwAfAP4DwgTUQ6AiXAg8AVwAQf535WRGqA/wJ/MT7EM0XkOuA6gB49ehzelcQgPTu6mHblSC7793yufX4h/7riaLJTE6M9Ld+I2OT0gWfb1gxf3N9Qh7Qxk18NbGAdeak9zxf3Q58J9V98S3dYj9HCZ2yD6Kx8m7Te47jQ5rx1Mbx4nvWoXfWO7V923C/t2j3vcbt+H/eLQz9Xud8K26+caecYl2gN1aWv23YbQy+yeqvlRdYzteMH6HIUnPcv6D3WGnoH98And9sCtCO8WqgUfG41XHuNCe1a/BGfZA2//PG20rN8F+xcBT2CML5OuAm+f94arz//PHQN4v27rdHb/VgYcZX/40RsOPTAbtuOJAyoseYPEbdupp8wqKdtR2uuBPXgyobN3zbcZoz1rPVUKSClTRNM1frtwKMicjXwJbAFqAZuAGYZYzbLod7ny4wxW0QkDWusXQG8cMhAxkwDpgGMHDmyTerkHNMri4cuGc4t/1nMWf+cw6OXjuCYXlmBPxgt4hLtP/kRV9p8p9oaMLX2ubbadglIyQ4ulB+XYL0ss26HjXMgLtkagsvfhNoqa8Cd+Fv4+mF4ZqJVYzjp98Hlc25fZj2AiRlw9bsN88NO/yvs3exWeegGg86u37dvG7w6GbYtsTl7x93gNjb+Bms+sM2C5z8Jc90pmJk94cJnYPB59caOwwHnPg57C63Rl97V9pgDa6x1GQHJYepj78oGV5CGYEIKnPInmHEtLHkFjro8tLE+/oN1ypz9UGBDz+GA858K3SAMEjXWmsLVRGPcukrQNuJZ27/LLkYe9/eBEqjYp541pa0TsGrd3Zj7fAARSQUuMMbsFZHjgbEicgOQCiSISJkxZqoxZov7s6Ui8go23HqIsdZeOGtYZ3plp/Crl79n8rR5/Pb0AVw3Lh8fRm7skJRxeO1BPBx1ha1ufOUS63VJSINjfgbHXAvZ/ewxwy62qgzfPAJrPrK5WN7i543ZsQJeONeGL6+aadNVvHE4bS+558+B/14LV79nk+63LoZXp9i1fcqrMOCM+s/EJdqw4uBzrUdp+Zv2PEdOsfsaE58Ek1+BpyZY4+/nn0Jylm2nEYYwYLMZeiF8+yR8+DvrkRtwpm3t4WzC/KkohdUf2IKT0b/2nxfXmDAZaqDGWtOk5trGd77Yvd7mL7SFSklXrv3meKCkvpWHp4KmLVyfovinrmod6zGbDFzqfYCIZAO73b0e7wKeATDGXOZ1zNXASGPMVBGJAzKNMbtEJB44G/gkEhcTyxzRJYOZN43hzulLue/9VSzYWMKDFx1JRkp84A+3ZuKT4NR7rcrB8Ettm4/EtIbHJKbBOQ9bD9jbN8FTp9p8t+NusGG/fVvcj632eeW74Ii3oU9/raMSUmDKa/D0KfDqJXDinTYBPqWjLajoNMT/nFOyrEEZCFc2XDYdnjoFXr7YztnUHF6+WksjYr2AH0yFef+yBnFyFvQ7zRqrfSdY43Tz/PrHjuX2f2JWH3vfYgA11prClWtdzb4oXmcNGWcbWGi8e615XnvadqhnTWnDGGOqRcRTte4EnvFUrQMLjTEzgfHAfSJisGHQXwU4bSLwodtQc2INtX+H6xpaE+lJ8Tx+2Qie/Xoj/zNrJWc9MofHLxvBsG6RlX6OOEdeYh+B6HsK3DDXisJ/eb99NMaVYz1yP3kkcM50ao41pp4+1YZiux0Dl7wMaXnNuw5fZPeDS16yuXNv32hDvd1Gtdz5W4Kc/nDFDDi4zxYprH4f1n4IS1/DZkK4MxASUqHbSFu80H2UzSFsbuFHC6PGWlOk5lgDprb2UPdmcUHbyFeDRioGg+xrT0Nc9awpbZxAVevGmOnA9ADneA54zv26HGgihtW+ERGuGdOb4T0yufHl77nwiblcNy6fX4zvQ2qi/ksiOdOGQYdeaPu0pXd1P7rYBrCh9ifM7geXz7C9ysb8Jjx9QXuPtcbjW7+whQUx0kPxEJLSbTHEEZNsE9vN86Fgti2q6H6sDXd6V8LGEPqX0RSpeTah9OCehk33PG07WqraJdr4kpzas8lWGHmkgRRFUVqQET068N7NY/nTO8t5dPY6XluwmdtO68/FI7vjdMRwLluk6DvBPlqCriPsI5wMd+e2tRYnhjMOeo22j1ZAOBUMWj/+JKdKt9lE0bbQtgO8rtPLWNO2HYqihJkOrgQennwUb/1qNL06pnDXjGWc9c85zFnbRDNyJXYZcj50HhbtWbRJ1FhrCn+SU3VtO9qIsZbcwRZLNPasaQhUUZQIMLx7Jm/84ngev2wE5ZXVXPH0t/z02W9Zs6MFhbgVpRWjxlpT1ElONTbW3G07Wou7NxAOhy0s8BhrtbWw50f1rCmKEjFEhDOHduaTW0/kd2cOYuGmEk5/+EtufOV7NdqUdo/mrDVFnWetkUt+93pwJkJ6AJHa1oQrp17MvWw71FSqZ01RlIiTGOfk5+PyufDobjz1VQHPfb2R95Zt46yhnbl5Qj/656UFPomitDHUs9YUSZlWB+4Qz9p6Kw8SxgZ4EceVXR/u9VSCZvaK2nQURWnfdHAlcMfpA/nqzpO5YXwfZq8qUk+b0m5Rz1pTOBy+VQyK19d3nm4ruHKhxC05tUfbdiiKEht4jLZrx+TXedreXbqNfrmpnDwwl5MG5nJ0zw7EO9vQl2dFaYQaa4ForA9aWwMlG2DAxOjNKRx4h0E9nrWM7v6PVxRFiSDeRtt/vy9k9uoinvl6A09+WUBaUhzj+uVw0sBcxvXPJjctRvt8KUozUWMtEKm5DT1rezfbfK62oAnqjSsbKsugcr/1rDWn+aKiKEqY6eBK4Nqx+Vw7Np+yimq+WruL2auKmL26iPeWbQNgYKc0xvTNZky/bI7t3ZHkhNhsdKoowaLGWiBcuVC0qv59W6sE9eDptbZ/l/ZYUxSlVZCaGMfEIZ2YOKQTtbWGFdv28eXanXy1dhcvzN3EU19tIMHpYGSvDozum83xfToyrGsGcRoyVVoZaqwFItWds2aMFYQtLrDb20qPNQ+eyteyndaz1vOE6M5HURQlBBwOYUjXDIZ0zeCG8X05UFnDtxt389XancxZu4v7P1wNgCvByTG9szg+vyPH9+nIEV0yVDFBiXnUWAuEK9eGPQ/utZptxeus2GtqCwrhxgIeAffSrbBvi3rWFEVp1SQnODmxfw4n9rdRg11lFcwrKGbu+mLmFRTz+Wqbi5yWFMfw7pkM65bBkd0yObJ7JnnpmgKixBZqrAXCu9dacqbtsZaVb71sbQlPGHTrIjC1WgmqKEqbIjs1kbOHdeHsYV0AKNp3kLkFxcwr2M2SzXv41xcF1NQaADqlJzGsWwajemdx8sBc8nNSozl1RVFjLSDe+qDZ/axnrUuYBXGjQYrbs1a40D6rZ01RlDZMbnoS5w7vyrnDuwJwoLKGFdv2smTzXpYU7mHJ5j18tGIHf3lvJfnZLiYMymXCoDxG9uygOW9KxFFjLRDe+qDVlVaGaehF0Z1TOEhIseHdLd/b9+pZUxSlHZGc4OTonlkc3TOrbtvm3fv5bFURn6zcwfPfbOLfczaQkRzP2H7ZDMhLo3eOi97ZLnp1dOFK1H+nSvjQ365AuLwS70s22hBhW6sE9eDKsT3kHHGQ3jXas1EURYkq3bNSuOqEXlx1Qi93m5CdfLKyiLnri3l36bYGx+alJ9I720W/3DQGdk5jYKd0BnZKUyNOaRHC+lskIhOBfwBO4CljzP822v8QcJL7bQqQa4zJdO/7ADgO+MoYc3Y459kkKVkgDutZ273ebmtrPdY8eIy1jG7g0L5ESvsgiHWqJ/AMkAPsBi43xhR67U8HVgJvGmNudG87GngOSAZmAb82xpjwX40SLmybkM5MHNIZsGHTjcXlbNxVTsGucjbsKqdgZxlvLdpC6bzqus/17JjCwE5pDOqczrBuGQztmklOWmK0LkNppYTNWBMRJ/AYcCpQCCwQkZnGmBWeY4wxt3gdfxNwlNcp7scacNeHa45B4XDafK6yIq8ea23YWAPNV1PaDcGsU8ADwAvGmOdF5GTgPuAKr/1/Br5odOongOuAeVhjbSLwfniuQokGyQlOBnVOZ1Dn9AbbjTFs2XOAldtKWbVtH6u2l7Jy+z4+WrEDj7neOSOJoV0zGNYtgyO6ZpCRHE+8w0GcU4h3Ck6HgziH0MGVQKp65hTC61kbBawzxhQAiMhrwLnACj/HTwHu9rwxxnwqIuPDOL/gSc211aDigOQO1tvWFvG079B8NaX9EMw6NRjwfLGcDbzl2eH2oOUBHwAj3ds6A+nGmLnu9y8Ak1BjrV0gInTrkEK3DimcOri+xVN5RTXLt+5jaeEelm3Zy7LCvXy0YkfA83XOSKJvbip9c1Ppl5tG39xU+uS46JCSgEP7w7UbwmmsdQU2e70vBI71daA7zNAb+CyUAUTkOuy3V3r06NG8WQaDK8d61ipK226+GtQXU6hnTWk/BLNOLQEuwIZKzwPSRKQjUAI8iPWyTWh0zkKv94XubYcQsTVMiTquxDhG9c5iVO/6L/v7Dlaxalsp5ZXVVNcYqmtqqaq1z9U1hp1lFawrKmNtUSmvfbuZA1U1dZ91OoQOKQl0dCWQ5UogK9W+7uhKrHud5ap/zkxJ0Oa/rZhwGmu+fiv85WxMBqYbY2r87PeJMWYaMA1g5MiR4csHSc2z+Wq1NdBrbNiGiTqeMGiHXlGdhqJEkGDWqduBR0XkauBLYAtQDdwAzDLGbJaGfReDXvsitoYpMUl6UnwD460pamsNW/ceYG1RGRt3lVNcVklxeSW7yysoLqtk5dZ9FJdXsvdAlc/PxzuF7lkp9OroomfHhs956UmqnxrjhNNYKwS6e73vBmz1c+xk4FdhnMvhkZoDpdutkkGb9qy5XfZZvaM7D0WJHAHXKWPMVuB8ABFJBS4wxuwVkeOBsSJyA5AKJIhIGdYD162pcypKqDgc9eFVBvg/rqqmlpL9lewur6w36Moq2L6vgh93l7Nh137mFRSzv7Khb8SV4CQ7LZHs1ESyUxPomJpIelI8cQ7B4RDiHILT/RzndJCRHE9mcjyZKfFkpiSQmRJvc++0B11YCKextgDoJyK9sd9EJwOXNj5IRAYAHYC5YZzL4eGRnALomB/duYSTAWfABU+3zaa/iuKbgOuUiGQDu40xtcBd2MpQjDGXeR1zNTDSGDPV/b5URI4D5gNXAo+E/1IUBeKdDnLTkshN8y+ZZYwNsW7ctZ9NxeUUlVawq6yCXWWVFJdVsGFXOQs2llB2sJoaY+qUHYIhIzmenLREclIT7bP70TkjiT45NvcuKV69eKESNmPNGFMtIjcCH2JL4p8xxiwXkXuBhcaYme5DpwCvNS5rF5E5wEAgVUQKgZ8ZYz4M13ybxJPLBW3bsxaXCEMvjPYsFCViBLlOjQfuExGDDYMGEwX4JfWtO95HiwuUGEJE6gy6YMKwxhhqDVTX1lJbC5XVtew7WEXJ/kpK9lexZ38le/ZX1Xn0dpZWsLO0giWFeyjaV9Eg184h0CMrhX55afRzF07EOR0crKqhorqWiqoaDlbVcLCqlsQ4Bz06ptAjyz6yXAlIW5N6DBJpK61/Ro4caRYuXBiek6/7FF46376+qxAS08IzjqIoISEi3xljRkZ7Hi1BWNcwRYki5RXVbNlzgLU7ylizo5S1RaWs2WFz76r9eO0cAo13pSbG0T0rhR5ZyfTLTaN/pzT656WSn51KQlzrC7+Gsn5pA5dg8HjWUjupoaYoiqIoIeBKjKN/Xhr989I4i8512yura/lx937AkBjnJCneSVK8g8Q4J/FOoaK6ls279/Pj7v1sKrbPm3fvZ21RGZ+sLKoLz8Y5hF7ZLvrnpZKRnEBtraHGmLrnmlqDARwiOAScIoj7dZxTyEtPqpMN65XtIiM5Pjo3qgnUWAsGj+RUW22GqyiKoigRJiHOQd/cVL/7k+KdNlyad6iTpKK6hoKd5azZUcqaHaWs3l7G8q37KK+owemwBpnDXRThFAEBY6DWGPuota+ragzF5RV4Bxk7uhLole0iNy0Rh0MQ6g09h9hzdspIoluHZLp1SKFrZjKdM5NIjAtfLp4aa8GQ0hEQNdYURVEUJQZIjPOtINEcDlbV8OPu/Wxwy4ZtdD+vLSrDGFNn5Bnsc2V1LTtLKxqEaUUgLy2JM4d25o/nDD7sOTVGjbVgcMbB6X+FnidEeyaKoiiKorQgSfHOujBtsFTV1LJ970EKSw6wZc8BCkv2U1hygM4Z/qtwDwc11oLl+NhtA6coiqIoSuSIdzronpVC96yUiIzX+sonFEVRFEVR2hFqrCmKoiiKosQwaqwpiqIoiqLEMGqsKYqiKIqixDBqrCmKoiiKosQwaqwpiqIoiqLEMGqsKYqiKIqixDBqrCmKoiiKosQwYoxvxfvWhojsBDaF8JFsYFeYpqPj6/g6fmTG72mMyWmB80SdENewtvLz0/F1/NY4fkvNIej1q80Ya6EiIguNMSN1fB1fx29/47d2on3/dHwdvz2PH405aBhUURRFURQlhlFjTVEURVEUJYZpz8baNB1fx9fx2+34rZ1o3z8dX8dvz+NDhOfQbnPWFEVRFEVRWgPt2bOmKIqiKIoS86ixpiiKoiiKEsO0O2NNRCaKyGoRWSciU6M0h40iskxEFovIwgiM94yIFInID17bskTkYxFZ637uEOHx7xGRLe57sFhEzgzj+N1FZLaIrBSR5SLya/f2sN+DJsaO5PUnici3IrLEPYc/ubf3FpH57ut/XUQSIjz+cyKyweseDA/H+G2NaK9hun61n/UrwPgRuQe6frkxxrSbB+AE1gP5QAKwBBgchXlsBLIjON44YATwg9e2/wOmul9PBf4W4fHvAW6P0PV3Bka4X6cBa4DBkbgHTYwdyesXINX9Oh6YDxwH/AeY7N7+L+CXER7/OeDCSNyDtvKIhTVM16/2s34FGD8i90DXL/tob561UcA6Y0yBMaYSeA04N8pzCjvGmC+B3Y02nws87379PDApwuNHDGPMNmPM9+7XpcBKoCsRuAdNjB0xjKXM/Tbe/TDAycB09/aw/Q40Mb4SOu1uDdP1K3rrV4DxI4KuX5b2Zqx1BTZ7vS8kwv843RjgIxH5TkSui8L4AHnGmG1g/xiB3CjM4UYRWeoOM4QtjOGNiPQCjsJ+O4roPWg0NkTw+kXEKSKLgSLgY6x3Zo8xptp9SFj/FhqPb4zx3IO/uu/BQyKSGK7x2xCxsIbp+mVpV+uXj/EhQvdA16/2Z6yJj23R+IY/2hgzAjgD+JWIjIvCHKLNE0AfYDiwDXgw3AOKSCrwX+A3xph94R4vwNgRvX5jTI0xZjjQDeudGeTrsEiNLyJDgLuAgcAxQBZwZ7jGb0PEwhqm61c7W7/8jB+xe6DrV/sz1gqB7l7vuwFbIz0JY8xW93MR8Cb2ly/S7BCRzgDu56JIDm6M2eH+A6gF/k2Y74GIxGMXmpeNMTPcmyNyD3yNHenr92CM2QN8js25yBSROPeuiPwteI0/0R1eMcaYCuBZovN30NqI+hqm61f7Wr/8jR+NNaw9r1/tzVhbAPRzV5EkAJOBmZGcgIi4RCTN8xo4Dfih6U+FhZnAVe7XVwFvR3JwzyLj5jzCeA9ERICngZXGmL977Qr7PfA3doSvP0dEMt2vk4FTsHkns4EL3YeF7XfAz/irvP7RCDbfJBp/B62NqK5hun5Z2st260e6AAADLklEQVT61dT4kboHun65CVflQqw+gDOx1Szrgd9FYfx8bAXXEmB5JOYAvIp1U1dhv5n/DOgIfAqsdT9nRXj8F4FlwFLsotM5jOOPwbrIlwKL3Y8zI3EPmhg7ktc/DFjkHusH4I9ev4vfAuuAN4DECI//mfse/AC8hLviSh8B72fU1jBdv9rX+hVg/IjcA12/7EPlphRFURRFUWKY9hYGVRRFURRFaVWosaYoiqIoihLDqLGmKIqiKIoSw6ixpiiKoiiKEsOosaYoiqIoihLDqLGmRA0RuU9ExovIJBGZ6ueYe0Rki4gs9npktuAcnhORCwMfqSiK0hBdw5RIocaaEk2OxWrMnQjMaeK4h4wxw70eeyIzPUVRlCbRNUyJCGqsKRFHRO4XkaVYTbW5wLXAEyLyxxDOcbWIvC0iH4jIahG522vfrSLyg/vxG6/tV7pFd5eIyItepxsnIt+ISIF+Q1UUJRC6himRJi7wIYrSshhj7hCRN4ArgFuBz40xo5v4yC0icrn7dYkx5iT361HAEGA/sEBE3sN22v4p9huvAPNF5AugEvgdVoR6l4hkeZ2/M7ZL90BsJ+7pLXGdiqK0TXQNUyKNGmtKtDgKK1syEFgR4NiHjDEP+Nj+sTGmGEBEZlAvi/KmMabca/tY9/bpxphdAMaY3V7nectYMeIVIpJ3GNekKEr7QdcwJWKosaZEFBEZDjwHdAN2ASl2sywGjjfGHAjhdI210gz2m6jPoX0c76Gi0XGKoig+0TVMiQaas6ZEFGPMYmPMcKwQ9WCsGO7p7qTbUBY5gFNFJEtEkoFJwNfAl8AkEUkRERdwHjbx91PgYhHpCNAohKAoihIUuoYp0UA9a0rEEZEcbN5GrYgMNMYECiF453uAXdQAvgJeBPoCrxhjFrrP/xzwrfuYp4wxi9zb/wp8ISI1wCLg6pa4HkVR2he6himRRozx51VVlNhFRK4GRhpjboz2XBRFUUJF1zAlFDQMqiiKoiiKEsOoZ01RFEVRFCWGUc+aoiiKoihKDKPGmqIoiqIoSgyjxpqiKIqiKEoMo8aaoiiKoihKDKPGmqIoiqIoSgzz/wFh5WdPZXDHOwAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 720x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(nrows = 1, ncols = 2, figsize = (10, 4))\n", "\n", "# Accuracy\n", "ax[0].plot(history.history['acc'])\n", "ax[0].plot(history.history['val_acc'])\n", "ax[0].set_ylabel('Accuracy')\n", "ax[0].set_xlabel('# Epoch')\n", "ax[0].legend(['train', 'test'], loc='upper left')\n", "ax[0].set_title('Accuracy')\n", "\n", "# Loss\n", "ax[1].plot(history.history['loss'])\n", "ax[1].plot(history.history['val_loss'])\n", "ax[1].set_ylabel('Loss')\n", "ax[1].set_xlabel('# Epoch')\n", "ax[1].legend(['train', 'test'], loc='upper left')\n", "ax[1].set_title('Loss')"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " Classification report : \n", "               precision    recall  f1-score   support\n", "\n", "         0.0       0.97      0.72      0.83   1911460\n", "         1.0       0.23      0.79      0.36    207206\n", "\n", "    accuracy                           0.73   2118666\n", "   macro avg       0.60      0.75      0.59   2118666\n", "weighted avg       0.90      0.73      0.78   2118666\n", "\n", "Accuracy   Score :  0.7251921728106271\n", "F1 Score:  0.3592928105783494\n", "Area under curve :  0.8344086677839623 \n", "\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAtIAAAFlCAYAAADGTQ/6AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDMuMC4zLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvnQurowAAIABJREFUeJzs3Xd4VNXWwOHfSmihB0KHSBcERQQBASki0otepAqCKDb002u/omLXaxcVRUVEmlwsINKxICpCACkqIJ1I7yEJIWV9f5wTHGICScjkTCbrfZ7zZGafts4k2bNmz977iKpijDHGGGOMyZoQrwMwxhhjjDEmL7JE2hhjjDHGmGywRNoYY4wxxphssETaGGOMMcaYbLBE2hhjjDHGmGywRNoYY4wxxphssETanJWIhInIVyJyTET+l4nt54rIjbkRm7+ISKSInBCRUK9jMcbkLyIySEQWeB2H17yoh0WkuoioiBTIrXP6k4j8JiLtsrGf/Q1mgSXSQUJEBopIlFvx7HET2tY5cOg+QAWgrKpef66NVbWLqn6cA+c9g4hMcCu4nmnKX3fLh2byONtF5OqzbaOqO1W1uKomn0fIxpg8zq0v4t16da9bDxX35zlVdbKqXuPPcwSitHVzfq+H3fe12udzDFVtoKrfneM8//jwkF//BrPLEukgICL/Bl4HnsNJeiOBd4BeOXD4C4BNqpqUA8c6X5uA063d7j/+9cCWnDpBsLREGGNyTA9VLQ5cCjQGHvE4nmzxsm7Lj/Wqvd75hyXSeZyIlAKeAu5U1c9VNVZVE1X1K1V9wN2msNtyu9tdXheRwu66diISLSL3ich+tzV7mLvuSeBxoJ/bIjNcREaLyCSf85/xaVZEvhORm93HISIySkR2uMee6Mbru9+NIrJTRA6KyKPnuNyvgFYiEu4+7wysBfb6xFNLRL4RkUPuMSeLSGl33Sc4HzK+cq/nQZ84hovITuAb32sSkTLu69PDPUZxEdksIkOy/1szxuQ1qroXmI+TUAOn69aX3Tpsn4i8KyJhPut7icivInJcRLaISGe3vJSIfOjWt3+JyDOpXRhEZKiILHUfvysiL/vGISIz3cYTRKSyiHwmIgdEZJuI3O2z3WgRmSEik0TkODA07TW5cUx099/h1tchPnH8KCJjxOnat0FEOqTZ92zX8KOIvCYih4HR51E3+763PO0eN0ZEFohIhE88Q9xrOCQij8lZvn0Up8viK+72x0Rkqe/vDRiU3vuSiDQTkZ9F5Kh73W+JSCGf9Soid4rIn8CfbtkbIrLL/RtYKSJX+mwfKiL/cf82Ytz11URkibvJGvf16Odu3939ezoqIj+JyCU+x9ouIg+JyFog1n3/Ov0auLFHuXHsE5FX3V1Tz3XUPdcVvn+D7r4NRGShiBx29/1Peq9rvqWqtuThBSeZTAIKnGWbp4BlQHmgHPAT8LS7rp27/1NAQaArEAeEu+tHA5N8jpX2eXVAU88PfAfc7D6+CdgM1ASKA58Dn6TZ730gDGgEJAD1M7iGCcAzwDjgdrdsOjAAWAoMdctqAx2Bwu61LgFe9znOduDqdOKfCBRzY0l7TdfgJOvl3XhneP17t8UWW/y/+NYXQFVgHfCGz/rXgVlAGaAEzof95911zYBjbn0UAlQB6rnrvgTec+uc8sBy4FZ33VBgqfu4DbALEPd5OBAPVHaPuRKnsaOQW89uBTq5244GEoHe7rZh6VzfRGCmG3t1nG/9hvvEkQTci/Pe0M+9njKZvIYk4C6ggFuvZrdu9n1v2QLUdY/3HfCCu+4i4ATQ2n0tXnav/eoMfq9vu/tXAUKBlm5cqedM930JaAK0cK+pOvAHcI/PcRVYiPP3EOaW3QCUdfe5D+e9pIi77gGcv6kLAXHPV9bnWLV9jn0ZsB9o7sZ8o/uaFfZ5/X4Fqvmc+/RrCvwMDHYfFwdapPc6p/M3WALY48ZexH3e3Ov/zUBaPA/AlvP8BcIgYO85ttkCdPV53gnY7j5uh1Mx+/4T7ff5JxtN9hPpxcAdPtte6FZuBXz2q+qzfjnQP4NrmICTSLd2K4RSwD6cyu50Ip3Ofr2B1T7PT1csaeKvmdE1uWVj3Apvd2pFZ4sttgT34tYXJ4AYt05YDJR21wkQC9Ty2f4KYJv7+D3gtXSOWQEnOQvzKRsAfOs+9k1iBNgJtHGf3wJ84z5uDuxMc+xHgI/cx6OBJWe5tlA3jot8ym4FvvOJYzduEu+WLQcGZ/IadmZ0bnebzNbNvu8to3zW3wHMcx8/Dkz1WVcUOEU6iTTOh4p4oFE661LPmdn3pXuAL3yeK3DVOa77SOq5gY1Arwy2S5tIj8VtAPMp2wi09Xn9bkrn7zc1kV4CPAlEZHDNGSXSA3x/T7b8c7F+NHnfISBCRApoxv2YKwM7fJ7vcMtOHyPNvnE4n1jPV3rnLYBTCafa6/P4nOdV1aUiUg4YBcxW1XgROb1eRMoDbwJX4nxyDsGpuM5l1znWjwNGAs+p6qFMHM8YExx6q+oiEWkLTAEigKM4rapFgZU+dZDgJKjgtAzOSed4F+C08O7x2S+EdOogVVURmYaTzCwBBgKTfI5TWUSO+uwSCvzg8/xs9VoETutt2jq6is/zv9TNpnzWV87kNZxx7vOom31l9H5R2fd8qhonIhnV0xE4LatnG1uT7nlEpC7wKtAU53dfAOdbAV9pr/s+4GY3RgVKujGA8zeS2TE+FwA3ishdPmWFOPO9/Gy/7+E43zxvEJFtwJOqOjsT581KjPmS9ZHO+34GTuJ8us/Ibpx/wlSRbll2xOJUIKkqZvG8STgtyedjEs7XTBPTWfc8TmV1iaqWxPlaTXzWazr7nK0ct9/fe+75bpfzHEltjMl7VPV7nG/GUvssH8Rp2WygqqXdpZQ6AxPBSWpqpXOoXTituRE++5VU1QYZnHoq0EdELsBphf7M5zjbfI5RWlVLqGpX37DPckkHcb4hTFtH/+XzvIr4tlT8/d6RmWtIe+7s1s2ZsQen6w3g9IHG6U6RnoM475np/W7OZSywAajjXsN/OPMawOc63P7QDwF9cbpLlsbpHpO6T0Z/I+nZBTyb5vddVFWnpnfutFT1T1UdgNMN50VghogUO9s+2YgxX7JEOo9T1WM4X2u9LSK9RaSoiBQUkS4i8l93s6nAKBEp5w7OeJy/WzWy6legjThzfJbi7CPYpwL3ikgNcaaMeg749Cwt55n1Jk5fuyXprCuB81XsURGpgtMHzdc+nL6EWZE6sOImnDfRiWJzTBuTH70OdBSRS1U1Bacv7WtuaysiUkVEOrnbfggME5EO4gy8riIi9VR1D7AAeEVESrrrarkt3v+gqquBA8AHwHxVTW2BXg4cdweYhbkD1xqKyOWZuRB1ppWbDjwrIiXcRP3fnPneUB64231PuR6oD8zJ6jW4/FE3p5oB9BCRlu7gvyf5Z4ILgPt7Gw+8Ks5gzVB3gF3hTJynBHAcOCEi9YDbM7F9Es7vr4CIPI7TIp3qA+BpEakjjktEJPUDQNrX433gNhFp7m5bTES6iUiJTMSNiNwgIuXc60/9G0p2Y0sh49d+NlBRRO4RZ3BtCRFpnplz5heWSAcBVX0VpwIchfNPsQunG8KX7ibPAFE4M1ysA1a5Zdk510LgU/dYK3H+yTIyHvgEJ+HdhtMKcNdZts9sDIdVdXGarxxTPYkzKOMY8DXOAEdfz+N8qDgqIvef61wi0gTntR3ivvG8iPMJ/uHzuQZjTN6jqgdwvpl6zC16CGdA9TJxZsZYhDMWBFVdDgwDXsOpj77n79bfIThfy/+O071hBlDpLKeeClyN07UkNZZkoAfOLCLbcFpaP8AZP5JZd+F8y7gVZ6zJFJx6O9UvQB332M8CfXy6tmX1GnK0bvalqr+51zINp3U6BmesT0IGu9yP8164AjiMU69nJh+6H6d7TQxOYvvpObafD8zFGcS5A+c90Lf7xas4H2YW4CToH+KM+wGnj/vH7uvRV1WjcPrIv4Xzem8mnZlYzqIz8JuInADewOn3fVJV43B+tz+652rhu5OqxuA0XPXA6fLyJ9A+C+cNepJ+LmKMMcaY/Eqcm1zdrKo5cWOvXOV+A3oUpwvGNq/jMcHNWqSNMcYYk6eJSA+3a2MxnC5463BmrTDGryyRNsYYY0xe1wtnIORunO4o/TPo/mdMjrKuHcYYY4wxxmSDtUgbY4wxxhiTDYF8QxZrKjcmeKQ7FVVmhDUeme26IH71W9k+r8maiIgIrV69utdhGGNMtqxcufKgqpbL6n6BnEgbY4zJI6pXr05UVJTXYRhjTLaIyI5zb/VPlkgbYwKbWA80Y4wxgckSaWNMYBPrnWGMMSYwWSJtjAls1iJtjDEmQFkibYwJbNYibYwxJkBZU48xxhhjjDHZYC3SxpjAZl07jDHGBChLpI0xgc26dhhjjAlQlkgbYwKbtUgbY4wJUJZIG2MCm7VIG2OMCVDW1GOMCWwSkv3FpEtExovIfhFZn8F6EZE3RWSziKwVkctyO0ZjjMkL7J3GGBPYRLK/mIxMADqfZX0XoI67jADG5kJMxhiT51jXDmOMyWdUdYmIVD/LJr2AiaqqwDIRKS0ilVR1T64EaIwx/paSDMkJJMTFcfxobLYPY4m0MSawWRcNL1QBdvk8j3bLzkikRWQETos1kZGRuRacMSafUIXEE5BwDE7FuMtxZ0k45pYfh1MnIDEWkmIhMR6S4pwlMQ6S4tM8jofkU6ApxKcUo9eHfdgbUzzbIVoibYwJbNZFwwvpvej6jwLVccA4gKZNm/5jvTHGnJaSBPEHIXYvxO2H+AMQfwhOHoGTh50l4cjfZQlueWhhKFQSCpVwl5JQuJT7szQULglFykCJalCoOBQoCgXCoGDRDB6HOccMKUDhFKXybzNZM3dzti/LEmljTGCzFmkvRAPVfJ5XBXZ7FIsxJlBpipMUx0RD3D44sRtO/PV3shy3H+L3Owl0wnEIKwtFK0DR8hBWznleJBxKVYcKlzkJcepSuLSzPrSQ38IPCRE++KAnu3fHcMEFD2TrGJZIG2MCmyXSXpgFjBSRaUBz4Jj1jzYmn1F1WoePb4Nj250EOWYnnNgDsbudstg9TutwiWpOglysEpSoCuUucZLlouUhrDwULeckxiGhXl8VW7Yc5sEHFzF+fE9KlSpCgQIhREaWyvbxLJE2xgS2EOvakdNEZCrQDogQkWjgCaAggKq+C8wBugKbgThgmDeRGmP8RtVpOT6+3UmKj211lphdznJ8BxQoAiWrO0vxKlAyEio0hWIVnbISVf3aYpzT1q7dR6dOk9i79wSVKxdnzJiu531MS6SNMYHNWqRznKoOOMd6Be7MpXCMMf6i6nS7OLIJjmx0f/4JR7dAzA4oWBxK1XCS4lI1oVILqNvHaWEuWd3pcxwkfvppF926TeHo0ZN06FCD557rkCPHtUTaGBPYbLChMcZkLLVl+chGJ0E+vBGO/ukkzce2OV0vwutC+IXOz2rtoXRtp19ywWJeR58r5s3bzHXXfUp8fBLXXluPqVP/ReHCOZMCWyJtjDHGGBPokhOdBPngejj4Gxze4CTLRzc7XTDCL4TStaBMPag3EMpc6LQyB1GrcnZ8+ul6Bg/+gsTEFIYNu5Rx43pQoEDOfdNpibQxJrBZ1w5jTH6i6gzqO7DOTZrXwaH1TtJcohqUbQgRDaBWDydpLl3LmfnCpOv773eQmJjCffddwUsvdURy+FtOS6SNMYHNunYYY4JV3EEnUU5NmA+ud5LmQiXchPliuKAjNP03lKnvzIdssmTMmC507FiT3r3r5XgSDZZIG2MCnbVIG2OCQdxB2LcC9q2EA2ucn/GHnGS53MVQ/lK46AYngQ4r43W0eZaq8uqrPzNsWGPKlAkjNDSEa6+t77fzWSJtjAlsfmyRFpHxQHdgv6o2dMteAnoAp4AtwDBVPSoi1YE/gI3u7stU9TZ3nybABCAMZ+q4/1NVFZEywKdAdWA70FdVj4jTLPIGzhRzccBQVV3lHutGYJR7jmdU9WN/Xb8xxk8SjsHeFbA3ykma9/7i3KWv/GXO9HG1r4OWTzn9mK2xIMckJaVw661fMX78r3z55UaWLBnql1ZoX5ZIG2MCm3/fZCYAbwETfcoWAo+oapKIvAg8Ajzkrtuiqpemc5yxwAhgGU4i3RmYCzwMLFbVF0TkYff5Q0AXoI67NHf3b+4m3k8ATXFuyb1SRGap6pGcu2RjTI5KSXIG/+1d7iTPfy11+jiXbwwVL4caneGKxy1p9rOTJ5MYOPAzvvhiA2FhBXj00Sv9nkSDJdLGmEDnx4pQVZe4Lc2+ZQt8ni4D+pztGCJSCSipqj+7zycCvXES6V44Nz4B+Bj4DieR7gVMdOdrXiYipd3jtAMWquph91gLcZLyqdm9RmNMDjt1Avb8Ant+ht0/OUuxSk7SXOFyaHSbc2e/EEuxcktMTALXXvspixdvo3TpIsyePYBWrSJz5dz2WzbGBC0RGYHTUpxqnKqOy8IhbsLpmpGqhoisBo4Do1T1B6AKEO2zTbRbBlAh9dbaqrpHRMq75VWAXensk1G5McYrp2KcZHnHYoj+3hkQWP5SqNwSLr4ZOn/s3ALbeOLQoTi6dp3C8uV/UaFCMRYsGMwll1TItfNbIm2MCWzn8VWomzRnJXH++7QijwJJwGS3aA8QqaqH3D7RX4pIAyC9JnM91+Ez2Cc7xzLG5KTEWNj1Hez8xummceh3qHAZVLsK2vzXuftfgcJeR2lcH364muXL/6JGjdIsXDiYWrVyd6CmJdLGmMDmwfR37oC/7kAHt/sFqpoAJLiPV4rIFqAuTqtxVZ/dqwK73cf7RKSS2xpdCdjvlkcD1dLZJ5q/u4Kkln+Xc1dmjPkHVTiwFrbPc5a9K5wBgRd0dBLnipfbtHMB7P77WxIXl8iIEU2oXLlErp/fEmljTGDL5cE5ItIZpx9zW1WN8ykvBxxW1WQRqYkzUHCrqh4WkRgRaQH8AgwBxri7zQJuBF5wf870KR8pItNwBhsec5Pt+cBzIpJ6d4VrcAY7GmNyUtx+2DYPts11umsUKAI1ukGT+6Bau3x/N8BAt3btPipWLE758sUICRFGj27nWSyWSBtjApsfE2kRmYrTAhwhItE4M2Y8AhQGFrojvlOnuWsDPCUiSUAycFvqoEDgdv6e/m6uu4CTQE8XkeHATuB6t3wOztR3m3GmvxsG4CblTwMr3O2e8jmHMSa7NAX2r4Yts2H7XOf22tWugprdoPUzzt0BTZ7www876N59KrVqhfPdd0MpWdLbbjaWSBtjApt/Z+0YkE7xhxls+xnwWQbrooCG6ZQfAjqkU67AnRkcazwwPuOojTGZkpLkDBLcPBM2zXD6NdfsCa2egSpXWj/nPGjOnD/517+mc/JkEjVrhlO4cKjXIVkibYwJcDbvqjEmsxLjYde3sPUr+PNzZ1q62r3h2tkQ0dCTMRcmZ0yduo4hQ74kKSmF4cMb89573QkN9f79wRJpY4wxxuRd8Ydh62zY/IUz00a5S6BmD+i/FMLreB2dyQHvvLOCkSPnoAoPPtiSF164OldutpIZlkgbYwJbgFSWxpgAknDMSZ43TIXoH5wBgnX7QMdxNqdzkPnmm23ceeccAF54oQMPPdTa44jOZIm0MSawWdcOYww48ztvngUbpzndN6q2gQv7QbepUCj3pz0zuaN9++rcfntTGjeuyC23NPE6nH+wRNoYE9isRdqY/EsV9kXB2vdh0/+gYjOoPwi6fAKFS3odnfGTpKQUjh49SUREUUSEd97p5nVIGbJE2hgT0AKlH5wxJhcd3wkbp8Mfk5xuHBffDEN/h+KVvI7M+NnJk0n07z+DzZsPs2TJMMqUCfM6pLOyRNoYE9AskTYmn0iMh81fwu8fO3cXrNsH2r4Cke2ti1c+cfx4Ar17T+Pbb7cTHl6E7duPWiJtjDHnxfJoY4KXpkD0Etj4qdMCXaEJNBgKPT+323LnMwcOxNKly2RWrtxDpUrFWbBgMA0blvc6rHOyRNoYE9CsRdqYIBTzF2yYAmvfgwJFod4AGLwKSl7gdWTGA7t2HeOaayaxYcNBatYMZ+HCwdSsGe51WJliibQxxhhjcseeX2D1W7Dta6h9LXSZBJWa26DifOzgwThatRrPrl3Hufji8syffwOVKuWdWVgskTbGBDRrkTYmj0uMd7purHkH4g9CozvgqjFQpLTXkZkAULZsGNdeW48VK3bz9dcDCQ8P7D7RaVkibYwJaJZIG5NHxe2HqFdh3QdOq3PzR6FWDxs4aABITk4hNDQEEeG11zqTkJBEWFhBr8PKMvtrNsYENBHJ9mKM8cCh32HBLTD+QjgVA4OWw3VfQ+1elkQbAL7+ehNNmoxj//5YAEJCJE8m0WCJtDEm0Ml5LMaY3LN7GXzeDaZfBSWqwbANcPXbULqm15GZADJ58lp69/6UNWv2MX78aq/DOW/WtcMYE9CsZdmYAJaSBJs+g1VvQOxuaPYw9JgBBfNWP1eTO956azl33TUXgEceac1DD7XyOKLzF7CJdFjjkV6HYPxs7LgHvQ7B5JKhl0dme19LpI0JQKdiYP0EWPmK0/p8+f1QqxeEhHodmQlAqspTT33P6NHfA/DSSx25//6WHkeVMwI2kTbGGGNMgIk7AMufh98+hmrtoNtUqHyF11GZAKaq3HPPPN58czkhIcK4cd0ZPvwyr8PKMZZIG2MCmrVIGxMA4g7Cyldh7TioPxAGr4aS2f+myeQfIkLRogUpVCiUqVP/xXXX1fc6pBxlibQxJqBZIm2Mh5ISYP2H8NNoqHMd3LACStXwOiqTxzz3XAcGD27ERReV8zqUHGezdhhjApvN2mFM7ktOhLUfwPg6sGUW9FkAHd+1JNpkyrFjJxky5At2744BnAaRYEyiwVqkjTEBzlqkjclFmgIbpsLSR6F0Heg2DaoEx6Awkzv274+lc+dJrF69l/37Y5k37wavQ/IrS6SNMQHNEmljckn0UvjuHpBQ6PIJVL3S64hMHrNz5zE6dvyETZsOUbt2Gd59t7vXIfmdJdLGmIBmibQxfnZgHfzwMBxcD1c+D/UGgP3fmSzasOEgHTt+QnT0cRo1qsD8+TdQoUJxr8PyO0ukjTHGmPwodh/8OAo2z4QWj0LPz6FAYa+jMnnQypW76dx5MgcPxtGqVTVmzx5I6dJFvA4rV1gibYwJbNYwZkzOSjoJq8fA8hegwY1w0yYoUtrrqEwetnDhVg4ejKNLl9rMmNGXokULeh1SrrFE2hgT0KxrhzE5aOsc+Pb/oEw9GLgMwut4HZEJAg891IoqVUrQr19DChXKX3e3tETaGBPQLJE2Jgcc3wHf3w/7V8NVY6BGF68jMnncp5+u54orqhEZWQoRYfDgRl6H5AmbR9oYE9BEJNuLMfle8ilY9gx8chmUbQhD1loSbc7bG28so3//z+jY8RNiY095HY6nrEXaGBPQLCE2Jpuif4BFtzk3UblhJZSq7nVEJo9TVZ544juefnoJALfd1oRixQp5HJW3LJE2xgQ2y6ONyZpTMfDtvbB9PrR7BS7s63VEJgikpCh33z2Xt99eQUiI8OGHPRk69FKvw/KcJdLGmIBmLdLGZJIq/Pk5fHcvVO8EQ3+DwiW9jsoEgcTEZIYOncmUKesoVCiUTz/tQ+/e9bwOKyBYIm2MMcbkdfGHYOGtcOh3566E1dp6HZEJIjNnbmTKlHUUL16ImTP7c9VVNbwOKWBYIm2MCWjWIm3MOfz1E8zuB3X7QNdJUCB/3AjD5J4+fS7i6afb06lTLS6/vIrX4QQUS6SNMQHNEmljMpCcCCv+C6vegE7joVZ3ryMyQWTfvhPExiZSs2Y4AKNGtfE4osBk098ZYwKbnMdi0iUinUVko4hsFpGH01kfKSLfishqEVkrIl29iNOcxaHfYUpziF7izMhhSbTJQdu3H6V164+4+uqJ7N4d43U4Ac0SaWNMQLN5pHOWiIQCbwNdgIuAASJyUZrNRgHTVbUx0B94J3ejNBlShXXj4dO20Og2+Nc8KFnN66hMEPn99wO0bj2ezZsPU6pUEUJDrS49G+vaYYwJaJYQ57hmwGZV3QogItOAXsDvPtsokDrdQylgd65GaNJ36gQsuNlpjb7+Gyh3sdcRmSCzYsVfdOkymUOH4rnyyki++moApUpZn/uzsRZpY0xAsxbpHFcF2OXzPNot8zUauEFEooE5wF3pHUhERohIlIhEHThwwB+xmlQH1sKky6BgMRi03JJok+O++WYbV101kUOH4unevS7z599gSXQmWCJtjDH5S3qfMDTN8wHABFWtCnQFPhGRf7xfqOo4VW2qqk3LlSvnh1ANAL+OhelXQYvHodOHNiuHyXHbth2ha9fJnDhxikGDLubzz/sSFlbQ67DyBOvaYYwJaNaynOOiAd9OtVX5Z9eN4UBnAFX9WUSKABHA/lyJ0DiSEuDbu51bfQ/8GcLreB2RCVI1aoTzyCOtOXQontdf70xIiNW7mWWJtDEmsFl9ntNWAHVEpAbwF85gwoFpttkJdAAmiEh9oAhgfTdyU+xemHktFK8MA5fZHQqNXxw5Ek94eBgAjz/u3MTHGi+yxrp2GGMCmvWRzlmqmgSMBOYDf+DMzvGbiDwlIj3dze4DbhGRNcBUYKiqpu3+Yfwl+geY1BSqd4YeMyyJNjlOVRk16hsaNXqXnTuPAVi9mU3WIm2MCWhWsec8VZ2DM4jQt+xxn8e/A61yOy4D/DEFvr0HunwMNbp4HY0JQsnJKYwcOYd3311JaKgQFbWbyMhSXoeVZ1kibYwJaJZHm3whJRl+fAz+mAx9v4GIhl5HZILQqVPJ3Hjjl0ybtp7ChUOZPv16eva80Ouw8jRLpI0xAc1apE3QS4yF2QMgMQZuWAFFy3sdkQlCcXGJ9OkznblzN1OiRCFmzRpAu3bVvQ4rz7NE2hhjjPFKTDR80R3KN4aeMyC0kNcRmSCUmJhMp06TWLp0JxERRZk3bxBNmlT2OqygYIMNjTEBTST7y7mPLeNFZL+IrPcpKyMiC0XkT/dnuFsuIvKmiGwWkbUicpnPPje62/8pIjf6lDcRkXXuPm/g3xzIAAAgAElEQVSK27yenXOYILRvNUxpAfUGQqfxlkQbvylYMJQePepSrVpJfvhhmCXROcgSaWNMQPPzrB0TcOdL9vEwsFhV6wCL3ecAXYA67jICGOvGVwZ4AmiOc/vtJ1ITY3ebET77dc7OOUwQ2jIbPrsG2r8OzR60wQDGL3wn23nwwVasXXs79epFeBhR8LFE2hgT0PzZIq2qS4DDaYp7AR+7jz8GevuUT1THMqC0iFQCOgELVfWwqh4BFgKd3XUlVfVnd+q4iWmOlZVzmDxs8uTJVK9enZCQEKpXr84v798EC0dA71lQt4/X4Zkg9dtv+2nW7AO2bj1yuqx0absrZk6zRNoYE9BCQiTbSzZVUNU9AO7P1JFfVYBdPttFu2VnK49Opzw75zB51OTJkxkxYgQ7duxAVbmp3g7KbZ/AzLD7oPIVXodngtQvv0TTps0EoqJ289RT33sdTlCzRNoYE9DOp0VaREaISJTPMuJ8QkmnTLNRnp1zmDzq0UcfJS4ujhCBt6+D7vWhxZvK/z0xxuvQTJBauHALHTpM5PDheHr2vJCxY7t5HVJQs1k7jDEB7Xymv1PVccC4LO62T0Qqqeoet1vFfrc8Gqjms11VYLdb3i5N+XduedV0ts/OOUwetXPnTgqFwuRBEB4G7cZCTAJI7E6vQzNBaMaM3xk48DMSE1MYPPgSxo/vRYEC1mbqT/bqGmMCmj/7SGdgFpA688aNwEyf8iHuzBotgGNut4z5wDUiEu4OMrwGmO+uixGRFu5sHUPSHCsr5zB5VJ0a1fh8KIQIdP3ASaIBIiMjPY3LBJ8PP1xFv34zSExM4e67mzFhQm9LonOBtUgbY/ItEZmK05ocISLROLNvvABMF5HhwE7genfzOUBXYDMQBwwDUNXDIvI0sMLd7ilVTR3AeDvOzCBhwFx3IavnMHlUYjzf3BPOslV/0f+TZJJSnOKiRYvy7LPPehubCTqxsYmkpChPPtmOxx5rYzezyiWWSBtjApo/3wxUdUAGqzqks60Cd2ZwnPHA+HTKo4B/3OtZVQ9l9Rwmj0mMhZnXUaVGfRLC76XKt0+wc+dOIiMjefbZZxk0aJDXEZogc/fdzWnRoirNmtn45NxkibQxJqBZq4rJcxLj4cveUKwCdJ7AwJACDLzhxnPvZ0wWJCen8PDDixg+/LLTc0NbEp37rPOMMSagedBH2pjsS4yHL3u6SfTHEGLtVSbnnTqVzIABn/Hyyz/Tq9c0klL7DZlcZ//hxpiAZi3SJs9ISoCv+kCRMtB5AoSEeh2RCUKxsae47rrpLFiwhZIlC/P++z1sUKGHLJE2xgQ0y6NNnpCSDF/3hwJFoOska4k2fnH4cDzdu0/h55+jKVeuKPPn30DjxnbzUy/Zf7oxJqBZi7QJeKqw6DY4FQPXzYXQgl5HZILQnj0xXHPNJNav309kZCkWLhxM3bplvQ4r37NE2hhjjMkuVVjyIOxfDX2/syTa+M0332xj/fr91KsXwcKFg6lataTXIRkskc62d58YRJc2DTlwOIam1z8HwON3dKN720tIUeXA4RhGPDGJPQeOce+QDvTrejkABUJDqFejItWuepi4k6dY9OE9FCpUgAKhoXyxaDXPvDsHgNv6tWHkwPbUiixH1fYPcehoLAClS4Tx3ugbqFE1goRTidw6ejK/b9mTYUwmZ71zzw0UKhKGhIQQEhrKsKff4fv/TeDPVT8hIhQtWZrutz5AifAITsbF8tXYFzh+aD8pyck079qHS9p2Zt+Ozcz76E1OxcchISG07DWQi1q0A0BVWfK/j/hj+RJCQkJo3KEHl3e6lvjYGOaMe4Uj+3dToGAhut1yH+Wq1fD2xcgl1iBtAtpPj8OOhXD9YihU3OtoTBAbNOgSkpOVrl3rEBFR1OtwjEucaUsDT1jjkYEZmKvVZbWIjUvgg6eHnE5aSxQrQkzsSQDuGNCWejUrcfez087Yr2ubhtw1qD1dbh0DQLGwQsTGn6JAgRC+Gf9v7n9pBsvXbafRhVU5cjyOBR/8H60G/fd0Iv3cPb05EZfAc+PmUrd6BV5/uC9dbxuTYUyBbOy4B70OIcveuecGhj79NkVLlDpdlhAXS+GixQBYMf8LDv21g8433cNPM6eQEB9L+/63EHf8KO89cBN3v/0pRw/sQwTKVKxKzJGDfDTqTkb890OKFCvO2u/nseOPNXQf8QASEkLssSMUKxXON1PGUbBIGFdeN5hDu3cyf8IYBv7nJa9ehiwbenlkttPhy5/9Ltt1wYpH21kankuaNm2qUVFRXoeRu6JehbXjoP8PULSc19GYILRsWTTFihXk4osreB1K0BORlaraNKv7+W2Yp4jUE5GHRORNEXnDfVzfX+fLbT+u2sLhY3FnlKUm0QBFwwqT3oeUvp2bMn3eytPPY+NPAVCwQCgFCoSe3mfNxmh27jn8j/3r1azId8s3ArBp+z4uqFyG8mVKZBiT8b/UJBogMeHk302oIiTEx6OqnDoZT5FiJQgJCaVspaqUqVgVgBLhERQrVZq4mKMArFo8m1a9b0BCnH/NYqXCATj41w6qN2gMQNnKkRw7uI/YY0dy6xI9ZdPfmYC0cTqsfA36LLQk2vjFggVb6NBhItdcM4no6ONeh2My4JeuHSLyEDAAmAYsd4urAlNFZJqqvuCP8waC0Xf2YFD3Zhw7EU/nEW+esS6sSEE6tqzPvS9MP10WEiL8NOUhalUrx3ufLmHF+h1nPf66TX/Rq8Ol/PTrVpo2uIDISmWoUqE0+w/H+OV6TBoiTHvhYUSES6/qRuOrugHw/fTxrFu6iMJFizHIbSlu0rEXM159nDEj+3PqZBy9R446nSCn2r1lA8lJiYSXrwzAkf27+eOX79gU9SNFS5Sm45A7KFOxKuUja7JxxVKqXdiQ3Vs2cOzgPo4fPnA60Q5mNtjQBJzoH2DxSCeJLlnN62hMEPrf/35j0KDPSUxMoW/fBlSsaN2GApW/WqSHA5er6guqOsldXgCauevSJSIjRCRKRKKSDv7mp9D8a/TbX1Gny2NMmxvFbf3anLGuW5uL+fnXrRw5/nercUqK0qL/C9TuNIqmDS/golpnn8bm5Y8WUrpEUZZNe5jb+7dlzcZokpJtIvbcMvjx17jp2bH0feBZVi2axc4NawFo2/cmRr45hQYtryJq4UwAtq2LosIFtbjrrWnc9Oy7LJj4FglxsaePdeLIIb4a+yLdRtx/OsFOTkykQMFCDHv6HS5t34Wvx70CwBU9+nMyNoYP/3MrUQu+pMIFtQnJJ3PUWou0CSgxf8HsftD1EyjfyOtoTBB6//2V9Os3g8TEFO69twUfftjT5okOYP76zaQAldMpr+SuS5eqjlPVpqratEBEAz+Fljumz11B7w6XnlF2facm/M+nW4evYyfiWRL1J9e0vOisx42JPcmtoyfRov8LDH9sIhHhxdn+16Eci9ucXYlw5zasxUqFU7dJK/Zs2XjG+gYtr2LjiqUArP1+Phc2bY2IUKZiFUqXq8ihPbsAp1/19JdH0eb6oVSp/ffvvESZclx4+ZUA1G3amgO7tgJO95Hutz7A8Ofeo8dtDxEfc4zS5Sr6/XoDgYhkezEmR5064dy1sPFIqN7J62hMEHrxxaWMGDEbVXjmmfa88so1hIRYXRbI/JVI3wMsFpG5IjLOXeYBi4H/89M5PVcr8u9+ct3aXsKm7ftOPy9ZvAitm9Tmq+/Wni6LCC9OqeJhABQpXJCrml/IRp990lOqeBgFCzgtkcOubcnSVZvP6Jtt/OfUyXgS4uNOP962fiURVatzeG/06W3+XPUzZSs5X/WWjCjP9t9WAxB77AiH9uyidPlKJCcl8tnro2l4ZUfqN297xjnqNmnJjt9/BWDnH2sJd/tSn4w9QXJSIgBrvptLtXoXn9E32xjjZynJ8PUAKH8pNHvE62hMEFq1ag8PP7wYEXjnna48+mgbaxDIA/zSR1pV54lIXZyuHFUAAaKBFaqa7I9z5raPnx/KlU3qEFG6OJvnPc3T786hc+sG1LmgPCkpys49h8+YsaNn+0YsXraBuJOnTpdVjCjJ+08NJjQkhJAQ4bOFq5j7w3rAmfXj3zdeTYWyJVkx/T/MW/obdzw1hXo1K/LB04NJTk5hw9a93Pbk5LPG9PGXP+feixLkYo8f5fPXRwOQkpzMRS3bU6vR5Xz+xpMc2hONiFAqogKdhzmfFVv1HsTs917ig4dvQYH2/W6maIlSrF+6iF0b1xF/4jjrlswHoPutD1Dhgtpc0aM/s955nhVzP6NgkTC63vxvAA7u3snsd19EQkKJqBJJ11vu8+Il8IS9j5iAsOQhSIqHq9+1P0rjF5ddVokxY7pQtmwYAwZc7HU4JpNs+jvjmbw4/Z3JnvOZ/q7VSz9kuy748YErLePJJUE9/d2GabD0URj4CxSN8DoaE0QSEpLYseOY3aEwAATc9HfGGJMTbLCh8dTB3+Cbu6DnZ5ZEmxx14sQpunefSuvW49m48aDX4ZhsskTaGBPQbLBhxkSkkIjU9jqOoJUYC1/1gTYvOX2jjckhhw/Hc/XVE1m0aCsiwsmTSV6HZLLJEmljTECzRDp9ItINWAcsdJ9fKiJfeBtVEFGFecOgUgtoONTraEwQ2b07hjZtPuKXX/7iggtKsXTpMBo1yh+zMAUjvww2NMaYnBLk+fD5eApoDnwLoKq/Wut0Dlr3PhzZBAOXeR2JCSKbNx+mY8dP2L79KBddVI4FC26gSpWSXodlzoMl0sYYkzclqurRNC3vNkg7J+xeBktHQb8lUKCI19GYIHHixCnatp3A7t0xNGtWhTlzBlK2bFGvwzLnybp2GGMCmnXtyNAfItIXCBGRGiLyOmDNp+cr4RjMHQwd34Wy9byOxgSR4sULMXp0Wzp0qMGiRYMtiQ4SlkgbYwKazdqRoZFAE5y7xX4OnCSIb3iVK1Rh0e0Q2QHqXOd1NCZInDjx9/0jbrmlCQsWDKZEicIeRmRykiXSxpiAZi3SGeqkqg+pamN3eRjo4nVQedqasXBwPbR71etITJCYNm09tWq9yfr1+0+X2S2/g4sl0saYgGYt0hkalU7Zo7keRbDYGwU/PeHMF13QvnI35+/dd6MYOPAz9u+PZebMDV6HY/zEBhsaYwJaSD7IiLNCRDoBnYEqIuLbdFoSp5uHyarEeJg7BK4aA+F1vI7G5HGqyvPPL+XRR78B4PnnO/DQQ608jsr4iyXSxpiAZnn0P+wH1uP0if7NpzwGeNiTiPK6pf+BiIvhwn5eR2LyOFXl/vsX8OqryxCBsWO7ceutWb7rtMlDLJE2xgS0fNDXOUtUdTWwWkQmq+pJr+PJ83Z9B5umw5C19qnNnLdbb53N+++vomDBED755Fr69WvodUjGz6yPtDHG5E1VRGSaiKwVkU2pi9dB5SmJsbBwBFz9LoSV9ToaEwRat46kWLGCfPXVAEui8wlrkTbGBDQb4J6hCcAzwMs4s3UMw/pIZ833D0LFZlCrh9eRmCAxZEgjOneuTfnyxbwOxeQSa5E2xgQ0m/4uQ0VVdT6Aqm5R1VFAe49jyjuil8CWL+Gqt7yOxORhhw7F0bHjJ6xated0mSXR+Ysl0saYgGbT32UoQZxPC1tE5DYR6QGU9zqoPCExDuYNhQ5joUhpr6MxeVR09HGuvPIjFi3ayh13fI2qeh2S8YB17TDGBDQh+DPibLoXKA7cDTwLlAJu8jSivOKHh6FyK6jd0+tITB61adMhOnb8hJ07j9GwYXk+/7xffvgWzKTjnIm0iIwEJqrqcRF5D2gMPKKqi/0enTEm37M+0ulT1V/chzHAYAARqepdRHnEru/hz89gyDqvIzF51OrVe+jUaRIHDsTRokVVvv56IGXKhHkdlvFIZrp2jHCT6GuAKsDtwH/9G5Yxxjisj/Q/icjlItJbRCLc5w1EZCKwzOPQAltirNOl4+r3IKyM19GYPOiHH3bQrt3HHDgQxzXX1GLRosGWROdzmUmkUzv9dAE+UtWVmdzPGGNMDhOR54HJwCBgnog8CnwLrAHqehlbwPvxMajSCmp19zoSk0ft3XuCmJgErr/+ImbN6k+xYoW8Dsl4LDN9pNeIyBycCvpRESnO38m1Mcb4VRA3LGdXL6CRqsaLSBlgt/t8Y2YPICKdgTeAUOADVX0hnW36AqNx6vs1qjowJ4L3zN4o+GMK3GhdOkz2XX99A8qXL0br1pGEhlqboslcIj0MaAJsVtU496vE4f4NyxhjHCGWSad1UlXjAVT1sIhsyGISHQq8DXQEooEVIjJLVX/32aYO8AjQSlWPiEjeng0k+RTMvwnavgxFy3kdjclj3nsvikaNKtKihTMEoW3b6t4GZALKORNpVU0WkZo4le6zQBjWtcMYk0ssj/6HmiLyuftYgOo+z1HV686xfzOchpGtACIyDaeV+3efbW4B3lbVI+4x9+dU8J6IehmKV4H6g7yOxOQhqsqzz/7AY499S5kyYWzaNJKyZYt6HZYJMJmZteMtoCDQBieRjgXeBS73b2jGGENQDxrMpn+leZ7VO4pUAXb5PI8GmqfZpi6AiPyI0/1jtKrOS3sgERkBjACIjIzMYhi55PhOiHoFboiyT2Um01JSlPvum8/rr/9CSIjw4otXWxJt0pWZrh0tVfUyEVkNp79KtN71xphcYbnPmXJg6tH0XtG0414KAHWAdkBV4AcRaaiqR9PEMg4YB9C0adPAHDvzwyNw6R1QqobXkZg8IikphZtvnsXHH6+hYMEQpkz5F336XOR1WCZAZSaRThSRENyKVkTKAil+jcoYY1zWRzrHRQPVfJ5XxRmwmHabZaqaCGwTkY04ifWK3Akxh+yNgl3fQMf3vI7E5BEnTybRv/8MZs7cSLFiBfnii3507FjL67BMAMtMX+e3gc+AciLyJLAUeNGvURljjPGXFUAdEanhfrvYH5iVZpsvgfYA7gDzusDWXI3yfKnCN3dB6+egUHGvozF5xPLlfzF79ibCw4uwaNEQS6LNOWVmsOFEEVkJXI3zleD1qrre75EZYwzp90MwfxORwqqakNntVTXJvWPtfJz+z+NV9TcReQqIUtVZ7rprROR3IBl4QFUP+SN+v/ljMqQkQYMbvY7E5CFt2lzA5MnX0aBBeRo2zNuT1ZjckZnBhpWBI8D/fMtUNe1XgcYYk+NssGH6RKQZ8CFQCogUkUbAzap617n2VdU5wJw0ZY/7PFbg3+6S9yTGw9L/QNcpIDbJlDm7XbuOsWvXcVq2dHo89evX0OOITF6SmT7Si/l7IEoYTt+6LcCF/grKGGNShVgenZE3ge443TBQ1TUi0t7bkALE2vegQhOo2trrSEyA27jxIB07fsKxYwksWTKURo0qeh2SyWMy07Wjvu9ztxVkmN8iMsYYH9YinaEQVd2R5vVJ9iqYgJFwHFa8CNfN9ToSE+BWrdpDp06TOHgwjiuuqEpkZCmvQzJ5UJa/81LV5TgT+htjjN+JZH8Jcrvchg0VkVARuQfY5HVQnlv+AlxwDZS/1OtITAD7/vvttGs3gYMH4+jUqRYLFw4mPDzM67BMHpSZPtJ3+zwNwbld+GG/RWSMMT782SItIhcCn/oU1QQeB0rj3N3vgFv+H7dfMSLyCDAcp/X3blWd75Z3Bt7AGcD3gaq+4JbXAKYBZYBVwGBVPSUihYGJOHXqIaCfqm7PQvi343TviAT2AYvcsvwrJtrp1jFkjdeRmAD21Vcb6dt3BidPJtGvXwMmTryWQoVCvQ7L5FGZaZEu57OUwqmse/kzKGOMSRUi2V/ORVU3quqlqnopTkIbB3zhrn4tdZ1PEn0RznRxDYDOwDtua3AozlShXYCLgAHutuBMF/qaqtbBGbg93C0fDhxR1drAa2R9WtEkVe2vqhHu0l9VD2bxGMFl2TNw8S1QoqrXkZgAtX9/LP36OUn0rbc2YfLk6yyJNuclM32kH8uNQIwxxmMdgC3p9Dv21QuY5k43t01ENvN3V7fNqroVQESmAb1E5A/gKmCgu83HwGhgrHus0W75DOAtERF3xozMWOHeKOVT4HNVjcnkfsHp2DbY9D8YttHrSEwAK1++GBMm9GbNmr0888xVNgbDnLcME2kR+YJ/3jb2NFW9zi8RGWOMj/N5oxOREcAIn6Jx7m2t09MfmOrzfKSIDAGigPtU9QhQBVjms020WwawK015c6AscFRVk9LZvkrqPu7czsfc7TPVqqyqtUSkpRv3kyLyK06SPy0z+wedn5+CS0dC0QivIzEBRlXZuvUItWqVAaBv3wb07dvA46hMsDhbi/RbuRaFMcZk4Hzai9ykOaPE+e9zOHf46wk84haNBZ7GaUx4GngFuCmDcJT0u8npWbbnHOsyRVV/An4SkdHA68BknP7Y+cux7bDlKxj+p9eRmACTkqLcc888xo9fzeLFQ2je3Lr9mJyVYSKtqotzMxBjjElPSO589doFWKWq+wBSfwKIyPvAbPdpNM5c+qmqAqk3p0qv/CBQWkQKuK3SvtunHitaRArgjEHJ9EBuESmO0z2kP1AfmAm0zOz+QWX583DJCCgS7nUkJoAkJiZz002zmDRpLYUKhbJ37wmvQzJBKDOzdtQCnsUZQFMktVxV6/oxLmOMAXJtGrsB+HTrEJFKqrrHfXotsN59PAuYIiKvApWBOsBynNblOu4MHX/hJLcDVVVF5FugD05L8Y04CW/qsW4EfnbXf5OF/tG4MX0F/FdVf8ji9QaPQxvgz89h2AavIzEBJD4+kb59ZzB79iaKFSvIzJn96dChptdhmSCUmTsbTgCeAV7GabUZBqT4MSZjjDnN34OBRKQo0BG41af4vyJyKU5Xi+2p61T1NxGZDvwOJAF3qmqye5yRwHyc6e/Gq+pv7rEeAqaJyDPAapzbeuP+/MQdsHgYJ/nOipqqanXxj49C0wcgrKzXkZgAcezYSXr2nMaSJTsoUyaMuXMH0axZlXPvaEw2ZCaRLqqq80XkZVXdAowSkfzb+mGMyVX+bpFW1TicQX6+ZYPPsv2zON/SpS2fA8xJp3wr6dzESlVPAtdnNV4ReUVV7wM+E5F/tGDnq4HgB9fD7p+gy0SvIzEBQlXp2nUKP/20iypVSrBgwWAuuqic12GZIJaZRDpBnCahLSJyG87XluX9G5YxxpgMpN5AxgaEr3gJGt8FBYt5HYkJECLCgw+25OGHFzN37iCqVy/tdUgmyGUmkb4XKA7cjdMKUxJn9LoxxvhdLg02zDNUdbn7sL6qnpFMu91L8sdA8RO7YetX0P51ryMxASAhIYnChZ2UplevenTtWoeCBe1GK8b/MryzoYj0FpFCqvqLqsao6k5VHayqvVT1x9wM0hiTf4lkfwly6TVoDE+nLDitegPqD7aZOgxRUbupU2cMS5bsOF1mSbTJLWdrkR4OvCciX+OMZl+cmwNbjqywby2D3ZHYU16HYPIAu/PYmUSkH87AxBoi8rnPqhLAUW+iymWnYmD9RzDgJ68jMR779ttt9Ow5jRMnTjF2bBRt2lzgdUgmnznbPNI9RKQ08C/gQWCCW2lPdW8CYIwxfpfh12b513LgEM6c1G/7lMfgzAoS/NaPh2ptIby215EYD82cuYF+/WaQkJDMgAENmTCht9chmXzorH2kVfUozhRNH4pIeZwR5u+KSAlVrZEbARpj8jdrkT6Tqm4DtgGLvI7FE5ridOvoOtnrSIyHPv74V4YPn0VysnLHHU0ZM6YrISFWV5jcl5nBhohIKaAbzl20ygJf+DMoY4xJZe+NZxKR71W1rYgc4cxbigugqlrGo9Byx/b5UKQMVL7C60iMR956azl33TUXgMcea8OTT7azD9zGMxkm0u5NCnrh3PGrOfA1zk1ZFtlNAIwxxjPt3Z8RnkbhldVjoNEdXkdhPFSnThkKFQrlxRev5p57WngdjsnnztYivRNnGqWPgD6qaiPDjDG5zlqkz+TTkFEN2K2qp0SkNXAJMAk47llw/nZ4I+yNgp6fn3tbE7Q6darNn3/eRWRkKa9DMeas43iqq2o/Vf3CkmhjjFdEJNtLkPsSUBGpBUwE6gNTvA3Jz359Gy4ZAQWKeB2JyUWJickMHfolixZtPV1mSbQJFBkm0qp6IjcDMcaY9IRI9pcgl6KqicB1wOuqehdQxeOY/CcxFv6YBJfc6nUkJhfFxSXSu/enfPzxGgYP/oL4+ESvQzLmDJkabGiMMV4J/oblbEsSkeuBwUDqvF8FPYzHvzbNgMotoWQ1ryMxueTo0ZP06DGVpUt3UrZsGLNm9ScsLHj/xE3elOlEWkQKq2qCP4Mxxpi07BbhGboJuAP4r6puFZEaODfPCk6/vg3NR3kdhckl+/adoHPnyfz6616qVCnBwoWDqV+/nNdhGfMP57zXgYg0E5F1wJ/u80YiMsbvkRljDE4lld0lmKnqeuBuIEpE6gG7VPVZj8Pyj30rIXYf1OzmdSQmF2zffpTWrT/i11/3UqdOGX788SZLok3AykyL9JtAd5yBLajqGhFpf/ZdjDEmZ1iDdPpE5ErgE+AvnDmkK4rIYFX90dvI/ODXsdDoVggJ9ToSkwt27DjKrl3HuPTSisyffwPlyxfzOiRjMpSZRDpEVXekGQGf7Kd4jDHGZM5rQFdV/R1AROrjJNZNPY0qp506AX9+BsP+8DoSk0vatq3OvHk30LhxRUqVshlaTGDLTCK9S0Sa4UyzFArcBWzyb1jGGOOwPtIZKpSaRAOo6h8iUsjLgPxi46dQtQ0Uq+h1JMaPFv9/e/cdX0WZNXD8d9IIEHqohh56i0iVoogUARGUpReRsrqi66qIyFpeXSxgRSwoUlQIKAqyShdYLCAEDEVAaaGFEkMPEEjyvH/MEAPpyb2Ze5Pz9XM/5j4zd+ZMEmZOnjnzPN/vJz4+kW7dagFw++3VnA1IqSzKSiL9EFZ5RxXgBLDKblNKKbhKAPMAACAASURBVLfTPDpdW0RkGlYvNMAg4FcH43GP7dOh5TNOR6HcaOHCXfTv/xU+PsLmzaOpX1/roZX3yDSRNsacBPrnQSxKKZVKARgPOqcexHrY8CmsGul1QP56EDx2J5w/BNXvcjoS5SYzZvzKqFH/JSnJ8MgjLahbN9jpkJTKlkwTaRH5GDA3thtjRrslIqWUSkFLO1ITkUZATWChMWaS0/G4zW+fQp0B4KNTHuRHb7zxM08+uRKAF164jeeeu60gzEiq8pmsnJ1Wpfg6EOgNHHZPOEopdT29rl5PRJ4BRgBbgOYi8qIxZobDYble4lXYORv+tsbpSJSLGWOYMGE1r7zyIwDvvNOVRx9t6XBUSuVMVko75qd8LyKfASvdFpFSSqWgpR2pDAIaG2PiRKQssATIf4n0wRVQvDqUqet0JMrF9u07zdtvb8DXV5g58x6GDGnidEhK5VhO7pdVB6q6OhCllFJZEm+MiQMwxsSISP6ce2bn51B/sNNRKDcIDS3N11/348qVRHr2rON0OErlSlZqpE/zV420D3AKeNqdQSml1DWCdknfoIaIfG1/LUDNFO8xxtzrTFgudOU8RC2FO/LXs5MF2cWLV4mIiKZ9e6sfrmvXUIcjUso1Mkykxar6b4I1cxZAkjEm1YOHSinlLlrakcp9N7yf6kgU7rTvW6jUBoroCA75wenTl+jRI5yIiGiWLBlIx441nA5JKZfJMJE2xhgRWWiMuSWvAlJKqZQ0kb6eMeZ7p2Nwu9/nQe2/OR2FcoHjxy/QpcvnbNt2gsqVixMSUtzpkJRyqazU1m0UkaZuj0QppdIgIjl+KS90+QwcXgu1ejsdicqlAwdO07btDLZtO0HdusH89NMD1KmjdxlU/pJuj7SI+BljEoC2wCgR2QfEYdXkGWOMJtdKKbfTHukCZt83UPl2KFTC6UhULuzYcZLOnT/j2LEL3HJLRZYuHUTZskWdDkspl8uotGMj0BTolUexKKVUKtqxnDERKWSMiXc6DpfZPQ8aDHM6CpULV64k0qPHXI4du8Dtt1fjm2/6U7x4IafDUsotMirtEABjzL60XnkUn1JKqTSISAsR2Q7ssd83ERHvHubiUiwcWw81ejgdicqFgABfZs68h759G7B06SBNolW+llGPdFkReTy9hcaYN90Qj1JKXUenCE/XFKAHsAjAGLNVRDo4G1Iu7V0EVTtBQJDTkagcOHr0HDfdZD1M2KFDdTp0qO5wREq5X0Y90r5AEFAsnZdSSrmdj+T8lc/5GGMO3tCW6EgkrvLHAu2N9lLTp2+hZs0pLFmyx+lQlMpTGfVIHzPGvJhnkSilVBq0Qzpdh0WkBWBExBd4BPjD4Zhy7soFiP4Juoc7HYnKpkmTfmLcuFUAbNt2gm7dajkckVJ5J6NEWi9fSinH+eipKD0PYZV3VAFOAKvsNu90cAVUbAWBJZ2ORGWRMYbx47/ntdd+AmDq1Lt4+OEWDkelVN7KKJHumGdRKKVUOrRHOm3GmJNAf6fjcJk/FkDNnk5HobIoMTGJf/zjOz76aAt+fj7Mnt2LgQMbOR2WUnku3UTaGHMqLwNRSqm0FIBa5xwRkY8Bc2O7MWa0A+HkTlIiHFwJbSc6HYnKogcf/Jbp038lMNCPBQv+RvfutZ0OSSlHZGVmQ6WUcoyPSI5f+dwq4Hv79RNQDsjSeNIi0lVEfheRvSLydAbr9RERIyLNXBJxek5EQJFyUEJHefAW998fRsWKQaxYMViTaFWgZVTaoZRSykMZY+anfC8inwErM/uc/WDie0An4AiwSUQWG2N23rBeMeBR4BeXBZ2eA0uheje370blTkJCEn5+Vv9bmzZV2LfvUQoX9nc4KqWcpT3SSimPJpLzVwFTHaiahfVaAHuNMfuNMVeAecA9aaz3EjAJuOy6ENNxYClUv8vtu1E5Fx19nmbNPuLrr3clt2kSrZQm0kopD6elHWkTkdMicsp+ncHqjX4mCx+9CTic4v0Ruy3ltm8GKhtjvs0khtEiEiEiETExMdk8AtvFk3D6d7ipbc4+r9xu375TtG07g61bT/DSS+tITExyOiSlPIaWdiilPFo+z4dzREQEaAIctZuSjDGpHjxM7+NptCV/VkR8gLeA+zPbkDHmI+AjgGbNmmV1/9c7tBpuag++ATn6uHKv7dtP0Lnz5xw/foFmzSqxdOkgfH21D06pa/Rfg1LKo/nk4pVf2UnzQmNMov3KThJ7BKic4n0IEJ3ifTGgIbBWRKKAVsBitz1weHClNS248jg//3yY9u1ncfz4Be64ozqrVw8lOLiI02Ep5VHy87VGKZUPiEiOX/ncRhFpmoPPbQJqiUh1EQnAGot68bWFxpizxphgY0w1Y0w1YAPQ0xgT4ZKoUzIGolZAtc4u37TKnZUr99Gp02ecOXOZXr3q8t13AylWrJDTYSnlcbS0Qynl0fJ9OpxNIuJnjEkA2gKjRGQfEIf1rTLGmAyTa2NMgoiMAZYDvsAMY8xvIvIiEGGMWZzR513q1C4wCVBKh0/zNKVKFcbHR7j//jA+/vju5NE6lFLX00RaKaW8y0agKdArpxswxiwBltzQ9lw6696e0/1k6tBqCLlNC+E9ULNmldi8eTShoaXx0VmRlEqXJtJKKY+W30ffyAEBMMbsczqQXItaAfUGOh2FAowxTJr0E1WqlGDAAGuq79q1yzgclVKeTxNppZRH0zQ6lbIi8nh6C40xb+ZlMDmWlABH10Hnj52OpMAzxvDUUyt5/fX1BAT40q5dVUJCijsdllJeQYuelFIezd0TsohIlIhsF5FIEYmw20qLyEoR2WP/v5TdLiIyxZ5ae1vKh/1EZJi9/h4RGZai/RZ7+3vtz0pG+8gCXyAIa3SNtF7e4eSvEBQCRcs7HUmBlpCQxMiRi3n99fX4+fkwe3YvTaKVygbtkVZKebQ8Gn2jgzHmzxTvnwa+N8a8KiJP2+/HAXcBtexXS+ADoKWIlAaeB5phjcm82Z52+7S9zmis0S+WAF2BpRnsIzPHjDEv5vqInRa13KqPVo6Jj09g4MCv+frrXRQu7MdXX/XlrrtqOR2WUl5FE2k3SUxMZEDf+yhXvjxT35/G888+w84dOzAYqlatzksTX6FI0aJERx/l+X8/w+nTpyhRoiQvvzqZ8hUqAHBzo3rUqmU9zV6hYkWmvPchAPcPGcjFuDgATp2KpWGjxrz97vvOHGgB8tpLz7L+x3WULFWaWfMWJrd/PX8OC7+ch6+vL63atOfBRx9n12/bef3l/7NWMIb7R/2Ddh06cvLEcV5+4RlOxf6Jj/jQo3cf+vQffN1+5n0+iw+nvMGiFesoWbIU58+d5bWXniP66GECAgrx1LMvUqNmwbnYOXTb7B7gdvvr2cBarCT3HuBTe9zmDSJSUkQq2uuuNMacAhCRlUBXEVkLFDfGrLfbP8V6SHBpBvvITP6odoleDw2HOx1FgXX+fDy9e8/n++8PUKJEIb77biBt2lRxOiylvI4m0m4y57NPqVGjJhfiLgAwdtwzBAUFATD5tVcInzuHEaNG8+bk17i7Zy969urNLxvW887bb/Dyq5MBKFQokC++/ibVtmd9Njf568f/+Qgd7uiYB0ekuna/h95/G8DLL0xIbvs1YiM/rlvDJ3O/IiAggNOnYgGoXjOUabPn4efnR+yfMYwY1IfW7W7D19eXf/zzSWrXrc/FuDhGD+1HsxatqVajJgAnTxxn8y/rKV+hYvI+Pp81ndDadfnP5Hc4GLWfdya9zJvvT8/bg3dQbnqkRWQ0Vm/wNR/Zs/GlZIAVImKAafby8saYYwDGmGMiUs5eN73ptTNqP5JGOxnsIzPe/w8+KRGif4YuM5yOpMA6fPgcERHRlC9flOXLB9OkSQWnQ1LKK2mNtBucOH6cH9atpfd9fZLbriXRxhji4y8n12/u27ePlq1aA9CiZSvWrv4+y/uJi7vAxo0b6NDxTtcFr9LVpGkzihUvcV3bN1/NZ+CwEQQEWNMblyptPeUeGFgYPz/r79Qr8fHJP+8ywWWpXbc+AEWKFqVq9er8GXMieXtT35rE3x95/LoC34MH9tG0eUsAqlarwfFjRzkVm7IKQaXHGPORMaZZiteNSTRAG3vs5buAh0WkfQabTG967ey259i1Xm+vdnwTBN2k9dEOql+/LMuWDebHHx/QJFqpXMjzRFpE8v29vEmvvsy/nhiLj8/1395nJ4znjtvacGD/fgYMGgJAnTp1WbVyOQDfr1pJXFwcZ86cBuDKlXgG9L2XwQP6svr7Van2s3rVKlq2bJ2cpKu8d/jQQbZHbuGh4QP559/vZ/fOHcnLdu7Yxv39ejF84L08Pu655MT6mmPRR9nz+27qNWgMwE/r1lC2bDlCa9e5br2aterwwxrr57/rt+0cP36MmJMnKCgkF6+sMMZE2/8/CSwEWgAn7JIN7P+ftFdPb3rtjNpD0mgng33kf0fWQQX3zDiu0rd37ynmz//rHNWqVQihoaUdjEgp7+dEj/T/pbdAREaLSISIRHzycVodR57vf2vXULp0aeo3aJhq2UsTX2HVmh+oUaMmy5dZcyE8PvYpIiI20fe+XmyO2Ei58uXx9bUSrmWr1hD+xde8OukNJr/6MocPHbpue0uXfMtd3bq7/6BUuhITEzl/7hzvz5jDg48+wQvjn8Qqn4X6DRsza/4ips2ax5zZ04mPj0/+3MWLF3n+6X8x5vFxFA0K4vLlS3w+82OG//3hVPsYOHQE58+fY8SgPnz9xVxq1a6b/DtSELhzinARKSoixa59DXQGdmBNmX1t5I1hwLUaq8XAUHv0jlbAWbs8YznQWURK2aNvdAaW28vOi0gre7SOoTdsK6195H/H1kO1rk5HUaBs3Xqctm1nMHDg16xatd/pcJTKN9xyNRaRbektAtK9l2ffdv0I4HJC7m5/OiXy1y2sXbuaH39YR3x8PHFxFxg/7kleee11AHx9felyVzdmzfyEXr3vo1y58rz1zlQALsbFsWrlCooVs0awKlfO+laFVK5Ms+Yt2L1rJ5WrWA+DnDlzmh3bt/PWlPccOEp1Tdly5WnX4U5EhHoNGuHjI5w9c5qSpf7q5alavQaBhQtzYN9e6tZvQELCVZ4f9y/u7NKd9h2sspzoI4c5Fn2UEYOscqCYkycYPaQvH8wMp0xwME8/9x/AKg3q36srFSvdlDqYfMrNf+2XBxbaSbcfMNcYs0xENgFfiMgI4BDwN3v9JUA3YC9wERgOVrmFiLwEbLLXezFFCcZDwCygMNZDhkvt9lfT2Uf+Zoz1oOHtbzkdSYHx00+H6N59LmfPxtOxY3VatQrJ/ENKqSxxV7dWeaALcPqGdgF+dtM+PcI///UE//zXEwBs2vgLs2fN4OVXJ3Po4EGqVK2KMYb/rV1D9eo1AJJH6/Dx8eGT6R/Rq/d9AJw7e5bAwoWtB9hOnyLy1y3c/8DI5P2sWL6M9rfdTqFChfL+IFWytrfdwa8Rv3DzLc05fDCKq1evUqJkKY4dPULZ8hXw8/Pj+LFoDh+MokKlStbsYS89T5XqNeg7KHmoYWqE1mbR8v8lv+93TxemzZ5njdpx/hyBgYXx9/fnu2++oknYLRQtQOU87hz+zhizH2iSRnssaTzUZ4/Wkfq2gbVsBpDq6TljTASQ6hZVevvI907/Ab6FoEQ1pyMpEJYu3cN9933BpUsJ3HtvPebOvZdChQrOHS2l3M1d/5q+BYKMMZE3LrCHgypQjDE8+8w4LsTFYYyhTp06THjOqnCJ2LiRKW+/CSLc0qwZz/z7eQD279/HS//3PD4iJBnD8JGjqBkamrzN5UuX8MCIUY4cT0H14r+fInLzJs6eOUOfHh0ZPuphuvXszWsvPcv9/Xvj7+/P+OcnIiJs3/orc2d/gq+fHz4+Pjz21ARKlizFtsgtrFj6X2qE1krufR71j0dp1Sb959sOHdjPy/83AR8fH6pVr8lT/063Oipfyh9jvalkR9ZBSEbPcypXCQ/fztChi0hISGLEiJv58MMe+PnpGANKuZJcq+f0NN5a2qGy7nTcFadDUHmkYomAHOfD32w/nuNzwT2NKmgenkeaNWtmIiIiMl9x+Ugo3xTC/uH+oAqwc+fiCQ2dQkzMRcaOvZXXXrszryY3UsorichmY0y2n4LW+ztKKY/mo33S+cuJCGisd9PcrXhxa5KVdesO8sQTtzodjlL5libSSiml8sbVi3B6D5QNczqSfMkYw88/H06eobB585to3rzgPJislBO0WEop5dFEcv5SHubEZghuAH76kLSrWXXQi2nXbiZz5253OhylCgztkVZKeTTR0o784/AaqNDS6SjyncuXExgw4CsWLdpNkSL+lClT2OmQlCowNJFWSnk07VnOR2K26UQsLnb+fDy9es1n9eoDlCwZyJIlA2ndunLmH1RKuYSWdiilPJoPkuOX8jAnNuvQdy70558X6djxU1avPkCFCkGsW3e/JtFK5THtkVZKeTTtkc4nLsXCxZNQKjTzdVWW9O37JZs2RVO9eklWrhxCzZqlM/+QUsqltEdaKeXR9GHDfOLYL1CmPohedlzlrbe60LZtFX788QFNopVyiPZIK6WUcr+YrVrW4QJ//nmR4OAiADRpUoF16+7XiVaUcpB2DSilPJrk4j/lQU5usWY0VDm2bt1BQkOnMHPmr8ltmkQr5SxNpJVSHs1Hcv5SHiRmK5S72ekovNZ33/1Bly6fc/ZsPCtW7McY43RISik0kVZKeTjtkc4H4s/B+aNQqrbTkXilOXO20avXfC5fTmDUqKZ8/nlv7YlWykNoIq2U8mj6sGE+cOwXCG4IPvpYTnZNnbqRwYMXkpCQxNNPt2HatB74+uqlWylPoWc1pZRH057lfODULihdx+kovM4772zgsceWAzBp0p2MHdvG4YiUUjfSRFop5dG01jkfiNkGFVs7HYXX6dSpJuXKFWXixDsYOVIf1FTKE2kirZRSyr1O/gqNRjgdhVcwxiTXP9evX5Y9ex6hePFCDkellEqPFloppTyaPmzo5ZISrNKO4EZOR+LxLl26Sq9e8/ngg03JbZpEK+XZtEdaKeXR9KFBL3d6LxStCAFBTkfi0c6di6dnz3D+97+D/PzzYQYMaETJkoFOh6WUyoQm0kopj6Z5tJf7cxuUbex0FB4tJiaOu+6aw+bNx6hUqRgrVgzWJFopL6GJtFLKo/lol7R3i90JZRo4HYXHOnz4LJ06fcbvv8dSs2YpVq4cQvXqpZwOSymVRVojrZTyaJKLl/IAsbugTD2no/BIv//+J23azOD332Np3Lg8P/74gCbRSnkZTaSVUp5NM2nvdmoXlK7rdBQeycdHiI9P5NZbK7N27TAqVNA6cqW8jZZ2KKU8mo6+4cWSEuDMHiitPdJpqVWrDOvW3U9ISHGKFg1wOhylVA5oj7RSSin3OHsAilQA/yJOR+Ix/vvf35ky5Zfk93XqBGsSrZQX0x5ppZRH02cNvdip33Vq8BQ++2wrw4d/Q2KioVmzStx6a2WnQ1JK5ZL2SCulPJqWSHsxrY9O9s47Gxg6dBGJiYYJE9rRunWI0yEppVxAe6SVUp5NM2LvdWo3VGjhdBSOMsbwwgtrefHFdQC88UZnHn+8tcNRKaVcRRNppZRH04cNvdjpPVBvkNNROCYpyfDPfy5l6tRN+PgI06ffzfDhNzsdllLKhTSRVkp5NK2R9mJn90HJmk5H4ZiYmDgWLtxNQIAv8+bdR+/eOnqJUvmNJtJKKY+mebSXunoJLsVCUMGtBS5fPogVK4Zw/PgF7rijutPhKKXcQB82VEop5Xpn90HxauDj63Qkeers2ct89tnW5Pf165fVJFqpfEx7pJVSnk27pL3T6T1QqpbTUeSpEycu0LXrHCIjj5OQkKT10EoVAJpIK6U8mj5s6KXOFKz66IMHz9Cp02fs2XOK0NDSdOigvdBKFQSaSCulPJo+bOilzu4vMFOD79oVQ+fOn3PkyDnCwiqwbNkgypcPcjospVQe0BpppZRH0wlZXE9EuorI7yKyV0SeTmP54yKyU0S2icj3IlI12zs5u79A9EhHRETTrt1Mjhw5R9u2VVizZpgm0UoVIJpIK6U8m2bSLiUivsB7wF1AfWCAiNS/YbVfgWbGmMbAAmBStnd0dj+UqJHLaD1bUpLhgQe+ITb2Et261WL58sGULBnodFhKqTykibRSyqNJLv5TaWoB7DXG7DfGXAHmAfekXMEYs8YYc9F+uwHI3hh2SYlw7hCUqOaCcD2Xj4/w1Vd9GTOmOYsW9aNIEX+nQ1JK5TFNpJVSqmC5CTic4v0Ruy09I4ClaS0QkdEiEiEiETExMX8tuHAECgeDX/7snf3112PJX9eqVYZ33+2Gv3/BGuZPKWXRRFop5dFEcv5SaUrrO2PSXFFkMNAMmJzWcmPMR8aYZsaYZmXLlv1rQewuKFLeBaF6nrfeWk/Tph/x1lvrnQ5FKeUBdNQOpZRH03zY5Y4AlVO8DwGib1xJRO4EJgC3GWPis7WHuGNQum5uYvQ4xhiefXYNEyf+AFhlHUoppYm0Usqzab7iapuAWiJSHTgK9AcGplxBRG4GpgFdjTEns72H84fzVX10UpJhzJglfPBBBL6+wowZ9zB0aBOnw1JKeQBNpJVSHk0fGnQtY0yCiIwBlgO+wAxjzG8i8iIQYYxZjFXKEQR8KVaNzCFjTM8s7+TcIajYwvXBO+DKlUSGDVvEvHk7KFTIly+++Bs9e9ZxOiyllIfQRFop5dG01tn1jDFLgCU3tD2X4us7c7WDc1FQp2+uNuEpHntsGfPm7aBYsQAWLx7A7bdXczokpZQH0YcNlVIeTYeR9kLnD0Oxypmv5wWeeqoNTZqUZ/XqYZpEK6VS0R5ppZRn04zY+1w4CkEZjajn2c6fjycoKAARoVq1kmzZ8nd9uFAplSaPTaQDPTYy5SoVSwQ4HYIqwESkMvApUAFIAj4yxrwjIi8Ao4BrAyM/Y5dCICLjscZVTgQeNcYst9u7Au9g1RxPN8a8ardXx5rwpDSwBRhijLkiIoXsfd8CxAL9jDFRbj/ovHDlPCRdhYBiTkeSI1FRZ+jU6TMeeCCM8ePbATpCh1IqfVraoZTyaG6c2TABeMIYUw9oBTycYqrst4wxYfbrWhJdH2uEiwZAV+B9EfHNZMrt1+xt1QJOYyXh2P8/bYwJBd6y18sfzh8F/2JeWdy+c2cMbdrMYO/eUyxYsIv4+ASnQ1JKeThNpJVSHs1dE7IYY44ZY7bYX58HdpHxDH/3APOMMfHGmAPAXqzpttOcclus4S7uABbYn58N9Eqxrdn21wuAjvb63u/iCa8cQ3rjxqO0azeT6OjztG9flTVrhlGokN4aVUplTBNppZRHy83DhimnsLZfo9Pch0g14GbgF7tpjIhsE5EZIlLKbktvau302ssAZ4wxCTe0X7cte/lZe33vF3cMgio6HUW2rFq1nzvumM2pU5fo0aM2y5YNonjxQk6HpZTyAppIK6U8Wy4y6ZRTWNuvj1JtXiQI+Ap4zBhzDvgAqAmEAceAN1JEciOTg/aMtuX9Tv8BQSFOR5FlS5fuoXv3ucTFXWXw4MZ8/XVfChf2dzospZSX0PtWSimP5s4JWUTEHyuJnmOM+RrAGHMixfKPgW/ttxlNrZ1W+59ASRHxs3udU65/bVtHRMQPKAGccuGhOSf+DBQq4XQUWdaoUXkqVAjinnvq8PbbXfXBQqVUtmgirZTyaO6qHLZrkj8Bdhlj3kzRXtEYc8x+2xvYYX+9GJgrIm8ClYBawEas3uVUU24bY4yIrAH6YNVNDwO+SbGtYcB6e/lqY0z+6JGOOw7lmjodRaaMMYgIISHF2bx5NGXKFCa/lKkrpfKOJtJKqYKqDTAE2C4ikXbbM1ijboRhlVpEAX8HsKfR/gLYiTXix8PGmESAtKbctrc3DpgnIv8BfsVK3LH//5mI7MXqie7vzgPNU3HHoGgFp6NIlzGGCRNW4+srvPTSHQAEBxdxOCqllLfSRFop5dHc1UdojPkxnc0vSaPt2mcmAhPTaE815bbdvh9rVI8b2y8Df8tOvF4j7jgU9cyHDRMTk3j44SVMm7YZX19h0KDG1K0b7HRYSikvpom0Usqz6d127xJ3zCMT6StXEhkyZCFffPEbgYF+fPFFH02ilVK5pom0UsqjufNhQ+ViCfFw9SIElsp83TwUF3eFPn2+ZNmyvRQvXoj//ncA7dtXdTospVQ+oIm0Usqj6fNfXuRSDBQO9qgf2unTl+jRI5yffz5M2bJFWLZsME2bel6Pucq+q1evcuTIES5fvux0KMqLBAYGEhISgr+/a4a51ERaKeXRPCclU5m6GANFyjodxXWuXEnkxIkLVKlSgpUrh1C7dv6Y90bBkSNHKFasGNWqVdMRV1SWGGOIjY3lyJEjVK9e3SXb1ERaKeXZ9ProPS7FQGHPSqTLlw9i1aqh+PoKlSt7z/jWKnOXL1/WJFpli4hQpkwZYmJiXLZNndlQKaWUa3hIIr1jx0kmTlzHtaG5q1UrqUl0PqVJtMouV//OaI+0Usqj6cOGXuRslFUj7aANG47QrdscTp++TNWqJRk8uLGj8Sil8jftkVZKeTSRnL9UHku4BP7OTW6ycuU+Onb8lNOnL9OzZx369KnvWCyq4Fi4cCEiwu7du92+rwMHDtCyZUtq1apFv379uHLlSqp1rl69yrBhw2jUqBH16tXjlVdeAaxSmBYtWtCkSRMaNGjA888/n+qzjzzyCEFBQW4/jvxEE2mllEeTXLxUHrscC8WdGVZuwYKddO8+l4sXrzJsWBO++qovgYF601W5X3h4OG3btmXevHlu39e4ceP417/+xZ49eyhVqhSffPJJqnW+/PJL4uPj2b59O5s3b2batGlERUVRqFAhVq9ezdatW4mMjGTZsmVs2LAh+XMRERGcjs3XOQAAGqNJREFUOXPG7ceQ32girZTyaNoj7UUu/elIacf06Vvo128BV68m8dhjLZkx4x78/PTyptzvwoUL/PTTT3zyySfXJdJr166lR48eye/HjBnDrFmzANi0aRO33norTZo0oUWLFpw/fz5L+zLGsHr1avr06QPAsGHDWLRoUar1RIS4uDgSEhK4dOkSAQEBFC9eHBFJ7m2+evUqV69eTa4XTkxMZOzYsUyaNClH34eCTP9cV0p5OM2IvcalPyEwb4eXi49P4M0315OUZHjppQ5MmNBOH0ArqN5ww8/9CZPh4kWLFtG1a1dq165N6dKl2bJlC02bNk13/StXrtCvXz/mz59P8+bNOXfuHIULF061XlhYGJGRkde1xcbGUrJkSfz8rNQtJCSEo0ePpvpsnz59+Oabb6hYsSIXL17krbfeonTp0oCVMN9yyy3s3buXhx9+mJYtWwIwdepUevbsScWKOsZ6dmkirZTyaJoTeZHLpyCwdJ7uslAhP1asGMLKlfsYPvzmPN238jCZJL3uEB4ezmOPPQZA//79CQ8PzzCR/v3336lYsSLNmzcHoHjx4mmud2MSDSSPQpNSWn80bty4EV9fX6Kjozl9+jTt2rXjzjvvpEaNGvj6+hIZGcmZM2fo3bs3O3bsoHTp0nz55ZesXbs2K4esbqCJtFJKKde4fCZPpgdPTEziyy930q9fA0SEkJDimkSrPBcbG8vq1avZsWMHIkJiYiIiwqRJk/Dz8yMpKSl53WuzLxpjcnzHJDg4mDNnzpCQkICfnx9HjhyhUqVKqdabO3cuXbt2xd/fn3LlytGmTRsiIiKoUaNG8jolS5bk9ttvZ9myZdSrV4+9e/cSGhoKwMWLFwkNDWXv3r05irOg0SIypZRH04cNvUj8aShU0r27iE9gwICvGDDgK156aZ1b96VURhYsWMDQoUM5ePAgUVFRHD58mOrVq/Pjjz9StWpVdu7cSXx8PGfPnuX7778HoG7dukRHR7Np0yYAzp8/T0JCQpb2JyJ06NCBBQsWADB79mzuueeeVOtVqVKF1atXY4whLi6ODRs2ULduXWJiYpIfJrx06RKrVq2ibt26dO/enePHjxMVFUVUVBRFihTRJDobNJFWSnk0fdjQWxi4ehEKpX2r2hUuXLjC3XeH8+WXOylRohB33OGaKX6Vyonw8HB69+59Xdt9993H3LlzqVy5Mn379qVx48YMGjSIm2+27pgEBAQwf/58HnnkEZo0aUKnTp2Se6tTCgsLS3Ofr732Gm+++SahoaHExsYyYsQIABYvXsxzzz0HwMMPP8yFCxdo2LAhzZs3Z/jw4TRu3Jhjx47RoUMHGjduTPPmzenUqdN1D0SqnJG0am48hMcGppTKthyntcfPXs3xuaBCCX9Np/NIs6ZhJmLEIXj4lFu2f+rUJbp1m8MvvxylXLmiLF8+mLCwCm7Zl/IOu3btol69ek6HobxQWr87IrLZGNMsu9vSGmmllGfTVNg7mEQIcM803NHR5+nc+TN++y2GqlVLsHLlEGrVytvRQZRSKi2aSCulPJrm0V4iKREKuSeR/sc/vuO332KoVy+YFSuGEBLivvIRpZTKDk2klVIeTWudvYRxXyL94Yc9CAz0Y+rUbgQHOzcFuVJK3UgfNlRKeTTJxX8qD7m4tOOPP2KTx82tUCGIefP6aBKtlPI4mkgrpZTKPZMIAcVcsqlly/YSFvYhTz21Ms1JKJRSylNoIq2U8mw6kLR3cFGN9Pz5O+jZM5xLlxKIjb2E5tFKKU+mibRSyqNpHu0lTFKue6SnTYtgwICvuHo1iSeeaM0nn/TEx0d/kspz+fr6EhYWRpMmTWjatCk///xzjrbz9ttvc/HixVTtvXv3JiwsjNDQUEqUKEFYWBhhYWH8/PPPVKtWjT///DO3h5BKUFBQttZ/4YUXeP3111O1R0VF0bBhwzQ/s2zZMurUqUNoaCivvvpqmuvMmjWLsmXLJh/z9OnTr1t+7tw5brrpJsaMGQNYk9tcWzcsLIzg4ODk6dvdSRNppZRH0wlZvEQuSjuMMbzyyg88+OB3GAMvv3wHkyd3yvFUykqlZc6cOVSrVg0fHx+qVavGnDlzcr3NwoULExkZydatW3nllVcYP358jraTXiK9cOFCIiMjmT59Ou3atSMyMpLIyEhuvfXWLG03q7Mm5qXExEQefvhhli5dys6dOwkPD2fnzp1prtuvX7/kYx45cuR1y5599lluu+225PfFihVLXjcyMpKqVaty7733uvVYQBNppZSH04cNvUQueqTfe28TzzyzGhH44IPujB/fTpNo5VJz5sxh9OjRHDx4EGMMBw8eZPTo0S5Jpq85d+4cpUqVSn4/efJkmjdvTuPGjXn++ecBiIuLo3v37jRp0oSGDRsyf/58pkyZQnR0NB06dKBDhw7Z2ue7775L06ZNadSoEbt37wasHuLRo0fTuXNnhg4dSmJiImPHjk2OZdq0aQAcO3aM9u3bExYWRsOGDfnhhx+StzthwgSaNGlCq1atOHHiBAAHDx6kY8eONG7cmI4dO3Lo0KFU8WzevJkmTZrQunVr3nvvvTRj3rhxI6GhodSoUYOAgAD69+/PN998k63j3rx5MydOnKBz585pLt+zZw8nT56kXbt22dpuTmgirZTyaNoj7SVMIvgVzdFH+/dvSOPG5QkPv48HH8z2xGJKZWrChAmpenwvXrzIhAkTcrXdS5cuERYWRt26dRk5ciTPPvssACtWrGDPnj1s3LiRyMhINm/ezLp161i2bBmVKlVi69at7Nixg65du/Loo49SqVIl1qxZw5o1a7K1/+DgYLZs2cJDDz10XXnF5s2b+eabb5g7dy6ffPIJJUqUYNOmTWzatImPP/6YAwcOMHfuXLp06ZLco35tWvK4uDhatWrF1q1bad++PR9//DEAY8aMYejQoWzbto1Bgwbx6KOPpopn+PDhTJkyhfXr11/XHh0dTbdu3QA4evQolStXTl4WEhLC0aNH0zy+r776isaNG9OnTx8OHz4MQFJSEk888QSTJ09O9/sSHh5Ov3798uQPck2klVJK5V42e6Tj4xNISrKeJAwOLsLmzaPp1y/tekqlciut3tOM2rPqWmnH7t27WbZsGUOHDsUYw4oVK1ixYgU333wzTZs2Zffu3ezZs4dGjRqxatUqxo0bxw8//ECJErl7QPda6cItt9xCVFRUcnvPnj0pXLgwYCX1n376KWFhYbRs2ZLY2Fj27NlD8+bNmTlzJi+88ALbt2+nWDHr329AQAA9evRItd3169czcOBAAIYMGcKPP/54XSxnz57lzJkzyeUWQ4YMSV5WqVIllixZApDmSDxpJbx33303UVFRbNu2jTvvvJNhw4YB8P7779OtW7frkvEbzZs3jwEDBqS73JV0QhallFK5l5QIAVl7SOn8+Xh6955P3brBvPvuXYgIfn7ar6Pcp0qVKhw8eDDNdldp3bo1f/75JzExMRhjGD9+PH//+99Trbd582aWLFnC+PHj6dy5M88991yO91moUCHAeugxZT100aJ/3R0yxvDuu+/SpUuXVJ9ft24d3333HUOGDGHs2LEMHToUf3//5MT2xu2mdGPya4zJUg9wSEhIcu8ywJEjR6hUqVKq9cqUKZP89ahRoxg3bhxgJfQ//PAD77//PhcuXODKlSsEBQUlP7S4detWEhISuOWWWzKNxRX0zKWU8mha2uElTBL4Z55Ix8Ze5M47P+P77w+wYMFOjh+/kAfBqYJu4sSJFCly/YQ+RYoUYeLEiS7bx+7du0lMTKRMmTJ06dKFGTNmcOGC9ft99OhRTp48SXR0NEWKFGHw4ME8+eSTbNmyBbAelDt//rzLYkmpS5cufPDBB1y9ehWAP/74g7i4OA4ePEi5cuUYNWoUI0aMSI4lPbfeeivz5s0DrJrztm3bXre8ZMmSlChRIrmnOr368+bNm7Nnzx4OHDjAlStXmDdvHj179ky13rFjx5K/Xrx4MfXq1Uve7qFDh4iKiuL1119n6NCh1438ER4enme90aA90kopD6cPDXqLJPDPuEb66NFzdO78OTt3xlCtWklWrhxCxYqumcRFqYwMGjQIsGqlDx06RJUqVZg4cWJye05dq5EGq0d29uzZ+Pr60rlzZ3bt2kXr1q0Ba0i5zz//nL179zJ27Fh8fHzw9/fngw8+AGD06NHcddddVKxYMdt10pkZOXIkUVFRNG3aFGMMZcuWZdGiRaxdu5bJkyfj7+9PUFAQn376aYbbmTJlCg888ACTJ0+mbNmyzJw5M9U6M2fO5IEHHqBIkSLX9YBHR0czcuRIlixZgp+fH1OnTqVLly4kJibywAMP0KBBAwCee+45mjVrRs+ePZkyZQqLFy/Gz8+P0qVLM2vWrCwd7xdffJFcRpIXxINnjfLYwJRS2ZbjbPjc5aQcnwuKB+ogxHmlWbXCJiIiAoIbpLl8z55YOnX6jIMHz9KgQVmWLx/MTTcVz+MoVX6ya9eu5F5KpbIjrd8dEdlsjMn2087aI62U8miaCXuLJPArnOaSnTtj6NBhNidPxtGy5U0sWTKI0qXTXlcppbyJJtJKKc+mmbR3MEngF5jmokqVilGhQhCNG5dn4cJ+BAUF5HFwSinlHppIK6U8mtZIewlj0u2RLlkykFWrhlC8eCEKFdLLjnKdrI4UodQ1ri5p1lE7lFJKuUAS+P7V0xwevp2HHvo2+aJVtmxRTaKVSwUGBhIbG+vyxEjlX8YYYmNjCQxM++5ZTuhZTSnl0bSzyUsYAz7+ALz//ibGjFmCMdCjR226d6/tcHAqPwoJCeHIkSPExMQ4HYryIoGBgYSEhLhse5pIK6U8mubR3sOIHxP/s45nn7WG73r11Y6aRCu38ff3p3r16k6HoQo4Le1QSnk2ycVLpUlEuorI7yKyV0SeTmN5IRGZby//RUSqZWW7jz+xgmefXYMITJvWg3Hj2mb+IaWU8mLaI62U8mj6sKFriYgv8B7QCTgCbBKRxcaYnSlWGwGcNsaEikh/4DWgX0bbjTpVks1v/4K/vw+ff34vffumPZ60UkrlJ9ojrZTyaDpFuMu1APYaY/YbY64A84B7bljnHmC2/fUCoKNkMjTCpQR/ihTx57//HaBJtFKqwPDkHukCdxkUkdHGmI+cjkO5l/6csyfQr+CdC9zsJuBwivdHgJbprWOMSRCRs0AZ4M+UK4nIaGC0/TaeKxN2dO06wS1Be6hgbvieFAB6zAVDQTzmOjn5kCcn0gXRaEATrPxPf87KSWn9YXLj+GFZWQf7D8KPAEQkIifT63ozPeaCQY+5YBCRiJx8Tks7lFKqYDkCVE7xPgSITm8dEfEDSgCn8iQ6pZTyIppIK6VUwbIJqCUi1UUkAOgPLL5hncXAMPvrPsBqo7NeKKVUKlra4Vn0dn/BoD9n5Ri75nkMsBzwBWYYY34TkReBCGPMYuAT4DMR2YvVE90/C5suiL/XeswFgx5zwZCjYxbtZFBKKaWUUir7tLRDKaWUUkqpHNBEWimllFJKqRzQRNpDZDZlr/J+IjJDRE6KyA6nY1Eqp9w1vbgny8IxPy4iO0Vkm4h8LyJVnYjTlbJ6TRKRPiJiRMTrh0rLyjGLSF/7Z/2biMzN6xhdLQu/21VEZI2I/Gr/fndzIk5Xyew6LJYp9vdjm4g0zWybmkh7gBRT9t4F1AcGiEh9Z6NSbjAL6Op0EErlVBbPVcnTiwNvYU0v7rWyeMy/As2MMY2xZoKclLdRulZWr0kiUgx4FPglbyN0vawcs4jUAsYDbYwxDYDH8jxQF8riz/nfwBfGmJuxHjp+P2+jdLlZZHwdvguoZb9GAx9ktkFNpD1DVqbsVV7OGLMOHYtXeTe3TC/u4TI9ZmPMGmPMRfvtBqyxub1ZVq9JL2H90XA5L4Nzk6wc8yjgPWPMaQBjzMk8jtHVsnLMBihuf12C1GPOe5UsXIfvAT41lg1ASRGpmNE2NZH2DGlN2XuTQ7EopVR6snKuum56ceDa9OLeKrvn5xHAUrdG5H6ZHrOI3AxUNsZ8m5eBuVFWfs61gdoi8pOIbBARb7/DmJVjfgEYLCJHgCXAI3kTmmOynY/pONKeIUvT8SqllMNcNr24F8ny8YjIYKAZcJtbI3K/DI9ZRHywynbuz6uA8kBWfs5+WLf8b8e66/CDiDQ0xpxxc2zukpVjHgDMMsa8ISKtscaXb2iMSXJ/eI7I9vlLe6Q9Q1am7FVKKacVxOnFs3R+FpE7gQlAT2NMfB7F5i6ZHXMxoCGwVkSigFbAYi9/4DCrv9vfGGOuGmMOAL9jJdbeKivHPAL4AsAYsx4IBILzJDpnZDsf00TaM2Rlyl6llHJaQZxePNNjtsscpmEl0d5eNwuZHLMx5qwxJtgYU80YUw2rLrynMSbCmXBdIiu/24uADgAiEoxV6rE/T6N0rawc8yGgI4CI1MNKpGPyNMq8tRgYao/e0Qo4a4w5ltEHtLTDA6Q3Za/DYSkXE5FwrFuCwXa92fPGmE+cjUqprHPj9OIeK4vHPBkIAr60n6s8ZIzp6VjQuZTFY85XsnjMy4HOIrITSATGGmNinYs6d7J4zE8AH4vIv7BKHO735j+M07oOA/4AxpgPserAuwF7gYvA8Ey36cXfD6WUUkoppRyjpR1KKaWUUkrlgCbSSimllFJK5YAm0koppZRSSuWAJtJKKaWUUkrlgCbSSimllFJK5YAm0vmYiCSKSKSI7BCRL0WkSC62dbuIfGt/3VNEns5g3ZIi8o8c7OMFEXkyjf2uv6HNT0ROiEjF7GxLKaUKuhTXhWuvahmsW01Edrhgn2tF5HcR2WpPr10nB9t4UESG2l/fLyKVUiybLiL1XRznJhEJy8JnHsvNtVV5P02k87dLxpgwY0xD4ArwYMqF9oDj2f4dMMYsNsa8msEqJYFsJ9LpWAeE3HCyvxPYkdkg6UoppVK5dl249orKo/0OMsY0AWZjjbudLcaYD40xn9pv7wcqpVg20hiz0yVR/hXn+2QtzscATaQLME2kC44fgFC7h2GXiLwPbAEqi0hnEVkvIlvsnusgABHpKiK7ReRH4N5rG7J7A6baX5cXkYX2X/BbReRW4FWgpt3bMdleb6z9F/42Efm/FNuaYPcArAJS9VIYY5KAL4F+KZr7A+H250fZ290qIl+l1TNg9zI0s78OFmtKW0TEV0Qmp4jr73Z7RRFZl6I3v11Ov+lKKeXp7OvCD/Y1YIt9Hr9xnQYistE+L24TkVp2++AU7dNExDeT3a0DQu3PdhSRX0Vku4jMEJFCdvurIrLT3s/rdtsLIvKkiPQBmgFz7H0WvnaOF5GHRGRSipjvF5F3cxjneuCmFNv6QEQiROS3a9cwEXkUK6FfIyJr7LY0r6cq/9JEugAQET/gLmC73VQH+NQYczMQB/wbuNMY0xSIAB4XkUDgY+BuoB1QIZ3NTwH+Z/8F3xT4DXga2Gf3dowVkc5ALaAFEAbcIiLtReQWrKT4ZqxEvXk6+wi318M+0XYDvrKXfW2MaW7vfxcwIhvfmhFY0382t/c9SkSqAwOB5caYMKAJEJmNbSqllCcrLH+VdSy0204CnexrQD+s8/qNHgTesc+LzYAjYk0Z3Q9oY7cnAoMy2f/dwHb7GjML6GeMaYQ10/JDIlIa6A00MMY0Bv6T8sPGmAVY16lB9jXmUorFC0jR6WPHNj+HcXbFmhL8mgnGmGZAY+A2EWlsjJkCRAMdjDEdxJo2PNX1NJP9KC+nU4Tnb4VF5FoS+APW1L2VgIPGmA12eyugPvCTWFPbBmD9JV4XOGCM2QMgIp8Do9PYxx3AUABjTCJwVkRK3bBOZ/v1q/0+CCuxLgYsNMZctPeR5rSzxphNIhIkVl1dPWCDMea0vbihiPwHq5wkCGuq06zqDDS2ezgASthxbQJmiIg/sMgYo4m0Uiq/uGQnkyn5A1PFqglOBGqn8bn1wAQRCcHqwNgjIh2BW4BN9vWjMFZSnpY5InIJiAIewerQOWCM+cNePht4GJgKXAami8h3wLdZPTBjTIyI7BeRVsAeex8/2dvNTpxFsabMbpqiva+IjMbKmypiXTe33fDZ9K6nKh/TRDp/S3XCtP9xx6VsAlYaYwbcsF4Y4Kr54wV4xRgz7YZ9PJaNfczD6pWuh13WYZsF9DLGbBWR+4Hb0/hsAn/dfQm8Ia5HjDGpkm8RaQ90Bz4TkckpavOUUiq/+RdwAusOnA9WInsdY8xcEfkF67y4XERGYp1DZxtjxmdhH4OMMRHX3ohImbRWMsYkiEgLoCPWOX8MVodNVs0H+gK7sTpqjFgXvizHCWzFKlF8D7jXvlP5JNDcGHNaRGZx/bUk+bBI43qq8jct7VAbgDYicq1mrYiI1MY6CVUXkZr2eumdGL4HHrI/6ysixYHzWL3N1ywHHpC/aq9vEpFyWLVyve0at2JYt/zSEw4Mxjqhpuy5LgYcs3uP07tVF4XVGwHQJ0X7cqxbif52XLVFpKiIVAVOGmM+xurFT9kroZRS+U0J4Jj9TMoQrN7Y64hIDWC/Xc6wGKvE4Xugj30+R0RK2+fPrNgNVLt27bH3+z/7OlHCGLME60G+tEbOuPEak9LXQC+sa9Z8uy1bcRpjrmKVaLSyy0KKY3VAnRWR8lilkmnFkt71VOVjmkgXcMaYGKwnoMNFZBvWiaCuMeYyVinHd2I9bHgwnU38E+ggItuBzVh1bbFYt7Z22L25K4C5wHp7vQVAMWPMFqwTXSRWzfMPGcS5E7gIrDbGpOxRfxb4BViJdWJOy+tYCfPPQHCK9unATmCLWEM8TcO6S3M7ECkivwL3Ae+kF5dSSuUD7wPDRGQDVllHXBrr9AN22OWCdbGes9mJlXCusK8fK7HKHjJlX2OGA1/a14Uk4EOspPRbe3v/w+otv9Es4MNrDxvesN3TWOf1qsaYjXZbtuO0a6/fAJ40xmzFKk38DZiBVS5yzUfAUhFZk971NCvfD+W9xBhX3b1XSimllFKq4NAeaaWUUkoppXJAE2mllFJKKaVyQBNppZRSSimlckATaaWUUkoppXJAE2mllFJKKaVyQBNppZRSSimlckATaaWUUkoppXLg/wHYY2rrDpNDsgAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 864x864 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["probabilities = classifier.predict_proba(X_test_sc)\n", "predictions = classifier.predict_classes(X_test_sc)\n", "\n", "print (\"\\n Classification report : \\n\",classification_report(y_test, predictions))\n", "print (\"Accuracy   Score : \",accuracy_score(y_test, predictions))\n", "\n", "#confusion matrix\n", "conf_matrix = confusion_matrix(y_test,predictions)\n", "plt.figure(figsize=(12,12))\n", "plt.subplot(221)\n", "sns.heatmap(conf_matrix, fmt = \"d\",annot=True, cmap='Blues')\n", "b, t = plt.ylim()\n", "plt.ylim(b + 0.5, t - 0.5)\n", "plt.title('Confuion Matrix')\n", "plt.ylabel('True Values')\n", "plt.xlabel('Predicted Values')\n", "\n", "#f1-score\n", "f1 = f1_score(y_test, predictions)\n", "print(\"F1 Score: \", f1)\n", "\n", "#roc_auc_score\n", "model_roc_auc = roc_auc_score(y_test,probabilities) \n", "print (\"Area under curve : \",model_roc_auc,\"\\n\")\n", "fpr,tpr,thresholds = roc_curve(y_test,probabilities)\n", "gmeans = np.sqrt(tpr * (1-fpr))\n", "ix = np.argmax(gmeans)\n", "threshold = np.round(thresholds[ix],3)\n", "\n", "plt.subplot(222)\n", "plt.plot(fpr, tpr, color='darkorange', lw=1, label = \"Auc : %.3f\" %model_roc_auc)\n", "plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')\n", "plt.scatter(fpr[ix], tpr[ix], marker='o', color='black', label='Best Threshold:' + str(threshold))\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver operating characteristic')\n", "plt.legend(loc=\"lower right\")\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}