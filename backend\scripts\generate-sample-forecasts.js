const mongoose = require('mongoose');
const DemandForecast = require('../models/DemandForecast');
const Product = require('../models/Product');

async function generateSampleForecasts() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect('mongodb://localhost:27017/inventory_management');
    console.log('✅ Connected to MongoDB');

    // Check if forecasts already exist
    const existingCount = await DemandForecast.countDocuments();
    console.log(`📊 Found ${existingCount} existing forecasts`);

    if (existingCount > 0) {
      console.log('✅ Demand forecasts already exist');
      process.exit(0);
    }

    // Get some products to create forecasts for
    const products = await Product.find().limit(10);
    console.log(`📦 Found ${products.length} products`);

    if (products.length === 0) {
      console.log('❌ No products found. Creating sample products first...');
      
      // Create sample products
      const sampleProducts = [
        { name: '<PERSON>ana<PERSON>', sku: 'FRUIT-001', category: 'Fresh Fruits', quantity: 150, price: 1.29 },
        { name: 'Organic Milk', sku: 'DAIRY-001', category: 'Dairy', quantity: 80, price: 4.99 },
        { name: 'Bread', sku: 'BAKERY-001', category: 'Bakery', quantity: 45, price: 2.49 },
        { name: 'Chicken Breast', sku: 'MEAT-001', category: 'Meat', quantity: 30, price: 8.99 },
        { name: 'Apples', sku: 'FRUIT-002', category: 'Fresh Fruits', quantity: 120, price: 2.99 }
      ];

      for (const productData of sampleProducts) {
        await Product.create(productData);
      }
      
      console.log('✅ Created sample products');
      const newProducts = await Product.find().limit(5);
      products.push(...newProducts);
    }

    console.log('🔮 Generating sample demand forecasts...');

    // Generate forecasts for each product
    for (const product of products.slice(0, 5)) {
      const forecastData = [];
      const baseDate = new Date();
      
      // Generate 30 days of forecast data
      for (let i = 0; i < 30; i++) {
        const date = new Date(baseDate);
        date.setDate(date.getDate() + i);
        
        // Generate realistic demand patterns
        const baseDemand = Math.random() * 20 + 5; // 5-25 base demand
        const weekendBoost = date.getDay() === 0 || date.getDay() === 6 ? 1.3 : 1.0;
        const seasonalFactor = 1 + 0.2 * Math.sin((date.getMonth() / 12) * 2 * Math.PI);
        
        const predictedDemand = Math.round(baseDemand * weekendBoost * seasonalFactor);
        const variance = predictedDemand * 0.2;
        
        forecastData.push({
          date: date,
          predicted_demand: predictedDemand,
          lower_bound: Math.max(0, Math.round(predictedDemand - variance)),
          upper_bound: Math.round(predictedDemand + variance),
          confidence_interval: 0.95
        });
      }

      const forecast = new DemandForecast({
        product_id: product._id,
        product_name: product.name,
        product_sku: product.sku,
        model_type: 'Prophet',
        forecast_horizon_days: 30,
        forecast_data: forecastData,
        model_accuracy: 0.85 + Math.random() * 0.1, // 85-95% accuracy
        status: 'active',
        forecast_generated_at: new Date(),
        last_updated: new Date()
      });

      await forecast.save();
      console.log(`✅ Created forecast for ${product.name}`);
    }

    console.log('🎉 Successfully generated sample demand forecasts!');
    
    // Verify the forecasts were created
    const finalCount = await DemandForecast.countDocuments();
    console.log(`📊 Total forecasts in database: ${finalCount}`);

  } catch (error) {
    console.error('❌ Error generating forecasts:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
generateSampleForecasts();
