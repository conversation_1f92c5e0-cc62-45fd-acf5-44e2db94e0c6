{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Market Basket Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Market basket analysis scrutinizes the products customers tend to buy together, and uses the information to decide which products should be cross-sold or promoted together. The term arises from the shopping carts supermarket shoppers fill up during a shopping trip.\n", "\n", "Association Rule Mining is used when we want to find an association between different objects in a set, find frequent patterns in a transaction database, relational databases or any other information repository.\n", "\n", "The most common approach to find these patterns is Market Basket Analysis, which is a key technique used by large retailers like Amazon, Flipkart, etc to analyze customer buying habits by finding associations between the different items that customers place in their “shopping baskets”. The discovery of these associations can help retailers develop marketing strategies by gaining insight into which items are frequently purchased together by customers. The strategies may include:\n", "\n", "- Changing the store layout according to trends\n", "- Customers behavior analysis\n", "- Catalog Design\n", "- Cross marketing on online stores\n", "- Customized emails with add-on sales, etc."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Matrices\n", "\n", "- **Support** : Its the default popularity of an item. In mathematical terms, the support of item A is the ratio of transactions involving A to the total number of transactions.\n", "\n", "\n", "- **Confidence** : Likelihood that customer who bought both A and B. It is the ratio of the number of transactions involving both A and B and the number of transactions involving B.\n", "     - Confidence(A => B) = Support(A, B)/Support(A)\n", "\n", "\n", "- **Lift** : Increase in the sale of A when you sell B.\n", "    \n", "    - Lift(A => B) = Confidence(A, B)/Support(B)\n", "        \n", "    - Lift (A => B) = 1 means that there is no correlation within the itemset.\n", "    - Lift (A => B) > 1 means that there is a positive correlation within the itemset, i.e., products in the itemset, A, and B, are more likely to be bought together.\n", "    - Lift (A => B) < 1 means that there is a negative correlation within the itemset, i.e., products in itemset, A, and B, are unlikely to be bought together."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Apriori Algorithm:** Apriori algorithm assumes that any subset of a frequent itemset must be frequent. Its the algorithm behind Market Basket Analysis. Say, a transaction containing {Grapes, Apple, Mango} also contains {Grapes, Mango}. So, according to the principle of Apriori, if {Grapes, Apple, Mango} is frequent, then {<PERSON>rap<PERSON>, Mango} must also be frequent."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from mlxtend.frequent_patterns import apriori\n", "from mlxtend.frequent_patterns import association_rules\n", "\n", "root = 'C:/Data/instacart-market-basket-analysis/'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["orders = pd.read_csv(root + 'orders.csv')\n", "order_products_prior = pd.read_csv(root + 'order_products__prior.csv')\n", "order_products_train = pd.read_csv(root + 'order_products__train.csv')\n", "products = pd.read_csv(root + 'products.csv')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(33819106, 4)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["order_products = order_products_prior.append(order_products_train)\n", "order_products.shape"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>product_id</th>\n", "      <th>add_to_cart_order</th>\n", "      <th>reordered</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>33120</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>28985</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>9327</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>45918</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>30035</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   order_id  product_id  add_to_cart_order  reordered\n", "0         2       33120                  1          1\n", "1         2       28985                  2          1\n", "2         2        9327                  3          0\n", "3         2       45918                  4          1\n", "4         2       30035                  5          0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["order_products.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["49685"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["order_products.product_id.nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Out of 49685 keeping top 100 most frequent products."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>frequency</th>\n", "      <th>product_name</th>\n", "      <th>aisle_id</th>\n", "      <th>department_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>24852</td>\n", "      <td>491291</td>\n", "      <td>Banana</td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>13176</td>\n", "      <td>394930</td>\n", "      <td>Bag of Organic Bananas</td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21137</td>\n", "      <td>275577</td>\n", "      <td>Organic Strawberries</td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21903</td>\n", "      <td>251705</td>\n", "      <td>Organic Baby Spinach</td>\n", "      <td>123</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>47209</td>\n", "      <td>220877</td>\n", "      <td>Organic Hass Avocado</td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>47766</td>\n", "      <td>184224</td>\n", "      <td>Organic Avocado</td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>47626</td>\n", "      <td>160792</td>\n", "      <td>Large Lemon</td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>16797</td>\n", "      <td>149445</td>\n", "      <td>Strawberries</td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>26209</td>\n", "      <td>146660</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>27845</td>\n", "      <td>142813</td>\n", "      <td>Organic Whole Milk</td>\n", "      <td>84</td>\n", "      <td>16</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   product_id  frequency            product_name  aisle_id  department_id\n", "0       24852     491291                  Banana        24              4\n", "1       13176     394930  Bag of Organic Bananas        24              4\n", "2       21137     275577    Organic Strawberries        24              4\n", "3       21903     251705    Organic Baby Spinach       123              4\n", "4       47209     220877    Organic Hass Avocado        24              4\n", "5       47766     184224         Organic Avocado        24              4\n", "6       47626     160792             Large Lemon        24              4\n", "7       16797     149445            Strawberries        24              4\n", "8       26209     146660                   Limes        24              4\n", "9       27845     142813      Organic Whole Milk        84             16"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["product_counts = order_products.groupby('product_id')['order_id'].count().reset_index().rename(columns = {'order_id':'frequency'})\n", "product_counts = product_counts.sort_values('frequency', ascending=False)[0:100].reset_index(drop = True)\n", "product_counts = product_counts.merge(products, on = 'product_id', how = 'left')\n", "product_counts.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Keeping 100 most frequent items in order_products dataframe"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["[13176, 21137, 21903, 47209, 47766, 47626, 16797, 26209, 27845]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["freq_products = list(product_counts.product_id)\n", "freq_products[1:10]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["len(freq_products)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["(7795471, 4)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["order_products = order_products[order_products.product_id.isin(freq_products)]\n", "order_products.shape"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["2444982"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["order_products.order_id.nunique()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>product_id</th>\n", "      <th>add_to_cart_order</th>\n", "      <th>reordered</th>\n", "      <th>product_name</th>\n", "      <th>aisle_id</th>\n", "      <th>department_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>28985</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>Michigan Organic Kale</td>\n", "      <td>83</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>17794</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>Carrots</td>\n", "      <td>83</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>24838</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>Unsweetened Almondmilk</td>\n", "      <td>91</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>21903</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>Organic Baby Spinach</td>\n", "      <td>123</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3</td>\n", "      <td>46667</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>Organic Ginger Root</td>\n", "      <td>83</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   order_id  product_id  add_to_cart_order  reordered            product_name  \\\n", "0         2       28985                  2          1   Michigan Organic Kale   \n", "1         2       17794                  6          1                 Carrots   \n", "2         3       24838                  2          1  Unsweetened Almondmilk   \n", "3         3       21903                  4          1    Organic Baby Spinach   \n", "4         3       46667                  6          1     Organic Ginger Root   \n", "\n", "   aisle_id  department_id  \n", "0        83              4  \n", "1        83              4  \n", "2        91             16  \n", "3       123              4  \n", "4        83              4  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["order_products = order_products.merge(products, on = 'product_id', how='left')\n", "order_products.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Structuring the data for feeding in the algorithm"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>product_name</th>\n", "      <th>100% Raw Coconut Water</th>\n", "      <th>100% Whole Wheat Bread</th>\n", "      <th>2% Reduced Fat Milk</th>\n", "      <th>Apple Honeycrisp Organic</th>\n", "      <th>As<PERSON>agu<PERSON></th>\n", "      <th>Bag of Organic Bananas</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON></th>\n", "      <th>Blueberries</th>\n", "      <th>Boneless Skinless Chicken Breasts</th>\n", "      <th>...</th>\n", "      <th>Sparkling Natural Mineral Water</th>\n", "      <th>Sparkling Water Grapefruit</th>\n", "      <th>Spring Water</th>\n", "      <th>Strawberries</th>\n", "      <th>Uncured Genoa Salami</th>\n", "      <th>Unsalted Butter</th>\n", "      <th>Unsweetened Almondmilk</th>\n", "      <th>Unsweetened Original Almond Breeze Almond Milk</th>\n", "      <th>Whole Milk</th>\n", "      <th>Yellow Onions</th>\n", "    </tr>\n", "    <tr>\n", "      <th>order_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 100 columns</p>\n", "</div>"], "text/plain": ["product_name  100% Raw Coconut Water  100% Whole Wheat Bread  \\\n", "order_id                                                       \n", "1                                0.0                     0.0   \n", "2                                0.0                     0.0   \n", "3                                0.0                     0.0   \n", "5                                0.0                     0.0   \n", "9                                0.0                     0.0   \n", "\n", "product_name  2% Reduced Fat Milk  Apple Honeycrisp Organic  Asparagus  \\\n", "order_id                                                                 \n", "1                             0.0                       0.0        0.0   \n", "2                             0.0                       0.0        0.0   \n", "3                             0.0                       0.0        0.0   \n", "5                             1.0                       0.0        0.0   \n", "9                             0.0                       0.0        0.0   \n", "\n", "product_name  Bag of Organic Bananas  Banana  Bartlett Pears  Blueberries  \\\n", "order_id                                                                    \n", "1                                1.0     0.0             0.0          0.0   \n", "2                                0.0     0.0             0.0          0.0   \n", "3                                0.0     0.0             0.0          0.0   \n", "5                                1.0     0.0             0.0          0.0   \n", "9                                0.0     0.0             0.0          0.0   \n", "\n", "product_name  Boneless Skinless Chicken Breasts  ...  \\\n", "order_id                                         ...   \n", "1                                           0.0  ...   \n", "2                                           0.0  ...   \n", "3                                           0.0  ...   \n", "5                                           0.0  ...   \n", "9                                           0.0  ...   \n", "\n", "product_name  Sparkling Natural Mineral Water  Sparkling Water Grapefruit  \\\n", "order_id                                                                    \n", "1                                         0.0                         0.0   \n", "2                                         0.0                         0.0   \n", "3                                         0.0                         0.0   \n", "5                                         0.0                         0.0   \n", "9                                         0.0                         0.0   \n", "\n", "product_name  Spring Water  Strawberries  Uncured Genoa Salami  \\\n", "order_id                                                         \n", "1                      0.0           0.0                   0.0   \n", "2                      0.0           0.0                   0.0   \n", "3                      0.0           0.0                   0.0   \n", "5                      0.0           0.0                   0.0   \n", "9                      0.0           0.0                   0.0   \n", "\n", "product_name  Unsalted Butter  Unsweetened Almondmilk  \\\n", "order_id                                                \n", "1                         0.0                     0.0   \n", "2                         0.0                     0.0   \n", "3                         0.0                     1.0   \n", "5                         0.0                     0.0   \n", "9                         0.0                     0.0   \n", "\n", "product_name  Unsweetened Original Almond Breeze Almond Milk  Whole Milk  \\\n", "order_id                                                                   \n", "1                                                        0.0         0.0   \n", "2                                                        0.0         0.0   \n", "3                                                        0.0         0.0   \n", "5                                                        0.0         0.0   \n", "9                                                        0.0         0.0   \n", "\n", "product_name  Yellow Onions  \n", "order_id                     \n", "1                       0.0  \n", "2                       0.0  \n", "3                       0.0  \n", "5                       0.0  \n", "9                       0.0  \n", "\n", "[5 rows x 100 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["basket = order_products.groupby(['order_id', 'product_name'])['reordered'].count().unstack().reset_index().fillna(0).set_index('order_id')\n", "basket.head()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["del product_counts, products, order_products, order_products_prior, order_products_train"]}, {"cell_type": "markdown", "metadata": {}, "source": ["encoding the units"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>product_name</th>\n", "      <th>100% Raw Coconut Water</th>\n", "      <th>100% Whole Wheat Bread</th>\n", "      <th>2% Reduced Fat Milk</th>\n", "      <th>Apple Honeycrisp Organic</th>\n", "      <th>As<PERSON>agu<PERSON></th>\n", "      <th>Bag of Organic Bananas</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON></th>\n", "      <th>Blueberries</th>\n", "      <th>Boneless Skinless Chicken Breasts</th>\n", "      <th>...</th>\n", "      <th>Sparkling Natural Mineral Water</th>\n", "      <th>Sparkling Water Grapefruit</th>\n", "      <th>Spring Water</th>\n", "      <th>Strawberries</th>\n", "      <th>Uncured Genoa Salami</th>\n", "      <th>Unsalted Butter</th>\n", "      <th>Unsweetened Almondmilk</th>\n", "      <th>Unsweetened Original Almond Breeze Almond Milk</th>\n", "      <th>Whole Milk</th>\n", "      <th>Yellow Onions</th>\n", "    </tr>\n", "    <tr>\n", "      <th>order_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 100 columns</p>\n", "</div>"], "text/plain": ["product_name  100% Raw Coconut Water  100% Whole Wheat Bread  \\\n", "order_id                                                       \n", "1                                  0                       0   \n", "2                                  0                       0   \n", "3                                  0                       0   \n", "5                                  0                       0   \n", "9                                  0                       0   \n", "\n", "product_name  2% Reduced Fat Milk  Apple Honeycrisp Organic  Asparagus  \\\n", "order_id                                                                 \n", "1                               0                         0          0   \n", "2                               0                         0          0   \n", "3                               0                         0          0   \n", "5                               1                         0          0   \n", "9                               0                         0          0   \n", "\n", "product_name  Bag of Organic Bananas  Banana  Bartlett Pears  Blueberries  \\\n", "order_id                                                                    \n", "1                                  1       0               0            0   \n", "2                                  0       0               0            0   \n", "3                                  0       0               0            0   \n", "5                                  1       0               0            0   \n", "9                                  0       0               0            0   \n", "\n", "product_name  Boneless Skinless Chicken Breasts  ...  \\\n", "order_id                                         ...   \n", "1                                             0  ...   \n", "2                                             0  ...   \n", "3                                             0  ...   \n", "5                                             0  ...   \n", "9                                             0  ...   \n", "\n", "product_name  Sparkling Natural Mineral Water  Sparkling Water Grapefruit  \\\n", "order_id                                                                    \n", "1                                           0                           0   \n", "2                                           0                           0   \n", "3                                           0                           0   \n", "5                                           0                           0   \n", "9                                           0                           0   \n", "\n", "product_name  Spring Water  Strawberries  Uncured Genoa Salami  \\\n", "order_id                                                         \n", "1                        0             0                     0   \n", "2                        0             0                     0   \n", "3                        0             0                     0   \n", "5                        0             0                     0   \n", "9                        0             0                     0   \n", "\n", "product_name  Unsalted Butter  Unsweetened Almondmilk  \\\n", "order_id                                                \n", "1                           0                       0   \n", "2                           0                       0   \n", "3                           0                       1   \n", "5                           0                       0   \n", "9                           0                       0   \n", "\n", "product_name  Unsweetened Original Almond Breeze Almond Milk  Whole Milk  \\\n", "order_id                                                                   \n", "1                                                          0           0   \n", "2                                                          0           0   \n", "3                                                          0           0   \n", "5                                                          0           0   \n", "9                                                          0           0   \n", "\n", "product_name  Yellow Onions  \n", "order_id                     \n", "1                         0  \n", "2                         0  \n", "3                         0  \n", "5                         0  \n", "9                         0  \n", "\n", "[5 rows x 100 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["def encode_units(x):\n", "    if x <= 0:\n", "        return 0\n", "    if x >= 1:\n", "        return 1 \n", "    \n", "basket = basket.applymap(encode_units)\n", "basket.head()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["244498200"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["basket.size"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2444982, 100)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["basket.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Creating frequent sets and rules"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.016062</td>\n", "      <td>(100% Raw Coconut Water)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.025814</td>\n", "      <td>(100% Whole Wheat Bread)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.015800</td>\n", "      <td>(2% Reduced Fat Milk)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.035694</td>\n", "      <td>(Apple Honeycrisp Organic)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.029101</td>\n", "      <td>(<PERSON><PERSON><PERSON><PERSON>)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    support                    itemsets\n", "0  0.016062    (100% Raw Coconut Water)\n", "1  0.025814    (100% Whole Wheat Bread)\n", "2  0.015800       (2% Reduced Fat Milk)\n", "3  0.035694  (Apple Honeycrisp Organic)\n", "4  0.029101                 (<PERSON><PERSON><PERSON><PERSON>)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["frequent_items = apriori(basket, min_support=0.01, use_colnames=True, low_memory=True)\n", "frequent_items.head()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>support</th>\n", "      <th>itemsets</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>124</th>\n", "      <td>0.010235</td>\n", "      <td>(Organic Blueberries, Organic Strawberries)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>125</th>\n", "      <td>0.010966</td>\n", "      <td>(Organic Raspberries, Organic Hass Avocado)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>0.017314</td>\n", "      <td>(Organic Strawberries, Organic Hass Avocado)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>127</th>\n", "      <td>0.014533</td>\n", "      <td>(Organic Strawberries, Organic Raspberries)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128</th>\n", "      <td>0.010130</td>\n", "      <td>(Organic Strawberries, Organic Whole Milk)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      support                                      itemsets\n", "124  0.010235   (Organic Blueberries, Organic Strawberries)\n", "125  0.010966   (Organic Raspberries, Organic Hass Avocado)\n", "126  0.017314  (Organic Strawberries, Organic Hass Avocado)\n", "127  0.014533   (Organic Strawberries, Organic Raspberries)\n", "128  0.010130    (Organic Strawberries, Organic Whole Milk)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["frequent_items.tail()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["(129, 2)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["frequent_items.shape"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>antecedents</th>\n", "      <th>consequents</th>\n", "      <th>antecedent support</th>\n", "      <th>consequent support</th>\n", "      <th>support</th>\n", "      <th>confidence</th>\n", "      <th>lift</th>\n", "      <th>leverage</th>\n", "      <th>conviction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>(<PERSON><PERSON>)</td>\n", "      <td>(<PERSON> Lemon)</td>\n", "      <td>0.059984</td>\n", "      <td>0.065764</td>\n", "      <td>0.011860</td>\n", "      <td>0.197723</td>\n", "      <td>3.006544</td>\n", "      <td>0.007915</td>\n", "      <td>1.164480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>(<PERSON> Lemon)</td>\n", "      <td>(<PERSON><PERSON>)</td>\n", "      <td>0.065764</td>\n", "      <td>0.059984</td>\n", "      <td>0.011860</td>\n", "      <td>0.180345</td>\n", "      <td>3.006544</td>\n", "      <td>0.007915</td>\n", "      <td>1.146843</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>0.112711</td>\n", "      <td>0.058325</td>\n", "      <td>0.014533</td>\n", "      <td>0.128940</td>\n", "      <td>2.210731</td>\n", "      <td>0.007959</td>\n", "      <td>1.081069</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.058325</td>\n", "      <td>0.112711</td>\n", "      <td>0.014533</td>\n", "      <td>0.249174</td>\n", "      <td>2.210731</td>\n", "      <td>0.007959</td>\n", "      <td>1.181751</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>(Organic Avocado)</td>\n", "      <td>(<PERSON> Lemon)</td>\n", "      <td>0.075348</td>\n", "      <td>0.065764</td>\n", "      <td>0.010538</td>\n", "      <td>0.139862</td>\n", "      <td>2.126728</td>\n", "      <td>0.005583</td>\n", "      <td>1.086147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>(<PERSON> Lemon)</td>\n", "      <td>(Organic Avocado)</td>\n", "      <td>0.065764</td>\n", "      <td>0.075348</td>\n", "      <td>0.010538</td>\n", "      <td>0.160244</td>\n", "      <td>2.126728</td>\n", "      <td>0.005583</td>\n", "      <td>1.101097</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(Organic Blueberries)</td>\n", "      <td>0.112711</td>\n", "      <td>0.042956</td>\n", "      <td>0.010235</td>\n", "      <td>0.090809</td>\n", "      <td>2.114024</td>\n", "      <td>0.005394</td>\n", "      <td>1.052633</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>(Organic Blueberries)</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.042956</td>\n", "      <td>0.112711</td>\n", "      <td>0.010235</td>\n", "      <td>0.238274</td>\n", "      <td>2.114024</td>\n", "      <td>0.005394</td>\n", "      <td>1.164840</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>0.090339</td>\n", "      <td>0.058325</td>\n", "      <td>0.010966</td>\n", "      <td>0.121389</td>\n", "      <td>2.081257</td>\n", "      <td>0.005697</td>\n", "      <td>1.071777</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.058325</td>\n", "      <td>0.090339</td>\n", "      <td>0.010966</td>\n", "      <td>0.188018</td>\n", "      <td>2.081257</td>\n", "      <td>0.005697</td>\n", "      <td>1.120298</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>(Banana)</td>\n", "      <td>(Organic Fuji Apple)</td>\n", "      <td>0.200938</td>\n", "      <td>0.037992</td>\n", "      <td>0.014378</td>\n", "      <td>0.071552</td>\n", "      <td>1.883367</td>\n", "      <td>0.006744</td>\n", "      <td>1.036147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>(Organic Fuji Apple)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.037992</td>\n", "      <td>0.200938</td>\n", "      <td>0.014378</td>\n", "      <td>0.378441</td>\n", "      <td>1.883367</td>\n", "      <td>0.006744</td>\n", "      <td>1.285576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>0.161527</td>\n", "      <td>0.058325</td>\n", "      <td>0.017294</td>\n", "      <td>0.107065</td>\n", "      <td>1.835662</td>\n", "      <td>0.007873</td>\n", "      <td>1.054584</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>(Organic Raspberries)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.058325</td>\n", "      <td>0.161527</td>\n", "      <td>0.017294</td>\n", "      <td>0.296508</td>\n", "      <td>1.835662</td>\n", "      <td>0.007873</td>\n", "      <td>1.191874</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.161527</td>\n", "      <td>0.090339</td>\n", "      <td>0.026487</td>\n", "      <td>0.163981</td>\n", "      <td>1.815175</td>\n", "      <td>0.011895</td>\n", "      <td>1.088087</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.090339</td>\n", "      <td>0.161527</td>\n", "      <td>0.026487</td>\n", "      <td>0.293199</td>\n", "      <td>1.815175</td>\n", "      <td>0.011895</td>\n", "      <td>1.186294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>(Honeycrisp Apple)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.034078</td>\n", "      <td>0.200938</td>\n", "      <td>0.012122</td>\n", "      <td>0.355725</td>\n", "      <td>1.770317</td>\n", "      <td>0.005275</td>\n", "      <td>1.240249</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>(Banana)</td>\n", "      <td>(Honeycrisp Apple)</td>\n", "      <td>0.200938</td>\n", "      <td>0.034078</td>\n", "      <td>0.012122</td>\n", "      <td>0.060329</td>\n", "      <td>1.770317</td>\n", "      <td>0.005275</td>\n", "      <td>1.027936</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>(Organic Avocado)</td>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>0.075348</td>\n", "      <td>0.102948</td>\n", "      <td>0.013207</td>\n", "      <td>0.175281</td>\n", "      <td>1.702625</td>\n", "      <td>0.005450</td>\n", "      <td>1.087707</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>(Organic Avocado)</td>\n", "      <td>0.102948</td>\n", "      <td>0.075348</td>\n", "      <td>0.013207</td>\n", "      <td>0.128289</td>\n", "      <td>1.702625</td>\n", "      <td>0.005450</td>\n", "      <td>1.060733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.112711</td>\n", "      <td>0.090339</td>\n", "      <td>0.017314</td>\n", "      <td>0.153616</td>\n", "      <td>1.700440</td>\n", "      <td>0.007132</td>\n", "      <td>1.074762</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.090339</td>\n", "      <td>0.112711</td>\n", "      <td>0.017314</td>\n", "      <td>0.191659</td>\n", "      <td>1.700440</td>\n", "      <td>0.007132</td>\n", "      <td>1.097666</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>(<PERSON><PERSON><PERSON><PERSON>)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.040789</td>\n", "      <td>0.200938</td>\n", "      <td>0.013432</td>\n", "      <td>0.329296</td>\n", "      <td>1.638788</td>\n", "      <td>0.005236</td>\n", "      <td>1.191377</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>(Banana)</td>\n", "      <td>(<PERSON><PERSON><PERSON><PERSON>)</td>\n", "      <td>0.200938</td>\n", "      <td>0.040789</td>\n", "      <td>0.013432</td>\n", "      <td>0.066844</td>\n", "      <td>1.638788</td>\n", "      <td>0.005236</td>\n", "      <td>1.027922</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>0.090339</td>\n", "      <td>0.102948</td>\n", "      <td>0.014787</td>\n", "      <td>0.163679</td>\n", "      <td>1.589929</td>\n", "      <td>0.005486</td>\n", "      <td>1.072618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>(Organic Hass Avocado)</td>\n", "      <td>0.102948</td>\n", "      <td>0.090339</td>\n", "      <td>0.014787</td>\n", "      <td>0.143632</td>\n", "      <td>1.589929</td>\n", "      <td>0.005486</td>\n", "      <td>1.062232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>(Organic Whole Milk)</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.058411</td>\n", "      <td>0.112711</td>\n", "      <td>0.010130</td>\n", "      <td>0.173423</td>\n", "      <td>1.538645</td>\n", "      <td>0.003546</td>\n", "      <td>1.073449</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(Organic Whole Milk)</td>\n", "      <td>0.112711</td>\n", "      <td>0.058411</td>\n", "      <td>0.010130</td>\n", "      <td>0.089873</td>\n", "      <td>1.538645</td>\n", "      <td>0.003546</td>\n", "      <td>1.034569</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>(Organic Avocado)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.075348</td>\n", "      <td>0.200938</td>\n", "      <td>0.022745</td>\n", "      <td>0.301866</td>\n", "      <td>1.502282</td>\n", "      <td>0.007605</td>\n", "      <td>1.144568</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>(Banana)</td>\n", "      <td>(Organic Avocado)</td>\n", "      <td>0.200938</td>\n", "      <td>0.075348</td>\n", "      <td>0.022745</td>\n", "      <td>0.113194</td>\n", "      <td>1.502282</td>\n", "      <td>0.007605</td>\n", "      <td>1.042677</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>(Seedless Red Grapes)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.035480</td>\n", "      <td>0.200938</td>\n", "      <td>0.010534</td>\n", "      <td>0.296906</td>\n", "      <td>1.477596</td>\n", "      <td>0.003405</td>\n", "      <td>1.136493</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>(Banana)</td>\n", "      <td>(Seedless Red Grapes)</td>\n", "      <td>0.200938</td>\n", "      <td>0.035480</td>\n", "      <td>0.010534</td>\n", "      <td>0.052425</td>\n", "      <td>1.477596</td>\n", "      <td>0.003405</td>\n", "      <td>1.017883</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.161527</td>\n", "      <td>0.112711</td>\n", "      <td>0.026463</td>\n", "      <td>0.163832</td>\n", "      <td>1.453551</td>\n", "      <td>0.008257</td>\n", "      <td>1.061136</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.112711</td>\n", "      <td>0.161527</td>\n", "      <td>0.026463</td>\n", "      <td>0.234787</td>\n", "      <td>1.453551</td>\n", "      <td>0.008257</td>\n", "      <td>1.095739</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>(Strawberries)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.061123</td>\n", "      <td>0.200938</td>\n", "      <td>0.017661</td>\n", "      <td>0.288936</td>\n", "      <td>1.437931</td>\n", "      <td>0.005379</td>\n", "      <td>1.123754</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>(Banana)</td>\n", "      <td>(Strawberries)</td>\n", "      <td>0.200938</td>\n", "      <td>0.061123</td>\n", "      <td>0.017661</td>\n", "      <td>0.087891</td>\n", "      <td>1.437931</td>\n", "      <td>0.005379</td>\n", "      <td>1.029347</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>0.112711</td>\n", "      <td>0.102948</td>\n", "      <td>0.016267</td>\n", "      <td>0.144326</td>\n", "      <td>1.401939</td>\n", "      <td>0.004664</td>\n", "      <td>1.048358</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.102948</td>\n", "      <td>0.112711</td>\n", "      <td>0.016267</td>\n", "      <td>0.158014</td>\n", "      <td>1.401939</td>\n", "      <td>0.004664</td>\n", "      <td>1.053805</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>(Organic Yellow Onion)</td>\n", "      <td>0.161527</td>\n", "      <td>0.048146</td>\n", "      <td>0.010460</td>\n", "      <td>0.064756</td>\n", "      <td>1.344989</td>\n", "      <td>0.002683</td>\n", "      <td>1.017760</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>(Organic Yellow Onion)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.048146</td>\n", "      <td>0.161527</td>\n", "      <td>0.010460</td>\n", "      <td>0.217252</td>\n", "      <td>1.344989</td>\n", "      <td>0.002683</td>\n", "      <td>1.071191</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>(<PERSON> Lemon)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.065764</td>\n", "      <td>0.200938</td>\n", "      <td>0.017603</td>\n", "      <td>0.267663</td>\n", "      <td>1.332062</td>\n", "      <td>0.004388</td>\n", "      <td>1.091111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>(Banana)</td>\n", "      <td>(<PERSON> Lemon)</td>\n", "      <td>0.200938</td>\n", "      <td>0.065764</td>\n", "      <td>0.017603</td>\n", "      <td>0.087602</td>\n", "      <td>1.332062</td>\n", "      <td>0.004388</td>\n", "      <td>1.023934</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.102948</td>\n", "      <td>0.161527</td>\n", "      <td>0.021517</td>\n", "      <td>0.209007</td>\n", "      <td>1.293944</td>\n", "      <td>0.004888</td>\n", "      <td>1.060026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>0.161527</td>\n", "      <td>0.102948</td>\n", "      <td>0.021517</td>\n", "      <td>0.133208</td>\n", "      <td>1.293944</td>\n", "      <td>0.004888</td>\n", "      <td>1.034911</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>(Organic Avocado)</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.075348</td>\n", "      <td>0.112711</td>\n", "      <td>0.010254</td>\n", "      <td>0.136095</td>\n", "      <td>1.207468</td>\n", "      <td>0.001762</td>\n", "      <td>1.027068</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(Organic Avocado)</td>\n", "      <td>0.112711</td>\n", "      <td>0.075348</td>\n", "      <td>0.010254</td>\n", "      <td>0.090980</td>\n", "      <td>1.207468</td>\n", "      <td>0.001762</td>\n", "      <td>1.017197</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>(Organic Whole Milk)</td>\n", "      <td>0.161527</td>\n", "      <td>0.058411</td>\n", "      <td>0.011288</td>\n", "      <td>0.069883</td>\n", "      <td>1.196413</td>\n", "      <td>0.001853</td>\n", "      <td>1.012335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>(Organic Whole Milk)</td>\n", "      <td>(Bag of Organic Bananas)</td>\n", "      <td>0.058411</td>\n", "      <td>0.161527</td>\n", "      <td>0.011288</td>\n", "      <td>0.193253</td>\n", "      <td>1.196413</td>\n", "      <td>0.001853</td>\n", "      <td>1.039326</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>(Organic Whole Milk)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.058411</td>\n", "      <td>0.200938</td>\n", "      <td>0.013368</td>\n", "      <td>0.228866</td>\n", "      <td>1.138984</td>\n", "      <td>0.001631</td>\n", "      <td>1.036216</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>(Banana)</td>\n", "      <td>(Organic Whole Milk)</td>\n", "      <td>0.200938</td>\n", "      <td>0.058411</td>\n", "      <td>0.013368</td>\n", "      <td>0.066529</td>\n", "      <td>1.138984</td>\n", "      <td>0.001631</td>\n", "      <td>1.008697</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>(Banana)</td>\n", "      <td>(<PERSON><PERSON>)</td>\n", "      <td>0.200938</td>\n", "      <td>0.059984</td>\n", "      <td>0.013539</td>\n", "      <td>0.067380</td>\n", "      <td>1.123292</td>\n", "      <td>0.001486</td>\n", "      <td>1.007930</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>(<PERSON><PERSON>)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.059984</td>\n", "      <td>0.200938</td>\n", "      <td>0.013539</td>\n", "      <td>0.225713</td>\n", "      <td>1.123292</td>\n", "      <td>0.001486</td>\n", "      <td>1.031996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>(Banana)</td>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>0.200938</td>\n", "      <td>0.102948</td>\n", "      <td>0.021839</td>\n", "      <td>0.108683</td>\n", "      <td>1.055712</td>\n", "      <td>0.001152</td>\n", "      <td>1.006435</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>(<PERSON> Baby Spinach)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.102948</td>\n", "      <td>0.200938</td>\n", "      <td>0.021839</td>\n", "      <td>0.212133</td>\n", "      <td>1.055712</td>\n", "      <td>0.001152</td>\n", "      <td>1.014209</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>(Banana)</td>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>0.200938</td>\n", "      <td>0.112711</td>\n", "      <td>0.023857</td>\n", "      <td>0.118728</td>\n", "      <td>1.053382</td>\n", "      <td>0.001209</td>\n", "      <td>1.006827</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>(Organic Strawberries)</td>\n", "      <td>(Banana)</td>\n", "      <td>0.112711</td>\n", "      <td>0.200938</td>\n", "      <td>0.023857</td>\n", "      <td>0.211665</td>\n", "      <td>1.053382</td>\n", "      <td>0.001209</td>\n", "      <td>1.013607</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 antecedents               consequents  antecedent support  \\\n", "35                   (<PERSON><PERSON>)             (Large Lemon)            0.059984   \n", "34             (<PERSON>)                   (Lim<PERSON>)            0.065764   \n", "52    (Organic Strawberries)     (Organic Raspberries)            0.112711   \n", "53     (Organic Raspberries)    (Organic Strawberries)            0.058325   \n", "37         (Organic Avocado)             (Large Lemon)            0.075348   \n", "36             (Large Lemon)         (Organic Avocado)            0.065764   \n", "47    (Organic Strawberries)     (Organic Blueberries)            0.112711   \n", "46     (Organic Blueberries)    (Organic Strawberries)            0.042956   \n", "49    (Organic Hass Avocado)     (Organic Raspberries)            0.090339   \n", "48     (Organic Raspberries)    (Organic Hass Avocado)            0.058325   \n", "24                  (<PERSON><PERSON>)      (Organic Fuji Apple)            0.200938   \n", "25      (Organic Fuji Apple)                  (Banana)            0.037992   \n", "5   (Bag of Organic Bananas)     (Organic Raspberries)            0.161527   \n", "4      (Organic Raspberries)  (Bag of Organic Bananas)            0.058325   \n", "3   (Bag of Organic Bananas)    (Organic Hass Avocado)            0.161527   \n", "2     (Organic Hass Avocado)  (Bag of Organic Bananas)            0.090339   \n", "14        (<PERSON><PERSON><PERSON><PERSON>)                  (Banana)            0.034078   \n", "15                  (<PERSON><PERSON>)        (Honeycrisp Apple)            0.200938   \n", "39         (Organic Avocado)    (Organic Baby Spinach)            0.075348   \n", "38    (Organic Baby Spinach)         (Organic Avocado)            0.102948   \n", "50    (Organic Strawberries)    (Organic Hass Avocado)            0.112711   \n", "51    (Organic Hass Avocado)    (Organic Strawberries)            0.090339   \n", "12          (<PERSON><PERSON><PERSON><PERSON>)                  (Banana)            0.040789   \n", "13                  (<PERSON><PERSON>)          (<PERSON><PERSON><PERSON><PERSON>)            0.200938   \n", "43    (Organic Hass Avocado)    (Organic Baby Spinach)            0.090339   \n", "42    (Organic Baby Spinach)    (Organic Hass Avocado)            0.102948   \n", "55      (Organic Whole Milk)    (Organic Strawberries)            0.058411   \n", "54    (Organic Strawberries)      (Organic Whole Milk)            0.112711   \n", "20         (Organic Avocado)                  (Banana)            0.075348   \n", "21                  (<PERSON><PERSON>)         (Organic Avocado)            0.200938   \n", "30     (<PERSON><PERSON><PERSON> Red Grapes)                  (Banana)            0.035480   \n", "31                  (<PERSON><PERSON>)     (<PERSON>dless Red Grapes)            0.200938   \n", "7   (Bag of Organic Bananas)    (Organic Strawberries)            0.161527   \n", "6     (Organic Strawberries)  (Bag of Organic Bananas)            0.112711   \n", "32            (Straw<PERSON>)                  (Banana)            0.061123   \n", "33                  (<PERSON><PERSON>)            (Strawberries)            0.200938   \n", "45    (Organic Strawberries)    (Organic Baby Spinach)            0.112711   \n", "44    (Organic Baby Spinach)    (Organic Strawberries)            0.102948   \n", "11  (Bag of Organic Bananas)    (Organic Yellow Onion)            0.161527   \n", "10    (Organic Yellow Onion)  (Bag of Organic Bananas)            0.048146   \n", "16             (<PERSON> Lemon)                  (Banana)            0.065764   \n", "17                  (<PERSON><PERSON>)             (Large Lemon)            0.200938   \n", "0     (Organic Baby Spinach)  (Bag of Organic Bananas)            0.102948   \n", "1   (Bag of Organic Bananas)    (Organic Baby Spinach)            0.161527   \n", "41         (Organic Avocado)    (Organic Strawberries)            0.075348   \n", "40    (Organic Strawberries)         (Organic Avocado)            0.112711   \n", "9   (Bag of Organic Bananas)      (Organic Whole Milk)            0.161527   \n", "8       (Organic Whole Milk)  (Bag of Organic Bananas)            0.058411   \n", "29      (Organic Whole Milk)                  (Banana)            0.058411   \n", "28                  (<PERSON><PERSON>)      (Organic Whole Milk)            0.200938   \n", "19                  (<PERSON><PERSON>)                   (<PERSON><PERSON>)            0.200938   \n", "18                   (<PERSON><PERSON>)                  (Banana)            0.059984   \n", "23                  (<PERSON><PERSON>)    (Organic Baby Spinach)            0.200938   \n", "22    (<PERSON>)                  (Banana)            0.102948   \n", "27                  (<PERSON><PERSON>)    (Organic Strawberries)            0.200938   \n", "26    (Organic Strawberries)                  (Banana)            0.112711   \n", "\n", "    consequent support   support  confidence      lift  leverage  conviction  \n", "35            0.065764  0.011860    0.197723  3.006544  0.007915    1.164480  \n", "34            0.059984  0.011860    0.180345  3.006544  0.007915    1.146843  \n", "52            0.058325  0.014533    0.128940  2.210731  0.007959    1.081069  \n", "53            0.112711  0.014533    0.249174  2.210731  0.007959    1.181751  \n", "37            0.065764  0.010538    0.139862  2.126728  0.005583    1.086147  \n", "36            0.075348  0.010538    0.160244  2.126728  0.005583    1.101097  \n", "47            0.042956  0.010235    0.090809  2.114024  0.005394    1.052633  \n", "46            0.112711  0.010235    0.238274  2.114024  0.005394    1.164840  \n", "49            0.058325  0.010966    0.121389  2.081257  0.005697    1.071777  \n", "48            0.090339  0.010966    0.188018  2.081257  0.005697    1.120298  \n", "24            0.037992  0.014378    0.071552  1.883367  0.006744    1.036147  \n", "25            0.200938  0.014378    0.378441  1.883367  0.006744    1.285576  \n", "5             0.058325  0.017294    0.107065  1.835662  0.007873    1.054584  \n", "4             0.161527  0.017294    0.296508  1.835662  0.007873    1.191874  \n", "3             0.090339  0.026487    0.163981  1.815175  0.011895    1.088087  \n", "2             0.161527  0.026487    0.293199  1.815175  0.011895    1.186294  \n", "14            0.200938  0.012122    0.355725  1.770317  0.005275    1.240249  \n", "15            0.034078  0.012122    0.060329  1.770317  0.005275    1.027936  \n", "39            0.102948  0.013207    0.175281  1.702625  0.005450    1.087707  \n", "38            0.075348  0.013207    0.128289  1.702625  0.005450    1.060733  \n", "50            0.090339  0.017314    0.153616  1.700440  0.007132    1.074762  \n", "51            0.112711  0.017314    0.191659  1.700440  0.007132    1.097666  \n", "12            0.200938  0.013432    0.329296  1.638788  0.005236    1.191377  \n", "13            0.040789  0.013432    0.066844  1.638788  0.005236    1.027922  \n", "43            0.102948  0.014787    0.163679  1.589929  0.005486    1.072618  \n", "42            0.090339  0.014787    0.143632  1.589929  0.005486    1.062232  \n", "55            0.112711  0.010130    0.173423  1.538645  0.003546    1.073449  \n", "54            0.058411  0.010130    0.089873  1.538645  0.003546    1.034569  \n", "20            0.200938  0.022745    0.301866  1.502282  0.007605    1.144568  \n", "21            0.075348  0.022745    0.113194  1.502282  0.007605    1.042677  \n", "30            0.200938  0.010534    0.296906  1.477596  0.003405    1.136493  \n", "31            0.035480  0.010534    0.052425  1.477596  0.003405    1.017883  \n", "7             0.112711  0.026463    0.163832  1.453551  0.008257    1.061136  \n", "6             0.161527  0.026463    0.234787  1.453551  0.008257    1.095739  \n", "32            0.200938  0.017661    0.288936  1.437931  0.005379    1.123754  \n", "33            0.061123  0.017661    0.087891  1.437931  0.005379    1.029347  \n", "45            0.102948  0.016267    0.144326  1.401939  0.004664    1.048358  \n", "44            0.112711  0.016267    0.158014  1.401939  0.004664    1.053805  \n", "11            0.048146  0.010460    0.064756  1.344989  0.002683    1.017760  \n", "10            0.161527  0.010460    0.217252  1.344989  0.002683    1.071191  \n", "16            0.200938  0.017603    0.267663  1.332062  0.004388    1.091111  \n", "17            0.065764  0.017603    0.087602  1.332062  0.004388    1.023934  \n", "0             0.161527  0.021517    0.209007  1.293944  0.004888    1.060026  \n", "1             0.102948  0.021517    0.133208  1.293944  0.004888    1.034911  \n", "41            0.112711  0.010254    0.136095  1.207468  0.001762    1.027068  \n", "40            0.075348  0.010254    0.090980  1.207468  0.001762    1.017197  \n", "9             0.058411  0.011288    0.069883  1.196413  0.001853    1.012335  \n", "8             0.161527  0.011288    0.193253  1.196413  0.001853    1.039326  \n", "29            0.200938  0.013368    0.228866  1.138984  0.001631    1.036216  \n", "28            0.058411  0.013368    0.066529  1.138984  0.001631    1.008697  \n", "19            0.059984  0.013539    0.067380  1.123292  0.001486    1.007930  \n", "18            0.200938  0.013539    0.225713  1.123292  0.001486    1.031996  \n", "23            0.102948  0.021839    0.108683  1.055712  0.001152    1.006435  \n", "22            0.200938  0.021839    0.212133  1.055712  0.001152    1.014209  \n", "27            0.112711  0.023857    0.118728  1.053382  0.001209    1.006827  \n", "26            0.200938  0.023857    0.211665  1.053382  0.001209    1.013607  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["rules = association_rules(frequent_items, metric=\"lift\", min_threshold=1)\n", "rules.sort_values('lift', ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}