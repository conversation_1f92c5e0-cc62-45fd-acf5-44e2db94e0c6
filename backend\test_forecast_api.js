const mongoose = require('mongoose');
const XGBoostForecastingService = require('./services/xgboostForecastingService');

async function testForecastAPI() {
  try {
    console.log('🚀 Testing XGBoost Forecasting API...');
    
    // Connect to database
    require('dotenv').config();
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/inventory_management';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
    
    // Initialize XGBoost forecasting service
    const forecastingService = new XGBoostForecastingService();
    
    console.log('📊 Preparing XGBoost features...');

    // First prepare the feature data
    const featureData = await forecastingService.prepareXGBoostFeatures();
    console.log(`✅ Prepared ${featureData.length} feature records`);

    if (featureData.length === 0) {
      console.log('⚠️ No feature data available - cannot generate forecasts');
      return;
    }

    console.log('🔮 Testing forecast generation...');

    // Test with a small subset of products (first 5)
    const limitedProductIds = featureData.slice(0, 5).map(f => f.product_id);
    const forecasts = await forecastingService.generateDemandForecasts(limitedProductIds, {
      forecastDays: 7
    });
    
    console.log(`✅ Generated ${forecasts.length} XGBoost forecasts successfully!`);
    
    if (forecasts.length > 0) {
      console.log('📈 Sample forecast results:');
      forecasts.slice(0, 3).forEach((forecast, index) => {
        console.log(`\n${index + 1}. Product: ${forecast.product_name}`);
        console.log(`   Model: ${forecast.model_type}`);
        console.log(`   Accuracy: ${forecast.accuracy}%`);
        console.log(`   Forecast Points: ${forecast.forecast_data.length}`);
        console.log(`   Confidence: ${forecast.confidence_interval ? 'Available' : 'Not available'}`);
        
        if (forecast.forecast_data.length > 0) {
          const firstPoint = forecast.forecast_data[0];
          console.log(`   First Forecast: ${firstPoint.predicted_demand} units on ${firstPoint.date}`);
        }
      });
      
      console.log('\n🎯 XGBoost Forecasting is working correctly!');
      console.log('✅ The "Generate Comprehensive Analysis" button should now work in the UI');
    } else {
      console.log('⚠️ No forecasts generated - check if there is sufficient data');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testForecastAPI();
