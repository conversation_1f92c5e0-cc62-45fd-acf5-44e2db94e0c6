#!/usr/bin/env python3
"""
Instacart XGBoost Reorder Prediction Model
Based on the predictive-analysis-model notebook approach
"""

import pandas as pd
import numpy as np
import json
import argparse
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# XGBoost and ML libraries
import xgboost as xgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, precision_score, recall_score
import joblib

# Custom JSON encoder to handle numpy types
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

class InstacartXGBoostPredictor:
    def __init__(self, output_path='./models_output'):
        self.output_path = output_path
        self.model = None
        self.feature_columns = []
        
        # Ensure output directory exists
        os.makedirs(output_path, exist_ok=True)
    
    def load_and_prepare_data(self, data_path):
        """Load and prepare feature data from CSV"""
        print(f"Loading data from: {data_path}")
        
        # Load the feature data
        df = pd.read_csv(data_path)
        print(f"Loaded {len(df)} records")
        
        # Prepare features based on the notebook approach
        feature_cols = [
            'total_product_orders_by_user', 'total_product_reorders_by_user',
            'avg_add_to_cart_order', 'last_order_dow', 'last_order_hour',
            'order_count', 'reorder_count', 'user_product_reorder_percentage',
            'avg_days_since_last_bought', 'aisle_id', 'department_id'
        ]
        
        # Filter to only include available columns
        available_cols = [col for col in feature_cols if col in df.columns]
        self.feature_columns = available_cols
        
        print(f"Using {len(available_cols)} features: {available_cols}")
        
        # Prepare feature matrix
        X = df[available_cols].fillna(0)
        
        # Create target variable (reorder prediction)
        # Use reorder_count > 0 as target (whether user will reorder this product)
        y = (df['reorder_count'] > 0).astype(int)
        
        print(f"Data preparation completed. Shape: {X.shape}")
        print(f"Target distribution: {y.value_counts().to_dict()}")
        
        return X, y, df
    
    def train_model(self, X, y):
        """Train XGBoost model for reorder prediction"""
        print("Training XGBoost model...")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # XGBoost parameters optimized for reorder prediction
        params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'max_depth': 6,
            'learning_rate': 0.1,
            'n_estimators': 100,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42
        }
        
        # Train model
        self.model = xgb.XGBClassifier(**params)
        self.model.fit(X_train, y_train)
        
        # Evaluate model
        y_pred = self.model.predict(X_test)
        y_pred_proba = self.model.predict_proba(X_test)[:, 1]
        
        performance = {
            'accuracy': float(accuracy_score(y_test, y_pred)),
            'f1_score': float(f1_score(y_test, y_pred)),
            'auc_score': float(roc_auc_score(y_test, y_pred_proba)),
            'precision': float(precision_score(y_test, y_pred)),
            'recall': float(recall_score(y_test, y_pred))
        }
        
        print("Model Performance:")
        for metric, value in performance.items():
            print(f"  {metric}: {value:.4f}")
        
        return performance
    
    def generate_forecasts(self, df, forecast_days=30):
        """Generate reorder forecasts for products"""
        print(f"Generating forecasts for {forecast_days} days...")
        
        if self.model is None:
            raise ValueError("Model not trained yet!")
        
        # Get unique products
        unique_products = df[['product_id', 'product_name']].drop_duplicates()
        
        forecasts = []
        
        for _, product in unique_products.iterrows():
            try:
                # Get product data
                product_data = df[df['product_id'] == product['product_id']]
                
                if len(product_data) == 0:
                    continue
                
                # Use the first row for prediction (or aggregate if multiple users)
                features = product_data[self.feature_columns].iloc[0:1]
                
                # Predict reorder probability
                reorder_prob = float(self.model.predict_proba(features)[0, 1])
                
                # Generate forecast data points
                forecast_data = []
                base_demand = max(1, int(product_data['total_product_orders_by_user'].mean()))
                
                for day in range(1, forecast_days + 1):
                    forecast_date = datetime.now() + timedelta(days=day)
                    
                    # Calculate predicted demand based on reorder probability
                    daily_demand = max(1, int(base_demand * reorder_prob * (0.8 + np.random.random() * 0.4)))
                    
                    forecast_data.append({
                        'date': forecast_date.isoformat(),
                        'predicted_demand': daily_demand,
                        'reorder_probability': reorder_prob,
                        'confidence_score': min(0.95, reorder_prob + 0.1)
                    })
                
                forecast = {
                    'product_id': int(product['product_id']),
                    'product_name': str(product['product_name']),
                    'model_type': 'xgboost_reorder',
                    'reorder_probability': reorder_prob,
                    'forecast_data': forecast_data,
                    'feature_importance': {}
                }
                
                forecasts.append(forecast)
                
            except Exception as e:
                print(f"Error forecasting for product {product['product_id']}: {e}")
                continue
        
        print(f"Generated forecasts for {len(forecasts)} products")
        return forecasts
    
    def save_forecasts(self, forecasts, performance):
        """Save forecasts and model to files"""
        # Save individual forecast files
        for i, forecast in enumerate(forecasts):
            filename = f"forecast_product_{forecast['product_id']}.json"
            filepath = os.path.join(self.output_path, filename)
            
            # Add performance metrics to each forecast
            forecast_with_performance = forecast.copy()
            forecast_with_performance.update(performance)
            
            with open(filepath, 'w') as f:
                json.dump(forecast_with_performance, f, indent=2, cls=NumpyEncoder)
        
        # Save summary
        summary = {
            'total_forecasts': len(forecasts),
            'model_performance': performance,
            'model_type': 'xgboost_reorder',
            'timestamp': datetime.now().isoformat(),
            'feature_columns': self.feature_columns
        }
        
        summary_path = os.path.join(self.output_path, 'xgboost_forecast_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, cls=NumpyEncoder)
        
        print(f"Forecasts saved to: {self.output_path}")
        
        # Save model
        model_path = os.path.join(self.output_path, 'xgboost_reorder_model.pkl')
        joblib.dump(self.model, model_path)
        print(f"Model saved to: {model_path}")

def main():
    parser = argparse.ArgumentParser(description='Instacart XGBoost Reorder Prediction')
    parser.add_argument('--data_path', required=True, help='Path to feature data CSV')
    parser.add_argument('--forecast_days', type=int, default=30, help='Number of days to forecast')
    parser.add_argument('--output_path', default='./models_output', help='Output directory')
    parser.add_argument('--model_type', default='xgboost', help='Model type identifier')
    
    args = parser.parse_args()
    
    try:
        # Initialize predictor
        predictor = InstacartXGBoostPredictor(args.output_path)
        
        # Load and prepare data
        X, y, df = predictor.load_and_prepare_data(args.data_path)
        
        # Train model
        performance = predictor.train_model(X, y)
        
        # Generate forecasts
        forecasts = predictor.generate_forecasts(df, args.forecast_days)
        
        # Save results
        predictor.save_forecasts(forecasts, performance)
        
        print("XGBoost reorder prediction completed successfully!")
        
    except Exception as e:
        print(f"Error in XGBoost prediction: {e}")
        raise

if __name__ == "__main__":
    main()
