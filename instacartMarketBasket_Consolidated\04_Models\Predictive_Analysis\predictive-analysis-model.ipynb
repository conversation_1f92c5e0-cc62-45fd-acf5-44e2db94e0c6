import numpy as np # linear algebra
import pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)

# garbage collector to free up memory
import gc
gc.enable()

orders = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/orders.csv' )
order_products_train = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/order_products__train.csv')
order_products_prior = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/order_products__prior.csv')
products = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/products.csv')
aisles = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/aisles.csv')
departments = pd.read_csv('/kaggle/input/d/datasets/psparks/instacart-market-basket-analysis/departments.csv')

def reduce_mem_usage(train_data):
    
#  iterate through all the columns of a dataframe and modify the data type to reduce memory usage."""
    start_mem = train_data.memory_usage().sum() / 1024**2
    print('Memory usage of dataframe is {:.2f} MB'.format(start_mem))

    for col in train_data.columns:
        col_type = train_data[col].dtype

        if col_type != object:
            c_min = train_data[col].min()
            c_max = train_data[col].max()
            if str(col_type)[:3] == 'int':
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    train_data[col] = train_data[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    train_data[col] = train_data[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    train_data[col] = train_data[col].astype(np.int32)
                elif c_min > np.iinfo(np.int64).min and c_max < np.iinfo(np.int64).max:
                    train_data[col] = train_data[col].astype(np.int64)  
            else:
                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                    train_data[col] = train_data[col].astype(np.float16)
                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                    train_data[col] = train_data[col].astype(np.float32)
                else:
                    train_data[col] = train_data[col].astype(np.float64)
        else:
            train_data[col] = train_data[col].astype('category')
        end_mem = train_data.memory_usage().sum() / 1024**2
        print('Memory usage after optimization is: {:.2f} MB'.format(end_mem))
        print('Decreased by {:.1f}%'.format(100 * (start_mem - end_mem) / start_mem))

    return train_data

reduce_mem_usage(order_products_prior)
reduce_mem_usage(order_products_train)
reduce_mem_usage(products)
reduce_mem_usage(orders)
reduce_mem_usage(departments)
reduce_mem_usage(aisles)

# get shape of each df
print(f" aisles : {aisles.shape} \n depts : {departments.shape} \n order_prod_prior : {order_products_prior.shape} \n order_products_train : {order_products_train.shape} \n orders : {orders.shape} \n products : {products.shape}")

# Helper functions to be able to correclty calculate the avergae of hours
# The problem is that if we deal with hours and average them normally, the average of 1:00 and 23:00 will be 12 not 0:00
import datetime
import math

def datetime_to_radians(x):
    # radians are calculated using a 24-hour circle, not 12-hour, starting at north and moving clockwise
    seconds_from_midnight = 3600 * x
    radians = float(seconds_from_midnight) / float(12 * 60 * 60) * 2.0 * math.pi
    return radians

def average_angle(angles):
    # angles measured in radians
    x_sum = np.sum(np.sin(angles))
    y_sum = np.sum(np.cos(angles))
    x_mean = x_sum / float(len(angles))
    y_mean = y_sum / float(len(angles))
    return np.arctan2(x_mean, y_mean)

def radians_to_time_of_day(x):
    # radians are measured clockwise from north and represent time in a 24-hour circle
    seconds_from_midnight = int(float(x) / (2.0 * math.pi) * 12.0 * 60.0 * 60.0)
    hour = seconds_from_midnight // 3600 % 24
    minute = (seconds_from_midnight % 3600) // 60
    second = seconds_from_midnight % 60
    return datetime.time(hour, minute, second)
    
def average_times_of_day(x):
    # input datetime.datetime array and output datetime.time value
    angles = [datetime_to_radians(y) for y in x]
    avg_angle = average_angle(angles)
    return radians_to_time_of_day(avg_angle)

def day_to_radians(day):
    radians = float(day) / float(7) * 2.0 * math.pi
    return radians
def radians_to_days(x):
    day = int(float(x) / (2.0 * math.pi) * 7) % 7
    return day
def average_days(x):
    angles = [day_to_radians(y) for y in x]
    avg_angle = average_angle(angles)
    return radians_to_days(avg_angle)

# We keep only the prior orders
users = orders[orders['eval_set'] == 'prior']
users['days_since_prior_order'].dropna()

# We group orders by user_id & calculate the variables based on different user_id
users = users.groupby('user_id').agg(
    
 user_orders= ('order_number' , max),
 user_period=('days_since_prior_order', sum),
 user_mean_days_since_prior = ('days_since_prior_order','mean')
    
)
users.head()

# We create a new table "orders_products" which contains the tables "orders" and "order_products_prior"
orders_products =pd.merge(orders , order_products_prior, on='order_id', how='inner')

# Getting the number of products in each basket(order)
groupedorders_products = orders_products.groupby(['order_id']).agg(
    basket_size = ('product_id', 'count')
).reset_index()
orders_products = orders_products.merge(groupedorders_products, on='order_id', how='left')
orders_products.head()

orders_products['p_reordered']= orders_products['reordered']==1
orders_products['non_first_order']= orders_products['order_number']>1

us=orders_products

# We group orders_products by user_id & calculate the variables based on different user_id
us=orders_products.groupby('user_id').agg(
    
     user_total_products =('user_id','count') ,
     p_reordered =('p_reordered', sum) ,
     non_first_order =('non_first_order', sum),
     user_distinct_products=('product_id','nunique')

).reset_index()
#    us['user_reorder_ratio'] = sum(reordered == 1) / sum(order_number > 1)
us['user_reorder_ratio']=us['p_reordered']/us['non_first_order']

del us["p_reordered"],us["non_first_order"]
del orders_products['p_reordered' ],orders_products['non_first_order']

us.head(20)

users =pd.merge(users,us ,on='user_id',  how='inner')

# We calculate the user_average_basket variable
users['user_average_basket'] = users['user_total_products'] / users['user_orders']
users.head()

# we exclude prior orders and thus we keep only train and test orders
us = orders[orders['eval_set'] != 'prior']
us['time_since_last_order'] = us['days_since_prior_order']
us['future_order_dow'] = us['order_dow']
us['future_order_hour_of_day'] = us['order_hour_of_day']

us = us[['user_id','order_id','eval_set','time_since_last_order', 'future_order_dow', 'future_order_hour_of_day']]

# We combine users and us tables and store the results into the users table
users_features = pd.merge(users , us, on='user_id', how='inner') 

# We delete the us table
del us, users

users_features.head()

prod_features = orders_products.groupby(['product_id']).agg(
    prod_freq = ('order_id', 'count'),
    prod_avg_position = ('add_to_cart_order', 'mean')
#     prod_avg_hour = ('order_hour_of_day', average_times_of_day),
#     prod_avg_dow = ('order_dow', average_days)
).reset_index()

prod_features.head(20)

non_first_order = orders_products['order_number'] != 1

groupedorders_products = orders_products[non_first_order].groupby(['product_id']).agg(
    prod_reorder_ratio = ('reordered', 'mean')
).reset_index()

prod_features = prod_features.merge(groupedorders_products, on='product_id', how='left')

# Group by users who have bought it more than once
# get the count of orders each user bought having the product. 
groupedorders_products = orders_products[non_first_order].groupby(['product_id', 'user_id']).agg(
    user_prod_freq = ('order_id', 'count')
).reset_index()

# get the avg # of orders the user will buy having that product after buying it for the first time
groupedorders_products = groupedorders_products.groupby(['product_id']).agg(
    user_prod_avg_freq = ('user_prod_freq', 'mean')
).reset_index()

prod_features = prod_features.merge(groupedorders_products, on='product_id', how='left')
del groupedorders_products, non_first_order

prod_features.head()

# We create the data table starting from the orders_products table 
data = orders_products

data = data.groupby(['user_id','product_id']).agg(

 up_orders= ('product_id', 'count'),
 up_first_order=('order_number', min),
 up_last_order = ('order_number',max),
 up_average_cart_position = ('add_to_cart_order','mean')
#  up_avg_hour = ('order_hour_of_day', average_times_of_day),
#  up_avg_dow = ('order_dow', average_days)
).reset_index()
 
del orders_products
data.head(20)


# up_order_rate = up_orders / user_total_products
data = data.merge(users_features[['user_id','user_orders']], on='user_id' , how='left')
data['up_order_rate'] = data['up_orders']/data['user_orders']

# up_orders_since_last_order = user_last_order - user_last_ordered_that_product
data['up_orders_since_last_order'] = data['user_orders'] - data['up_last_order']

# From the moment the user know about the product, how frequent he then bought it in his next orders?
# up_order_rate_since_first_order = up_orders / (user_orders - up_first_order + 1)
# The + 1 is added since order_numbering starts from 1 not 0
data['up_order_rate_since_first_order'] = data['up_orders']/(data['user_orders'] - data['up_first_order'] + 1)
del data['user_orders']

data.head()

# Merging user and product features with the final features dataframe
data = data.merge(users_features, on='user_id', how='left').merge(prod_features, on='product_id', how='left')
del users_features, prod_features

order_products_future = order_products_train.merge(orders, on='order_id', how='left')
order_products_future = order_products_future[['user_id', 'product_id', 'reordered']]
data = data.merge(order_products_future, on=['user_id', 'product_id'], how='left')

# Set 0 to Product who didn't exists in the future order so model can predict them as Not in future order.
data['reordered'].fillna(0, inplace = True)

'''
Calculates the difference between 2 values from a looping sequence
dist(X, Y) = min { X-Y, N-(X-Y) }
'''
def diff_bet_time(arr1, arr2, max_value=23):
    arr1 = pd.to_datetime(arr1, format='%H')
    arr2 = pd.to_datetime(arr2, format='%H:%M:%S')
    arr_diff = np.abs(arr1.dt.hour-arr2.dt.hour)
    return np.minimum(arr_diff, max_value- (arr_diff-1))

'''
Calculates the difference between 2 values from a looping sequence
dist(X, Y) = min { X-Y, N-(X-Y) }
'''
def diff_bet_dow(arr1, arr2, max_value=6):
    arr_diff = np.abs(arr1-arr2)
    return np.minimum(arr_diff, max_value- (arr_diff-1))

# data['up_hour_diff'] = diff_bet_time(data['future_order_hour_of_day'], data['up_avg_hour'])
# data['up_dow_diff'] = diff_bet_dow(data['future_order_dow'], data['up_avg_dow'])

# data['prod_hour_diff'] = diff_bet_time(data['future_order_hour_of_day'], data['prod_avg_hour'], )
# data['prod_dow_diff'] = diff_bet_dow(data['prod_avg_dow'], data['future_order_dow'])

# del data['prod_avg_dow'], data['prod_avg_hour'], data['future_order_hour_of_day'], data['up_avg_hour'], data['future_order_dow'], data['up_avg_dow']
del data['future_order_hour_of_day'], data['future_order_dow']

# # Saving features in a csv file
# data.to_csv('./features.csv', index=False)

# To saveup memory, delete any dataframe we won't use next
del order_products_prior, order_products_train, products, orders, departments, aisles

# Splitting data to train and test
X_train = data[data['eval_set'] == 'train']
y_train = X_train['reordered']
X_test = data[data['eval_set'] == 'test']
del data

from sklearn.model_selection import train_test_split

print('Class distribution before splitting')
pos_count = np.sum(X_train['reordered']==1)
neg_count = np.sum(X_train['reordered']==0)
print('positive ratio: ', pos_count)
print('negative count: ', neg_count)
print('positive ratio: ', pos_count/(pos_count+neg_count))

X_train, X_val, y_train, y_val = train_test_split(X_train, y_train, test_size=0.3)
print('Class distribution of Train set')
train_pos_count = np.sum(X_train['reordered']==1)
train_neg_count = np.sum(X_train['reordered']==0)
print('positive count: ', train_pos_count)
print('negative count: ', train_neg_count)
print('positive ratio: ', train_pos_count/(train_pos_count+train_neg_count))

print('Class distribution of Validation set')
val_pos_count = np.sum(X_val['reordered']==1)
val_neg_count = np.sum(X_val['reordered']==0)
print('positive count: ', val_pos_count)
print('negative count: ', val_neg_count)
print('positive ratio: ', val_pos_count/(val_pos_count+val_neg_count))

# Removing eval_set and the target variable from features
X_train_non_pred_vars = X_train[['product_id', 'order_id', 'user_id']]
X_train.drop(['reordered', 'eval_set', 'product_id', 'order_id', 'user_id'], axis=1, inplace=True)

X_val_non_pred_vars = X_val[['product_id', 'order_id', 'user_id']]
X_val.drop(['reordered', 'eval_set', 'product_id', 'order_id', 'user_id'], axis=1, inplace=True)

X_test_non_pred_vars = X_test[['product_id', 'order_id', 'user_id']]
X_test.drop(['reordered', 'eval_set', 'product_id', 'order_id', 'user_id'], axis=1, inplace=True)

# Drop features I suspect redundant or of no significant importance as the feature importance graph says
X_train.drop(['up_orders', 'up_last_order', 'user_total_products', 'user_distinct_products'], axis=1, inplace=True)
X_test.drop(['up_orders', 'up_last_order', 'user_total_products', 'user_distinct_products'], axis=1, inplace=True)
X_val.drop(['up_orders', 'up_last_order', 'user_total_products', 'user_distinct_products'], axis=1, inplace=True)

# Dropping the time dependent features 'up_dow_diff','prod_dow_diff','up_hour_diff','prod_hour_diff'
# We had a strong intuition that these time features will be significant in the prediction,
# However, they were the least feature's importance used by the model.
# This is why We commented them across the code.

print(X_train.shape, y_train.shape)
print(X_val.shape, y_val.shape)
print(X_test.shape)

X_train.columns

import xgboost as xgb
from sklearn import metrics

# Training the model with features except the product_id, user_id, order_id columns
clf = xgb.XGBClassifier(objective='binary:logistic', colsample_bytree = 0.4, learning_rate = 0.1,
                max_depth = 5, reg_lambda = 5.0, n_estimators = 100)
clf.fit(X_train,y_train)

import matplotlib.pyplot as plt
from sklearn.feature_selection import SelectFromModel
# Visualizing the Feature importance 
print(clf.feature_importances_)

xgb.plot_importance(clf)
plt.show()

# keep probabilities for the positive outcome only
y_test_prob = clf.predict_proba(X_test)[:, 1]
y_val_prob = clf.predict_proba(X_val)[:, 1]
y_train_prob = clf.predict_proba(X_train)[:, 1]

'''
This function maximizes a metric, while keeping another metric above a given threshold.
'''
def maximize_metric_keep_metric(metric1_list, metric2_list, metric2_thresh=0.3):
    for idx in range(len(metric1_list)):
        if(metric2_list[idx] > metric2_thresh):
            return idx
    return -1

from sklearn.metrics import precision_recall_curve

# Choosing Threshold that maximizes the f1_score
precision, recall, thresholds = precision_recall_curve(y_val, y_val_prob)
f1_scores = 2*recall*precision/(recall+precision)
opt_indx = np.argmax(f1_scores)
print("Maximuim f1_score for the positive class: ", f1_scores[opt_indx])
print("Correspoding precision: ", precision[opt_indx])
print("Correspoding recall: ", recall[opt_indx])
print("Correspoding Threshold: ", thresholds[opt_indx])
best_thresh = thresholds[opt_indx]

# Choosing Threshold that maximizes recall, while keeping precision above 0.3
opt_indx = maximize_metric_keep_metric(metric1_list=recall, metric2_list=precision, metric2_thresh=0.3)
print("Max recall for the positive class: ", recall[opt_indx])
print("Correspoding precision: ", precision[opt_indx])
print("Correspoding f1_score: ", f1_scores[opt_indx])
print("Correspoding Threshold: ", thresholds[opt_indx])
best_thresh = thresholds[opt_indx]

# plot the precision-recall curves
no_skill = len(y_val[y_val==1]) / len(y_val)
plt.plot([0, 1], [no_skill, no_skill], linestyle='--', label='No Skill')
plt.plot(recall, precision, marker='.', label='Logistic')
plt.plot(recall[opt_indx], precision[opt_indx], marker='o', color='k', label='optimum threshold')
# axis labels
plt.xlabel('Recall')
plt.ylabel('Precision')
plt.title('Precision-Recall Curve')
# show the legend
plt.legend()
# show the plot
plt.show()

# Changing probabilities to crisp predicted values, useing the threshold obtained from the ROC-curve
y_test_preds = y_test_prob>best_thresh
y_val_preds = y_val_prob>best_thresh
y_train_preds = y_train_prob>best_thresh

from sklearn.metrics import confusion_matrix, classification_report

print('-----------------CLASSIFICATION REPORT--------------------')
print("Train positive class count: ", y_train.sum())
print("Train negative class count: ", y_train.shape[0] - y_train.sum())
print("Train Set tn, fp, fn, tp:",confusion_matrix(y_train, y_train_preds).ravel())
print("Train Set report:",classification_report(y_train, y_train_preds))

print("Validation positive class count: ", y_val.sum())
print("Validation negative class count: ", y_val.shape[0] - y_val.sum())
print("Validation Set tn, fp, fn, tp:",confusion_matrix(y_val, y_val_preds).ravel())
print("Validation Set report:",classification_report(y_val, y_val_preds))

import csv

# Append prediction to test_order details
test_orders = X_test_non_pred_vars[['order_id','product_id']]
test_orders['reordered'] = y_test_preds

# Extracting orders who have no predicted products
empty_orders = test_orders.groupby(['order_id']).agg(
    count_reorders = ('reordered', 'sum')
).reset_index()
empty_orders = empty_orders[empty_orders['count_reorders'] == 0]

# For orders who have predicted products 
# Extract the products predicted to be in the future order
test_orders = test_orders[test_orders['reordered'] == 1]
# For each order group its predicted products together into a list 
test_orders = test_orders.groupby('order_id')['product_id'].apply(list).reset_index(name='products')


test_orders.head()

# csv header
headerNames = ['order_id', 'products']
rows = []

for index, row in test_orders.iterrows():
    products = ' '.join(str(product_id) for product_id in row['products']) 
    rows.append( 
        {'order_id': str(row['order_id']),
        'products': products})

for index, row in empty_orders.iterrows():
    rows.append( 
        {'order_id': str(row['order_id']),
        'products': 'None'})
    
with open('./submissions.csv', 'w', encoding='UTF-8', newline='') as f:
    writer = csv.DictWriter(f, fieldnames=headerNames)
    writer.writeheader()
    writer.writerows(rows)