/**
 * Comprehensive API Testing Script
 * Tests all major API endpoints and functionality
 */

const axios = require('axios');
const fs = require('fs');

const API_BASE = 'http://localhost:5000/api';
let authToken = null;

// Test configuration
const testConfig = {
  adminCredentials: {
    email: '<EMAIL>',
    password: 'admin123'
  }
};

// Helper function to make authenticated requests
const authRequest = (method, url, data = null) => {
  const config = {
    method,
    url: `${API_BASE}${url}`,
    headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
    timeout: 30000
  };
  if (data) config.data = data;
  return axios(config);
};

// Test login and authentication
async function testAuthentication() {
  console.log('\n🔐 TESTING AUTHENTICATION');
  console.log('='.repeat(50));
  
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, testConfig.adminCredentials);
    authToken = response.data.token;
    console.log('✅ Login successful');
    
    // Test profile endpoint
    const profileResponse = await authRequest('get', '/auth/me');
    console.log(`✅ Profile retrieved: ${profileResponse.data.name} (${profileResponse.data.role})`);
    
    return true;
  } catch (error) {
    console.error('❌ Authentication failed:', error.response?.data?.error || error.message);
    return false;
  }
}

// Test basic inventory APIs
async function testInventoryAPIs() {
  console.log('\n📦 TESTING INVENTORY APIs');
  console.log('='.repeat(50));
  
  try {
    // Test products endpoint
    const productsResponse = await authRequest('get', '/products?limit=5');
    const products = productsResponse.data.products || productsResponse.data;
    console.log(`✅ Products API: Found ${products.length} products`);
    
    // Test suppliers endpoint
    const suppliersResponse = await authRequest('get', '/suppliers?limit=5');
    const suppliers = suppliersResponse.data.suppliers || suppliersResponse.data;
    console.log(`✅ Suppliers API: Found ${suppliers.length} suppliers`);
    
    // Test orders endpoint
    const ordersResponse = await authRequest('get', '/orders?limit=5');
    const orders = ordersResponse.data.orders || ordersResponse.data;
    console.log(`✅ Orders API: Found ${orders.length} orders`);
    
    return true;
  } catch (error) {
    console.error('❌ Inventory APIs failed:', error.response?.data?.error || error.message);
    return false;
  }
}

// Test Instacart Market Basket APIs
async function testInstacartAPIs() {
  console.log('\n🛒 TESTING INSTACART APIs');
  console.log('='.repeat(50));
  
  try {
    // Test Instacart products
    const productsResponse = await authRequest('get', '/instacart/products?limit=5');
    const products = productsResponse.data.products || productsResponse.data;
    console.log(`✅ Instacart Products: Found ${products.length} products`);
    
    // Test Instacart orders
    const ordersResponse = await authRequest('get', '/instacart/orders?limit=5');
    const orders = ordersResponse.data.orders || ordersResponse.data;
    console.log(`✅ Instacart Orders: Found ${orders.length} orders`);
    
    // Test market basket analysis
    const analysisResponse = await authRequest('get', '/instacart/analysis/market-basket?limit=5');
    console.log(`✅ Market Basket Analysis: Available`);
    
    return true;
  } catch (error) {
    console.error('❌ Instacart APIs failed:', error.response?.data?.error || error.message);
    return false;
  }
}

// Test Amazon Reviews APIs
async function testAmazonReviewsAPIs() {
  console.log('\n⭐ TESTING AMAZON REVIEWS APIs');
  console.log('='.repeat(50));
  
  try {
    // Test status endpoint
    const statusResponse = await authRequest('get', '/amazon-reviews/status');
    const status = statusResponse.data;
    console.log(`✅ Amazon Reviews Status: ${status.reviews.total} reviews, ${status.products.total} products`);
    
    // Test reviews endpoint
    const reviewsResponse = await authRequest('get', '/amazon-reviews/reviews?limit=5');
    const reviews = reviewsResponse.data.reviews || reviewsResponse.data;
    console.log(`✅ Reviews API: Found ${reviews.length} reviews`);
    
    // Test products endpoint
    const productsResponse = await authRequest('get', '/amazon-reviews/products?limit=5');
    const products = productsResponse.data.products || productsResponse.data;
    console.log(`✅ Amazon Products API: Found ${products.length} products`);
    
    // Test analytics dashboard
    const analyticsResponse = await authRequest('get', '/amazon-reviews/analytics/dashboard');
    console.log(`✅ Analytics Dashboard: Available`);
    
    return true;
  } catch (error) {
    console.error('❌ Amazon Reviews APIs failed:', error.response?.data?.error || error.message);
    return false;
  }
}

// Test Predictive Analytics APIs
async function testPredictiveAPIs() {
  console.log('\n🔮 TESTING PREDICTIVE ANALYTICS APIs');
  console.log('='.repeat(50));
  
  try {
    // Test dashboard summary
    const summaryResponse = await authRequest('get', '/predictive/dashboard/summary');
    console.log(`✅ Predictive Dashboard Summary: Available`);
    
    // Test comprehensive dashboard
    const comprehensiveResponse = await authRequest('get', '/predictive/dashboard/comprehensive');
    console.log(`✅ Comprehensive Dashboard: Available`);
    
    // Test forecasts endpoint
    const forecastsResponse = await authRequest('get', '/predictive/forecasts?limit=5');
    const forecasts = forecastsResponse.data.forecasts || forecastsResponse.data;
    console.log(`✅ Forecasts API: Found ${forecasts.length} forecasts`);
    
    // Test reorder suggestions
    const suggestionsResponse = await authRequest('get', '/predictive/reorder-suggestions?limit=5');
    const suggestions = suggestionsResponse.data.suggestions || suggestionsResponse.data;
    console.log(`✅ Reorder Suggestions: Found ${suggestions.length} suggestions`);
    
    return true;
  } catch (error) {
    console.error('❌ Predictive Analytics APIs failed:', error.response?.data?.error || error.message);
    return false;
  }
}

// Test Chatbot APIs
async function testChatbotAPIs() {
  console.log('\n🤖 TESTING CHATBOT APIs');
  console.log('='.repeat(50));
  
  try {
    // Test Gemini connection
    const geminiResponse = await authRequest('get', '/chatbot/test-gemini');
    console.log(`✅ Gemini AI Connection: Working`);
    
    // Test chatbot query
    const queryResponse = await authRequest('post', '/chatbot/query', {
      message: 'How many products do we have in inventory?',
      context: 'inventory'
    });
    console.log(`✅ Chatbot Query: Response received`);
    
    // Test enhanced chatbot
    const enhancedResponse = await authRequest('post', '/chatbot/enhanced-query', {
      message: 'Show me low stock items',
      includeDatabase: true,
      includeGemini: true
    });
    console.log(`✅ Enhanced Chatbot: Working`);
    
    return true;
  } catch (error) {
    console.error('❌ Chatbot APIs failed:', error.response?.data?.error || error.message);
    return false;
  }
}

// Test Dashboard APIs
async function testDashboardAPIs() {
  console.log('\n📊 TESTING DASHBOARD APIs');
  console.log('='.repeat(50));
  
  try {
    // Test main dashboard stats
    const statsResponse = await authRequest('get', '/dashboard/stats');
    console.log(`✅ Dashboard Stats: Available`);
    
    // Test reports
    const reportsResponse = await authRequest('get', '/reports/inventory-summary');
    console.log(`✅ Reports API: Available`);
    
    return true;
  } catch (error) {
    console.error('❌ Dashboard APIs failed:', error.response?.data?.error || error.message);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 COMPREHENSIVE API TESTING STARTED');
  console.log('='.repeat(60));
  
  const results = {
    authentication: false,
    inventory: false,
    instacart: false,
    amazonReviews: false,
    predictive: false,
    chatbot: false,
    dashboard: false
  };
  
  // Run all tests
  results.authentication = await testAuthentication();
  
  if (results.authentication) {
    results.inventory = await testInventoryAPIs();
    results.instacart = await testInstacartAPIs();
    results.amazonReviews = await testAmazonReviewsAPIs();
    results.predictive = await testPredictiveAPIs();
    results.chatbot = await testChatbotAPIs();
    results.dashboard = await testDashboardAPIs();
  }
  
  // Summary
  console.log('\n📋 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test.toUpperCase()}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  console.log(`\n🎯 OVERALL SCORE: ${passed}/${total} tests passed (${Math.round(passed/total*100)}%)`);
  
  if (passed === total) {
    console.log('\n🎉 ALL SYSTEMS OPERATIONAL!');
    console.log('Your inventory management system is fully functional.');
  } else {
    console.log('\n⚠️ SOME ISSUES DETECTED');
    console.log('Please check the failed tests above and ensure:');
    console.log('- Backend server is running (npm run dev in backend folder)');
    console.log('- MongoDB is connected');
    console.log('- All dependencies are installed');
    console.log('- Data has been seeded (npm run seed in backend folder)');
  }
  
  return results;
}

// Run the tests
runAllTests().catch(console.error);
