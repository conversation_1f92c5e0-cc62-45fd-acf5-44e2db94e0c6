const express = require('express');
const router = express.Router();
const AmazonReview = require('../models/AmazonReview');
const AmazonProduct = require('../models/AmazonProduct');
const AmazonReviewsService = require('../services/amazonReviewsService');
const { auth } = require('../middleware/auth');

const reviewsService = new AmazonReviewsService();

// Import Amazon Fine Food Reviews from CSV
router.post('/import', auth, async (req, res) => {
  try {
    const { csvFileName } = req.body;
    
    if (!csvFileName) {
      return res.status(400).json({
        success: false,
        error: 'CSV file name is required'
      });
    }

    console.log(`Starting import of Amazon reviews from: ${csvFileName}`);
    
    const result = await reviewsService.importReviewsFromCSV(csvFileName);
    
    res.json({
      success: true,
      message: 'Amazon Fine Food Reviews imported successfully',
      result
    });
  } catch (error) {
    console.error('Error importing Amazon reviews:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Process text for reviews (sentiment analysis, keyword extraction)
router.post('/process-text', auth, async (req, res) => {
  try {
    const { limit = 1000 } = req.body;
    
    console.log(`Starting text processing for ${limit} reviews...`);
    
    const result = await reviewsService.processReviewsText(limit);
    
    res.json({
      success: true,
      message: 'Text processing completed',
      result
    });
  } catch (error) {
    console.error('Error processing review text:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Generate product insights from reviews
router.post('/generate-insights/:productId', auth, async (req, res) => {
  try {
    const { productId } = req.params;
    
    console.log(`Generating insights for product: ${productId}`);
    
    const insights = await reviewsService.generateProductInsights(productId);
    
    if (!insights) {
      return res.status(404).json({
        success: false,
        error: 'No reviews found for this product'
      });
    }
    
    res.json({
      success: true,
      message: 'Product insights generated successfully',
      insights
    });
  } catch (error) {
    console.error('Error generating product insights:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get reviews with filtering and pagination
router.get('/reviews', auth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      product_id,
      user_id,
      score,
      sentiment,
      sortBy = 'time',
      sortOrder = 'desc'
    } = req.query;

    const query = {};
    if (product_id) query.product_id = product_id;
    if (user_id) query.user_id = user_id;
    if (score) query.score = parseInt(score);
    if (sentiment) query.sentiment_label = sentiment;

    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const reviews = await AmazonReview.find(query)
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await AmazonReview.countDocuments(query);

    res.json({
      reviews,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(total / limit),
        total_items: total,
        items_per_page: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get specific review by ID
router.get('/reviews/:reviewId', auth, async (req, res) => {
  try {
    const review = await AmazonReview.findOne({ review_id: req.params.reviewId });
    
    if (!review) {
      return res.status(404).json({ error: 'Review not found' });
    }
    
    res.json(review);
  } catch (error) {
    console.error('Error fetching review:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get reviews for a specific product
router.get('/reviews/product/:productId', auth, async (req, res) => {
  try {
    const { limit = 10, sentiment, sortBy = 'helpfulness_ratio' } = req.query;
    
    let query = { product_id: req.params.productId };
    if (sentiment) query.sentiment_label = sentiment;
    
    const reviews = await AmazonReview.find(query)
      .sort({ [sortBy]: -1, time: -1 })
      .limit(parseInt(limit));
    
    res.json(reviews);
  } catch (error) {
    console.error('Error fetching product reviews:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get reviews by user
router.get('/reviews/user/:userId', auth, async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const reviews = await AmazonReview.getReviewsByUser(req.params.userId, parseInt(limit));
    
    res.json(reviews);
  } catch (error) {
    console.error('Error fetching user reviews:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get sentiment analysis for a product
router.get('/sentiment/:productId', auth, async (req, res) => {
  try {
    const sentimentAnalysis = await AmazonReview.getProductSentimentAnalysis(req.params.productId);
    
    res.json({
      product_id: req.params.productId,
      sentiment_analysis: sentimentAnalysis
    });
  } catch (error) {
    console.error('Error fetching sentiment analysis:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get top keywords for a product
router.get('/keywords/:productId', auth, async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    
    const keywords = await AmazonReview.getTopKeywords(req.params.productId, parseInt(limit));
    
    res.json({
      product_id: req.params.productId,
      keywords
    });
  } catch (error) {
    console.error('Error fetching keywords:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get products with filtering and pagination
router.get('/products', auth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      brand,
      min_rating,
      sentiment,
      sortBy = 'recommendation_score',
      sortOrder = 'desc'
    } = req.query;

    const query = { is_analyzed: true };
    if (category) query.category = new RegExp(category, 'i');
    if (brand) query.brand = new RegExp(brand, 'i');
    if (min_rating) query.average_rating = { $gte: parseFloat(min_rating) };

    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const products = await AmazonProduct.find(query)
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await AmazonProduct.countDocuments(query);

    res.json({
      products,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(total / limit),
        total_items: total,
        items_per_page: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get specific product by ID
router.get('/products/:productId', auth, async (req, res) => {
  try {
    const product = await AmazonProduct.findOne({ product_id: req.params.productId });
    
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    res.json(product);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: error.message });
  }
});

// Search products
router.get('/products/search/:query', auth, async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const products = await AmazonProduct.searchProducts(req.params.query, parseInt(limit));
    
    res.json(products);
  } catch (error) {
    console.error('Error searching products:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get top-rated products
router.get('/products/top-rated', auth, async (req, res) => {
  try {
    const { limit = 10, min_reviews = 5 } = req.query;
    
    const products = await AmazonProduct.getTopRatedProducts(parseInt(limit), parseInt(min_reviews));
    
    res.json(products);
  } catch (error) {
    console.error('Error fetching top-rated products:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get products by category
router.get('/products/category/:category', auth, async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const products = await AmazonProduct.getProductsByCategory(req.params.category, parseInt(limit));
    
    res.json(products);
  } catch (error) {
    console.error('Error fetching products by category:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get product recommendations
router.get('/products/:productId/recommendations', auth, async (req, res) => {
  try {
    const { limit = 5 } = req.query;
    
    const recommendations = await AmazonProduct.getRecommendations(req.params.productId, parseInt(limit));
    
    res.json(recommendations);
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get analytics dashboard data
router.get('/analytics/dashboard', auth, async (req, res) => {
  try {
    const [
      totalReviews,
      totalProducts,
      avgRating,
      sentimentDistribution,
      topCategories,
      recentReviews
    ] = await Promise.all([
      AmazonReview.countDocuments(),
      AmazonProduct.countDocuments({ is_analyzed: true }),
      AmazonReview.aggregate([
        { $group: { _id: null, avgRating: { $avg: '$score' } } }
      ]),
      AmazonReview.aggregate([
        {
          $group: {
            _id: '$sentiment_label',
            count: { $sum: 1 }
          }
        }
      ]),
      AmazonProduct.aggregate([
        { $match: { is_analyzed: true } },
        {
          $group: {
            _id: '$category',
            count: { $sum: 1 },
            avg_rating: { $avg: '$average_rating' }
          }
        },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),
      AmazonReview.find()
        .sort({ time: -1 })
        .limit(10)
        .select('review_id product_id score sentiment_label time summary')
    ]);

    res.json({
      summary: {
        total_reviews: totalReviews,
        total_products: totalProducts,
        average_rating: avgRating[0]?.avgRating || 0
      },
      sentiment_distribution: sentimentDistribution,
      top_categories: topCategories,
      recent_reviews: recentReviews,
      last_updated: new Date()
    });
  } catch (error) {
    console.error('Error fetching analytics dashboard:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get processing status
router.get('/status', auth, async (req, res) => {
  try {
    const [
      totalReviews,
      processedReviews,
      totalProducts,
      analyzedProducts
    ] = await Promise.all([
      AmazonReview.countDocuments(),
      AmazonReview.countDocuments({ is_processed: true }),
      AmazonProduct.countDocuments(),
      AmazonProduct.countDocuments({ is_analyzed: true })
    ]);

    const processingStats = reviewsService.getProcessingStats();

    res.json({
      reviews: {
        total: totalReviews,
        processed: processedReviews,
        processing_percentage: totalReviews > 0 ? Math.round((processedReviews / totalReviews) * 100) : 0
      },
      products: {
        total: totalProducts,
        analyzed: analyzedProducts,
        analysis_percentage: totalProducts > 0 ? Math.round((analyzedProducts / totalProducts) * 100) : 0
      },
      processing_stats: processingStats,
      last_updated: new Date()
    });
  } catch (error) {
    console.error('Error fetching processing status:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;