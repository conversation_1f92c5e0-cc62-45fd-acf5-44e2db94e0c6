# Dataset folder - exclude from version control
dataset/
datasets/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Node modules (if any)
node_modules/

# Temporary files
*.tmp
*.temp

# Large data files
*.csv
*.json
*.parquet
*.pkl
*.pickle

# Model files
*.model
*.h5
*.pkl
models/

# Cache directories
.cache/
.pytest_cache/
