<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css" />
	<!-- <link rel="stylesheet" href="css/style.css"/> -->
    <style>
      #productHoney {
        display: none;
      }
      #product2 {
        display: none;
      }
      #product3{
        display: none;
      }
      #product4 {
        display: none;
      }
      #product5{
        display: none;
      }
      #productAlmond{
        display: none;
      }
      .btn:hover {
      background-color: black;
    }

    img {
    vertical-align: middle;
    height: 200px;
}
    </style>
	
  </head>
  <body>
    
    <style>
      body {
        background-image: url("images/insta1.png");
        background-repeat: no-repeat;
        font-family: cursive;
        padding-left: 400px;
        width: -webkit-fill-available;
        height: auto;
        padding: inherit;
      }
    </style>



    <div class="dropdown" style="font-family: cursive">
      <button
        class="btn btn-secondary dropdown-toggle"
        type="button"
        id="dropdownMenuButton1"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        style="background-color: #43b02a"
      >
        Select Product You Want To Buy
      </button>
      <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">

        <li >
          <a class="dropdown-item"  onclick="Almond()">
            Unsweetened Original Almond Breeze Almond Milk</a
          >
        </li>

        <li >
          <a class="dropdown-item" onclick="Honey()">
            Honey Bunches Of Oats Roasted Cereal</a 
            >
       </li>

        <li >
          <a class="dropdown-item" onclick="Dentastix()">
            Dentastix Fresh, Large</a
          >
        </li>

        <li >
          <a class="dropdown-item" onclick="Tissue()"> 
          Bathroom Tissue Double Rolls</a>
        </li>

        <li >
          <a class="dropdown-item" onclick="Soap()">
            Sensitive Skin Moisturizing Cream Soap Bars</a
          >
        </li>

        <li >
          <a class="dropdown-item" onclick="Sausage()">
           Regular Pork Sausage Tube</a>
        </li>
        
      </ul>
    </div>



    <div class="container">

<!-- start productAlmond -->
      <div class="row mb-2 white">
        <div id="productAlmond">
          <div class="row">
            <div class="col-12 smart_title rounded-top">
              <p class="float-start">
               <strong style=" font-size: x-large;"> people who buy this also buy: </strong>
              </p>
            </div>
          </div>
          <div class="row" style="margin-left: 100px;">
          <div class="col-lg-3 rounded p-2">
            <img
              src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTjtjyDmMzeyhumZfpe-iXtP1JcZyg-MagGObdFXFcP13m8Ix7Gd6Q7Uqz-f3p_tvt3WVk&usqp=CAU"
              alt="header image"
              class="rounded-3 header_img" 
            />
            <p> Banana </p>
          </div>
          <div class="col-lg-3 rounded p-2">
            <img
              src="https://m.media-amazon.com/images/I/81c2j4kWugL._AC_UL480_FMwebp_QL65_.jpg"
              alt="header image"
              class="rounded-3 header_img" width="100%"
            />
            <p> Large Lemon</p>
          </div>
          <div class="col-lg-3 rounded p-2">
            <img
              src="https://learnenglishteens.britishcouncil.org/sites/teens/files/styles/article/public/rs7776_thinkstockphotos-856586464_1-low.jpg?itok=zHdfQ6Ij"
               alt="header image" class=" rounded-3  header_img" width="100%"
            />
            <p> Organic Avocado</p>
          </div>

          <div class="col-lg-3 rounded p-2">
            <img
              src="https://m.media-amazon.com/images/I/61dezrJv2TL._AC_UL480_FMwebp_QL65_.jpg"
              alt="header image"
              class="rounded-3 header_img" 
            />
            <p>Organic Baby spinch</p>
          </div>
        </div>
        </div>
      </div>
<!-- end  productAlmond -->

<!-- start productHoney -->
      <div class="row mb-2 white">
        <div id="productHoney">
          <div class="row">
            <div class="col-12 smart_title rounded-top">
              <p class="float-start">
                <strong style=" font-size: x-large;">people who buy this also buy: </strong>
              </p>
            </div>
          </div>
          <div class="row">
          <div class="col-lg-3 rounded p-2">
             <img src="https://learnenglishteens.britishcouncil.org/sites/teens/files/styles/article/public/rs7776_thinkstockphotos-856586464_1-low.jpg?itok=zHdfQ6Ij" alt="header image"
        class=" rounded-3  header_img" width="100%">
      <p> Avocado</p>
          </div>
          <div class="col-lg-3 rounded p-2">
            <img
              src="https://m.media-amazon.com/images/I/61dezrJv2TL._AC_UL480_FMwebp_QL65_.jpg"
              alt="header image"
              class="rounded-3 header_img" 
            />
            <p>Organic Baby spinch</p>
          </div>
          


          <div class="col-lg-3 rounded p-2">
            <img src="https://m.media-amazon.com/images/I/81Reo5stUJL._SX679_PIbundle-12,TopRight,0,0_AA679SH20_.jpg"
        alt="header image" class=" rounded-3  header_img" width="100%">
          <p> Unsweetened Original Almond Breeze Almond Milk</p>
         </div>

          </div>
        </div>
        </div>
      </div>
<!-- end productHoney-->




    </div>

    <button
      type="submit"
      class="btn btn-primary"
      style="
        margin-bottom: 20px;

        font-family: cursive;
      "
    >
      <a href="model.html" target="_blank" style="color: white">Back</a>
    </button>

     

    <script>

      function Honey() {
        document.getElementById("productHoney").style.display = "block";
        console.log("mmm");
      }  


      function Dentastix() {
        document.getElementById("product2").style.display = "block";
        console.log("mmm");
      } 

      function Tissue() {
        document.getElementById("product3").style.display = "block";
        console.log("mmm");
      }

      function Soap() {
        document.getElementById("product4").style.display = "block";
        console.log("mmm");
      }

      function Sausage() {
        document.getElementById("product5").style.display = "block";
        console.log("mmm");
      }

      function Almond() {
        document.getElementById("productAlmond").style.display = "block";
        console.log("mmm");
      }
    </script>

    <!-- <script type="text/javascript" src="js/bootstrap.min.js"></script> -->
    <!-- <script type="text/javascript" src="js/popper.min.js"></script>
 -->
    <!-- <script type="text/javascript" src="js/bootstrap.bundle.min.js"></script> -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
      crossorigin="anonymous"
    ></script>
    <script type="text/javascript" src="js/javascript.js"></script>
  </body>
</html>
