import React, { useState, useEffect } from 'react';
import { 
  Star, 
  TrendingUp, 
  MessageSquare, 
  BarChart3,
  RefreshCw,
  Upload,
  Search,
  Filter,
  Eye
} from 'lucide-react';
import { amazonReviewsAPI } from '../../services/api';
import ReviewsChart from './ReviewsChart';
import SentimentAnalysis from './SentimentAnalysis';
import ProductInsights from './ProductInsights';
import toast from 'react-hot-toast';

const AmazonReviewsDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [processingStatus, setProcessingStatus] = useState(null);

  useEffect(() => {
    fetchDashboardData();
    fetchProcessingStatus();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await amazonReviewsAPI.getDashboardAnalytics();
      setDashboardData(response.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const fetchProcessingStatus = async () => {
    try {
      const response = await amazonReviewsAPI.getProcessingStatus();
      setProcessingStatus(response.data);
    } catch (error) {
      console.error('Error fetching processing status:', error);
    }
  };

  const handleImportReviews = async () => {
    try {
      const csvFileName = prompt('Enter CSV file name (e.g., Reviews.csv):');
      if (!csvFileName) return;

      toast.loading('Importing Amazon reviews...', { id: 'import-reviews' });
      
      const response = await amazonReviewsAPI.importReviews({ csvFileName });
      
      toast.success(`Imported ${response.data.result.totalProcessed} reviews`, {
        id: 'import-reviews'
      });
      
      await fetchDashboardData();
      await fetchProcessingStatus();
    } catch (error) {
      console.error('Error importing reviews:', error);
      toast.error('Failed to import reviews', { id: 'import-reviews' });
    }
  };

  const handleProcessText = async () => {
    try {
      toast.loading('Processing review text...', { id: 'process-text' });
      
      const response = await amazonReviewsAPI.processText({ limit: 1000 });
      
      toast.success(`Processed ${response.data.result.processedCount} reviews`, {
        id: 'process-text'
      });
      
      await fetchDashboardData();
      await fetchProcessingStatus();
    } catch (error) {
      console.error('Error processing text:', error);
      toast.error('Failed to process text', { id: 'process-text' });
    }
  };

  const StatCard = ({ title, value, icon: Icon, color, subtitle, trend }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
      {trend && (
        <div className="mt-4 flex items-center">
          <span className="text-sm text-gray-500">{trend}</span>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Star className="h-8 w-8 mr-3 text-yellow-500" />
            Amazon Fine Food Reviews Analysis
          </h1>
          <p className="text-gray-600 mt-1">
            Comprehensive analysis of customer reviews and product insights
          </p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={handleImportReviews}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <Upload className="h-4 w-4 mr-2" />
            Import Reviews
          </button>
          
          <button
            onClick={handleProcessText}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Process Text
          </button>
          
          <button
            onClick={fetchDashboardData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Processing Status */}
      {processingStatus && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-2">Processing Status</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-blue-600">Reviews:</span> {processingStatus.reviews.processed}/{processingStatus.reviews.total} processed ({processingStatus.reviews.processing_percentage}%)
            </div>
            <div>
              <span className="text-blue-600">Products:</span> {processingStatus.products.analyzed}/{processingStatus.products.total} analyzed ({processingStatus.products.analysis_percentage}%)
            </div>
          </div>
        </div>
      )}

      {/* Summary Cards */}
      {dashboardData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <StatCard
            title="Total Reviews"
            value={dashboardData.summary?.total_reviews?.toLocaleString() || 0}
            icon={MessageSquare}
            color="bg-blue-100 text-blue-600"
            subtitle="Customer reviews analyzed"
          />
          <StatCard
            title="Products Analyzed"
            value={dashboardData.summary?.total_products?.toLocaleString() || 0}
            icon={Star}
            color="bg-green-100 text-green-600"
            subtitle="Products with insights"
          />
          <StatCard
            title="Average Rating"
            value={dashboardData.summary?.average_rating?.toFixed(1) || '0.0'}
            icon={TrendingUp}
            color="bg-yellow-100 text-yellow-600"
            subtitle="Overall customer satisfaction"
          />
          <StatCard
            title="Sentiment Score"
            value={`${Math.round((dashboardData.sentiment_distribution?.find(s => s._id === 'positive')?.count || 0) / (dashboardData.summary?.total_reviews || 1) * 100)}%`}
            icon={BarChart3}
            color="bg-purple-100 text-purple-600"
            subtitle="Positive sentiment"
          />
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: BarChart3 },
            { id: 'sentiment', name: 'Sentiment Analysis', icon: TrendingUp },
            { id: 'products', name: 'Product Insights', icon: Star },
            { id: 'reviews', name: 'Recent Reviews', icon: MessageSquare }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && dashboardData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ReviewsChart data={dashboardData} />
            <SentimentAnalysis data={dashboardData.sentiment_distribution} />
          </div>
        )}

        {activeTab === 'sentiment' && dashboardData && (
          <SentimentAnalysis 
            data={dashboardData.sentiment_distribution} 
            detailed={true}
          />
        )}

        {activeTab === 'products' && (
          <ProductInsights />
        )}

        {activeTab === 'reviews' && dashboardData && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Reviews</h3>
            <div className="space-y-4">
              {dashboardData.recent_reviews?.map((review, index) => (
                <div key={review.review_id} className="border-b border-gray-200 pb-4 last:border-b-0">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${i < review.score ? 'fill-current' : ''}`}
                          />
                        ))}
                      </div>
                      <span className="ml-2 text-sm text-gray-600">
                        {new Date(review.time).toLocaleDateString()}
                      </span>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      review.sentiment_label === 'positive' ? 'bg-green-100 text-green-800' :
                      review.sentiment_label === 'negative' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {review.sentiment_label}
                    </span>
                  </div>
                  <p className="text-sm font-medium text-gray-900 mb-1">{review.summary}</p>
                  <p className="text-sm text-gray-600">Product ID: {review.product_id}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Last Updated */}
      {dashboardData && (
        <div className="text-center text-sm text-gray-500">
          Last updated: {new Date(dashboardData.last_updated).toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default AmazonReviewsDashboard;