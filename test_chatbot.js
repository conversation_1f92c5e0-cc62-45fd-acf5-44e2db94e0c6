const axios = require('axios');

// Test the chatbot API
async function testChatbot() {
  const baseURL = 'http://localhost:5000/api';
  let authToken = null;

  console.log('Testing chatbot API...\n');

  // First, try to login or register a test user
  try {
    console.log('Attempting to login with test user...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    authToken = loginResponse.data.token;
    console.log('Login successful!');
    console.log('\n---\n');

  } catch (loginError) {
    console.log('<PERSON><PERSON> failed, trying to register test user...');

    try {
      const registerResponse = await axios.post(`${baseURL}/auth/register`, {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'admin'
      });

      authToken = registerResponse.data.token;
      console.log('Registration successful!');
      console.log('\n---\n');

    } catch (registerError) {
      console.log('Registration failed:', registerError.response?.data || registerError.message);
      console.log('Proceeding without authentication...\n');
    }
  }

  const headers = {
    'Content-Type': 'application/json',
    ...(authToken && { 'Authorization': `Bearer ${authToken}` })
  };

  try {
    // Test 1: Check inventory levels for low stock items
    console.log('Test 1: Check inventory levels for low stock items');
    const response1 = await axios.post(`${baseURL}/chatbot/query`, {
      query: 'Check inventory levels for low stock items',
      session_id: 'test_session_1'
    }, { headers });

    console.log('Response:', JSON.stringify(response1.data, null, 2));
    console.log('\n---\n');

  } catch (error) {
    console.log('Error:', error.response?.data || error.message);
    console.log('\n---\n');
  }
  
  try {
    // Test 2: Simple "check" query
    console.log('Test 2: Simple "check" query');
    const response2 = await axios.post(`${baseURL}/chatbot/query`, {
      query: 'check',
      session_id: 'test_session_2'
    }, { headers });

    console.log('Response:', JSON.stringify(response2.data, null, 2));
    console.log('\n---\n');

  } catch (error) {
    console.log('Error:', error.response?.data || error.message);
    console.log('\n---\n');
  }

  try {
    // Test 3: What are the reviews for organic products?
    console.log('Test 3: What are the reviews for organic products?');
    const response3 = await axios.post(`${baseURL}/chatbot/query`, {
      query: 'What are the reviews for organic products?',
      session_id: 'test_session_3'
    }, { headers });

    console.log('Response:', JSON.stringify(response3.data, null, 2));
    console.log('\n---\n');

  } catch (error) {
    console.log('Error:', error.response?.data || error.message);
    console.log('\n---\n');
  }

  try {
    // Test 4: Test Gemini connection
    console.log('Test 4: Test Gemini connection');
    const response4 = await axios.get(`${baseURL}/chatbot/test-gemini`, { headers });

    console.log('Gemini Status:', response4.data);
    console.log('\n---\n');

  } catch (error) {
    console.log('Error:', error.response?.data || error.message);
    console.log('\n---\n');
  }
  
  try {
    // Test 5: Health check
    console.log('Test 5: Health check');
    const response5 = await axios.get(`${baseURL}/chatbot/health`);

    console.log('Health Status:', response5.data);
    console.log('\n---\n');

  } catch (error) {
    console.log('Error:', error.response?.data || error.message);
    console.log('\n---\n');
  }
}

// Run the test
testChatbot().catch(console.error);
