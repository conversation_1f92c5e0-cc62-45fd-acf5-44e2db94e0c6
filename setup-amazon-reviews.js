/**
 * Setup script for Amazon Fine Food Reviews dataset
 * This script imports and processes the Amazon reviews data
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:5000/api';
let authToken = null;

// Login to get auth token
async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    authToken = response.data.token;
    console.log('✅ Login successful');
    return true;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.error || error.message);
    return false;
  }
}

// Check if Amazon reviews data exists
async function checkAmazonData() {
  try {
    console.log('📊 Checking Amazon reviews data...');
    const response = await axios.get(`${API_BASE}/amazon-reviews/status`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    const { reviews, products } = response.data;
    console.log(`📈 Current status:`);
    console.log(`   Reviews: ${reviews.total} total, ${reviews.processed} processed`);
    console.log(`   Products: ${products.total} total, ${products.analyzed} analyzed`);
    
    return {
      hasReviews: reviews.total > 0,
      hasProducts: products.total > 0,
      needsProcessing: reviews.processed < reviews.total || products.analyzed < products.total
    };
  } catch (error) {
    console.error('❌ Failed to check Amazon data:', error.response?.data?.error || error.message);
    return { hasReviews: false, hasProducts: false, needsProcessing: false };
  }
}

// Import Amazon reviews from CSV
async function importAmazonReviews() {
  try {
    console.log('📥 Importing Amazon Fine Food Reviews...');
    
    // Check if sample file exists
    const sampleFile = path.join(__dirname, 'amazonFinefoodReview', 'sample_reviews.csv');
    const cleanedFile = path.join(__dirname, 'amazonFinefoodReview', 'cleaned_amazon_fine_food_reviews.csv');
    
    let csvFileName;
    if (fs.existsSync(sampleFile)) {
      csvFileName = 'sample_reviews.csv';
      console.log('📄 Using sample reviews file');
    } else if (fs.existsSync(cleanedFile)) {
      csvFileName = 'cleaned_amazon_fine_food_reviews.csv';
      console.log('📄 Using cleaned reviews file');
    } else {
      console.error('❌ No Amazon reviews CSV file found');
      return false;
    }
    
    const response = await axios.post(`${API_BASE}/amazon-reviews/import`, {
      csvFileName: csvFileName
    }, {
      headers: { Authorization: `Bearer ${authToken}` },
      timeout: 60000 // 60 seconds timeout for large imports
    });
    
    console.log('✅ Amazon reviews imported successfully');
    console.log(`📊 Result:`, response.data.result);
    return true;
  } catch (error) {
    console.error('❌ Failed to import Amazon reviews:', error.response?.data?.error || error.message);
    return false;
  }
}

// Process review text for sentiment analysis
async function processReviewText() {
  try {
    console.log('🔄 Processing review text for sentiment analysis...');
    
    const response = await axios.post(`${API_BASE}/amazon-reviews/process-text`, {
      limit: 1000 // Process first 1000 reviews
    }, {
      headers: { Authorization: `Bearer ${authToken}` },
      timeout: 120000 // 2 minutes timeout
    });
    
    console.log('✅ Review text processing completed');
    console.log(`📊 Result:`, response.data.result);
    return true;
  } catch (error) {
    console.error('❌ Failed to process review text:', error.response?.data?.error || error.message);
    return false;
  }
}

// Get sample data to verify setup
async function getSampleData() {
  try {
    console.log('📋 Fetching sample data...');
    
    // Get sample reviews
    const reviewsResponse = await axios.get(`${API_BASE}/amazon-reviews/reviews?limit=5`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    // Get sample products
    const productsResponse = await axios.get(`${API_BASE}/amazon-reviews/products?limit=5`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    // Get analytics dashboard
    const analyticsResponse = await axios.get(`${API_BASE}/amazon-reviews/analytics/dashboard`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Sample data retrieved successfully');
    console.log('📊 Analytics Summary:', analyticsResponse.data.summary);
    console.log('📝 Sample Reviews:', reviewsResponse.data.reviews?.length || 0);
    console.log('🛍️ Sample Products:', productsResponse.data.products?.length || 0);
    
    return true;
  } catch (error) {
    console.error('❌ Failed to get sample data:', error.response?.data?.error || error.message);
    return false;
  }
}

// Main setup function
async function setupAmazonReviews() {
  console.log('🚀 Setting up Amazon Fine Food Reviews Dataset...\n');
  
  // Step 1: Login
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.error('❌ Setup failed: Could not login');
    return;
  }
  
  // Step 2: Check current data status
  const dataStatus = await checkAmazonData();
  
  // Step 3: Import data if needed
  if (!dataStatus.hasReviews) {
    console.log('\n📥 No reviews found, importing...');
    const importSuccess = await importAmazonReviews();
    if (!importSuccess) {
      console.error('❌ Setup failed: Could not import reviews');
      return;
    }
  } else {
    console.log('✅ Reviews already imported');
  }
  
  // Step 4: Process text if needed
  if (dataStatus.needsProcessing) {
    console.log('\n🔄 Processing review text...');
    await processReviewText();
  } else {
    console.log('✅ Reviews already processed');
  }
  
  // Step 5: Verify setup with sample data
  console.log('\n📋 Verifying setup...');
  await getSampleData();
  
  console.log('\n🎉 Amazon Fine Food Reviews setup completed!');
  console.log('\n📱 You can now:');
  console.log('   • View reviews in the Reports section');
  console.log('   • Use the chatbot to query Amazon reviews data');
  console.log('   • Analyze sentiment and product insights');
  console.log('   • Get product recommendations based on reviews');
}

// Run setup
setupAmazonReviews().catch(console.error);
